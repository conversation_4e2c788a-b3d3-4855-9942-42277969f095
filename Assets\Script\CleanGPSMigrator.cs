using UnityEngine;

/// <summary>
/// Outil de migration vers le nouveau système GPS propre
/// Supprime tout le bordel et installe le système propre
/// </summary>
public class CleanGPSMigrator : MonoBehaviour
{
    [Header("Migration")]
    [SerializeField] private bool autoMigrateOnStart = true;
    [SerializeField] private bool showMigrationLogs = true;
    [SerializeField] private bool removeOldComponents = true;

    private void Start()
    {
        if (autoMigrateOnStart)
        {
            Invoke(nameof(MigrateToCleanSystem), 1f);
        }
    }

    /// <summary>
    /// Migre complètement vers le nouveau système propre
    /// </summary>
    [ContextMenu("Migrer vers Système Propre")]
    public void MigrateToCleanSystem()
    {
        LogMigration("🔄 === MIGRATION VERS SYSTÈME GPS PROPRE ===");

        // 1. <PERSON><PERSON><PERSON> le gestionnaire central s'il n'existe pas
        CreateCleanGPSManager();

        // 2. Migrer tous les bateaux
        MigrateAllBoats();

        // 3. Configurer l'adaptateur Meshtastic
        SetupMeshtasticAdapter();

        // 4. Nettoyer les anciens composants
        if (removeOldComponents)
        {
            CleanupOldComponents();
        }

        LogMigration("✅ === MIGRATION TERMINÉE ===");
        LogMigration("🎯 Système GPS propre installé et opérationnel!");
    }

    /// <summary>
    /// Crée le gestionnaire GPS central
    /// </summary>
    private void CreateCleanGPSManager()
    {
        var existingManager = FindObjectOfType<CleanGPSManager>();
        if (existingManager != null)
        {
            LogMigration("✅ CleanGPSManager existe déjà");
            return;
        }

        GameObject managerObject = new GameObject("CleanGPSManager");
        managerObject.AddComponent<CleanGPSManager>();

        LogMigration("✅ CleanGPSManager créé");
    }

    /// <summary>
    /// Migre tous les bateaux vers le nouveau système
    /// </summary>
    private void MigrateAllBoats()
    {
        LogMigration("🚤 === MIGRATION DES BATEAUX ===");

        // Trouver tous les bateaux avec UnifiedBoatGPS
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();
        LogMigration($"🔍 Trouvé {oldBoats.Length} bateaux avec UnifiedBoatGPS");

        int migratedCount = 0;

        foreach (var oldBoat in oldBoats)
        {
            if (MigrateBoat(oldBoat))
            {
                migratedCount++;
            }
        }

        LogMigration($"✅ Migration des bateaux terminée: {migratedCount}/{oldBoats.Length}");
    }

    /// <summary>
    /// Migre un bateau spécifique
    /// </summary>
    private bool MigrateBoat(UnifiedBoatGPS oldBoat)
    {
        if (oldBoat == null) return false;

        GameObject boatObject = oldBoat.gameObject;
        string boatName = boatObject.name;

        try
        {
            // Vérifier si CleanGPSSystem existe déjà
            var existingCleanGPS = boatObject.GetComponent<CleanGPSSystem>();
            if (existingCleanGPS != null)
            {
                LogMigration($"⚠️ {boatName} a déjà CleanGPSSystem");
                return false;
            }

            // Sauvegarder la position GPS actuelle
            var currentGPSPos = oldBoat.GetCurrentPosition();

            // Ajouter le nouveau composant
            var newGPS = boatObject.AddComponent<CleanGPSSystem>();

            // Transférer la position GPS si valide
            if (currentGPSPos.x != 0 && currentGPSPos.y != 0)
            {
                // Attendre un frame pour l'initialisation
                StartCoroutine(TransferGPSPosition(newGPS, currentGPSPos));
            }

            // Désactiver l'ancien composant
            oldBoat.enabled = false;

            LogMigration($"✅ {boatName} migré vers CleanGPSSystem");
            return true;
        }
        catch (System.Exception e)
        {
            LogMigration($"❌ Erreur migration {boatName}: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Transfère la position GPS après initialisation
    /// </summary>
    private System.Collections.IEnumerator TransferGPSPosition(CleanGPSSystem newGPS, Mapbox.Utils.Vector2d gpsPosition)
    {
        yield return new WaitForSeconds(0.2f);

        if (newGPS != null)
        {
            newGPS.UpdateGPSPosition(gpsPosition);
            LogMigration($"📍 Position GPS transférée: {gpsPosition}");
        }
    }

    /// <summary>
    /// Configure l'adaptateur Meshtastic
    /// </summary>
    private void SetupMeshtasticAdapter()
    {
        var meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticTracker == null)
        {
            LogMigration("⚠️ MeshtasticGPSTracker non trouvé");
            return;
        }

        // Vérifier si l'adaptateur existe déjà
        var existingAdapter = meshtasticTracker.GetComponent<CleanMeshtasticAdapter>();
        if (existingAdapter != null)
        {
            LogMigration("✅ CleanMeshtasticAdapter existe déjà");
            return;
        }

        // Ajouter l'adaptateur
        var adapter = meshtasticTracker.gameObject.AddComponent<CleanMeshtasticAdapter>();

        LogMigration("✅ CleanMeshtasticAdapter ajouté");
    }

    /// <summary>
    /// Nettoie les anciens composants
    /// </summary>
    private void CleanupOldComponents()
    {
        LogMigration("🧹 === NETTOYAGE DES ANCIENS COMPOSANTS ===");

        int cleanedCount = 0;

        // Supprimer les UnifiedBoatGPS désactivés (avec vérification de nullité)
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();
        for (int i = oldBoats.Length - 1; i >= 0; i--) // Parcourir à l'envers pour éviter les problèmes d'index
        {
            var oldBoat = oldBoats[i];
            if (oldBoat != null && !oldBoat.enabled)
            {
                string boatName = oldBoat.gameObject != null ? oldBoat.gameObject.name : "Unknown";
                try
                {
                    DestroyImmediate(oldBoat);
                    cleanedCount++;
                    LogMigration($"🗑️ UnifiedBoatGPS supprimé de {boatName}");
                }
                catch (System.Exception e)
                {
                    LogMigration($"⚠️ Erreur lors de la suppression de UnifiedBoatGPS de {boatName}: {e.Message}");
                }
            }
        }

        // Supprimer les autres composants obsolètes (avec vérification de nullité)
        var obsoleteTypes = new System.Type[]
        {
            typeof(BoatGPSManager),
            typeof(GPSRigidbodyFixer),
            typeof(TeleportationConflictResolver),
            typeof(ImprovedTeleportationManager),
            typeof(DirectGPSTeleporter),
            typeof(BoatInitializer),
            typeof(InitializationTeleporter),
            typeof(TeleportationDiagnostic)
        };

        foreach (var componentType in obsoleteTypes)
        {
            try
            {
                var components = FindObjectsOfType(componentType);
                for (int i = components.Length - 1; i >= 0; i--) // Parcourir à l'envers
                {
                    var component = components[i];
                    if (component != null && component is MonoBehaviour mono)
                    {
                        string objectName = mono.gameObject != null ? mono.gameObject.name : "Unknown";
                        try
                        {
                            DestroyImmediate(mono);
                            cleanedCount++;
                            LogMigration($"🗑️ {componentType.Name} supprimé de {objectName}");
                        }
                        catch (System.Exception e)
                        {
                            LogMigration($"⚠️ Erreur lors de la suppression de {componentType.Name} de {objectName}: {e.Message}");
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                LogMigration($"⚠️ Erreur lors de la recherche de {componentType.Name}: {e.Message}");
            }
        }

        LogMigration($"✅ Nettoyage terminé: {cleanedCount} composants supprimés");
    }

    /// <summary>
    /// Teste le nouveau système
    /// </summary>
    [ContextMenu("Tester Nouveau Système")]
    public void TestNewSystem()
    {
        LogMigration("🧪 === TEST DU NOUVEAU SYSTÈME ===");

        var manager = FindObjectOfType<CleanGPSManager>();
        if (manager == null)
        {
            LogMigration("❌ CleanGPSManager non trouvé");
            return;
        }

        var adapter = FindObjectOfType<CleanMeshtasticAdapter>();
        if (adapter == null)
        {
            LogMigration("❌ CleanMeshtasticAdapter non trouvé");
            return;
        }

        // Test avec données simulées
        adapter.TestSimulatedGPS();

        LogMigration("✅ Test terminé - Vérifiez le mouvement des bateaux");
        LogMigration("🧪 === FIN DU TEST ===");
    }

    /// <summary>
    /// Affiche l'état du système
    /// </summary>
    [ContextMenu("Afficher État du Système")]
    public void ShowSystemStatus()
    {
        LogMigration("📊 === ÉTAT DU SYSTÈME GPS ===");

        var manager = FindObjectOfType<CleanGPSManager>();
        var adapter = FindObjectOfType<CleanMeshtasticAdapter>();
        var cleanBoats = FindObjectsOfType<CleanGPSSystem>();
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();

        LogMigration($"CleanGPSManager: {(manager != null ? "✅" : "❌")}");
        LogMigration($"CleanMeshtasticAdapter: {(adapter != null ? "✅" : "❌")}");
        LogMigration($"Bateaux avec CleanGPSSystem: {cleanBoats.Length}");
        LogMigration($"Bateaux avec UnifiedBoatGPS: {oldBoats.Length}");

        if (manager != null)
        {
            LogMigration($"Bateaux gérés: {manager.GetManagedBoatsCount()}");
        }

        LogMigration("📊 === FIN DE L'ÉTAT ===");
    }

    private void LogMigration(string message)
    {
        if (showMigrationLogs)
        {
            Debug.Log($"[CleanGPSMigrator] {message}");
        }
    }
}
