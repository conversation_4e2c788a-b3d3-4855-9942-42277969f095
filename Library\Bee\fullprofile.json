{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3416, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3416, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3416, "tid": 4028, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3416, "tid": 4028, "ts": 1748363870810169, "dur": 19, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3416, "tid": 4028, "ts": 1748363870810214, "dur": 15, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3416, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3416, "tid": 1, "ts": 1748363867827192, "dur": 2780, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748363867829979, "dur": 97503, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748363867927487, "dur": 92448, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3416, "tid": 4028, "ts": 1748363870810234, "dur": 31, "ph": "X", "name": "", "args": {}}, {"pid": 3416, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867827148, "dur": 13479, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867840630, "dur": 2968393, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867840648, "dur": 60, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867840715, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867840721, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867840985, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867840991, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867841046, "dur": 9, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867841058, "dur": 2832, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867843901, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867843908, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844001, "dur": 6, "ph": "X", "name": "ProcessMessages 1556", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844009, "dur": 37, "ph": "X", "name": "ReadAsync 1556", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844049, "dur": 4, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844057, "dur": 46, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844106, "dur": 2, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844110, "dur": 38, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844152, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844156, "dur": 47, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844210, "dur": 4, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844217, "dur": 49, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844272, "dur": 3, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844278, "dur": 41, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844326, "dur": 4, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844333, "dur": 48, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844388, "dur": 4, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844395, "dur": 48, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844448, "dur": 3, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844454, "dur": 46, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844503, "dur": 2, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844508, "dur": 30, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844540, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844544, "dur": 32, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844579, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844583, "dur": 41, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844627, "dur": 2, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844631, "dur": 35, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844670, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844674, "dur": 35, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844712, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844716, "dur": 38, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844757, "dur": 2, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844761, "dur": 59, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844829, "dur": 5, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844837, "dur": 52, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844893, "dur": 5, "ph": "X", "name": "ProcessMessages 1589", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844901, "dur": 53, "ph": "X", "name": "ReadAsync 1589", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844961, "dur": 4, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867844967, "dur": 44, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845017, "dur": 4, "ph": "X", "name": "ProcessMessages 1258", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845024, "dur": 41, "ph": "X", "name": "ReadAsync 1258", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845068, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845073, "dur": 41, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845117, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845121, "dur": 38, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845163, "dur": 2, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845167, "dur": 32, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845202, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845207, "dur": 34, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845244, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845248, "dur": 40, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845291, "dur": 2, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845295, "dur": 38, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845337, "dur": 2, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845341, "dur": 32, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845376, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845380, "dur": 609, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867845995, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846037, "dur": 2, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846042, "dur": 36, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846081, "dur": 2, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846085, "dur": 38, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846127, "dur": 2, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846131, "dur": 36, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846171, "dur": 2, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846175, "dur": 36, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846214, "dur": 2, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846218, "dur": 46, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846267, "dur": 2, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846272, "dur": 35, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846310, "dur": 2, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846315, "dur": 38, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846357, "dur": 2, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846361, "dur": 38, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846402, "dur": 2, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846406, "dur": 36, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846445, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846449, "dur": 37, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846489, "dur": 2, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846493, "dur": 37, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846534, "dur": 2, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846538, "dur": 36, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846577, "dur": 2, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846581, "dur": 38, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846624, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846628, "dur": 34, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846665, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846669, "dur": 41, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846713, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846718, "dur": 30, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846751, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846755, "dur": 35, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846793, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846797, "dur": 37, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846837, "dur": 2, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846840, "dur": 37, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846881, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846884, "dur": 32, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846920, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846923, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846957, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846960, "dur": 35, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867846998, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847002, "dur": 35, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847040, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847044, "dur": 31, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847078, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847081, "dur": 36, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847120, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847124, "dur": 36, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847163, "dur": 2, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847166, "dur": 38, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847208, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847211, "dur": 38, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847253, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847257, "dur": 35, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847299, "dur": 3, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847304, "dur": 54, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847362, "dur": 3, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847367, "dur": 47, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847417, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847421, "dur": 36, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847460, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847463, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847505, "dur": 2, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847509, "dur": 36, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847548, "dur": 2, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847552, "dur": 37, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847592, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847596, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847632, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847636, "dur": 30, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847669, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847672, "dur": 31, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847706, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847710, "dur": 30, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847743, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847747, "dur": 29, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847779, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847781, "dur": 48, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847833, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847838, "dur": 42, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847885, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847889, "dur": 39, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847931, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847935, "dur": 33, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847972, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867847975, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848013, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848016, "dur": 40, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848061, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848065, "dur": 40, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848108, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848112, "dur": 34, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848150, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848153, "dur": 35, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848192, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848195, "dur": 38, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848237, "dur": 2, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848241, "dur": 38, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848282, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848285, "dur": 36, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848324, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848328, "dur": 35, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848366, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848370, "dur": 35, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848408, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848412, "dur": 38, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848453, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848458, "dur": 33, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848493, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848497, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848535, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848539, "dur": 37, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848578, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848582, "dur": 38, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848623, "dur": 2, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848628, "dur": 41, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848672, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848677, "dur": 37, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848718, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848722, "dur": 41, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848767, "dur": 2, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848771, "dur": 42, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848817, "dur": 2, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848821, "dur": 39, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848863, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848867, "dur": 37, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848908, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848912, "dur": 44, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848960, "dur": 2, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867848964, "dur": 41, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849009, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849014, "dur": 39, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849057, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849061, "dur": 40, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849104, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849108, "dur": 40, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849153, "dur": 4, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849160, "dur": 45, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849209, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849213, "dur": 39, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849255, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849260, "dur": 41, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849304, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849308, "dur": 44, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849358, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849362, "dur": 40, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849406, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849410, "dur": 168, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849585, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849591, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849651, "dur": 4, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849657, "dur": 212, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849874, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849879, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849941, "dur": 5, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867849949, "dur": 54, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850008, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850012, "dur": 46, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850061, "dur": 2, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850066, "dur": 41, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850110, "dur": 2, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850114, "dur": 36, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850154, "dur": 2, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850158, "dur": 94, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850259, "dur": 5, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850267, "dur": 52, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850325, "dur": 4, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850332, "dur": 35, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850371, "dur": 3, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850377, "dur": 34, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850415, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850418, "dur": 46, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850469, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850474, "dur": 45, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850522, "dur": 2, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850526, "dur": 39, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850569, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850573, "dur": 35, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850612, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850615, "dur": 39, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850658, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850662, "dur": 40, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850706, "dur": 2, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850710, "dur": 35, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850748, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850752, "dur": 38, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850793, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850797, "dur": 39, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850839, "dur": 2, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850843, "dur": 39, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850886, "dur": 2, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850890, "dur": 38, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850932, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850935, "dur": 37, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850976, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867850980, "dur": 42, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851026, "dur": 2, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851030, "dur": 34, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851068, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851072, "dur": 40, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851116, "dur": 2, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851120, "dur": 47, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851172, "dur": 4, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851179, "dur": 54, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851237, "dur": 3, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851244, "dur": 52, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851301, "dur": 3, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851308, "dur": 52, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851367, "dur": 4, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851374, "dur": 47, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851429, "dur": 4, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851436, "dur": 51, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851494, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851500, "dur": 55, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851560, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851566, "dur": 51, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851621, "dur": 3, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851628, "dur": 63, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851696, "dur": 4, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851703, "dur": 51, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851759, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851764, "dur": 52, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851821, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851828, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851891, "dur": 4, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851898, "dur": 54, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851957, "dur": 3, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867851964, "dur": 43, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852011, "dur": 2, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852016, "dur": 54, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852075, "dur": 4, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852082, "dur": 52, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852138, "dur": 3, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852145, "dur": 50, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852200, "dur": 3, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852207, "dur": 43, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852255, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852261, "dur": 49, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852314, "dur": 3, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852320, "dur": 50, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852374, "dur": 3, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852381, "dur": 50, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852436, "dur": 3, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852442, "dur": 46, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852493, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852499, "dur": 49, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852553, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852559, "dur": 50, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852614, "dur": 3, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852621, "dur": 62, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852691, "dur": 5, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852700, "dur": 55, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852763, "dur": 4, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852773, "dur": 45, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852822, "dur": 3, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852829, "dur": 42, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852877, "dur": 4, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852884, "dur": 60, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852949, "dur": 3, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867852954, "dur": 45, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853003, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853007, "dur": 43, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853054, "dur": 3, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853059, "dur": 47, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853111, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853115, "dur": 41, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853160, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853164, "dur": 44, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853212, "dur": 2, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853216, "dur": 50, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853270, "dur": 3, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853275, "dur": 43, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853321, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853326, "dur": 50, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853379, "dur": 3, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853384, "dur": 38, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853426, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853430, "dur": 51, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853484, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853489, "dur": 35, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853528, "dur": 2, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853532, "dur": 43, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853578, "dur": 2, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853583, "dur": 39, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853625, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853629, "dur": 39, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853671, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853675, "dur": 32, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853711, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853715, "dur": 28, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853745, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853748, "dur": 35, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853787, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853791, "dur": 39, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853839, "dur": 5, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853847, "dur": 55, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853909, "dur": 4, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853916, "dur": 48, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853975, "dur": 4, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867853981, "dur": 53, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854040, "dur": 4, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854047, "dur": 55, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854110, "dur": 4, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854118, "dur": 34, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854155, "dur": 2, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854160, "dur": 51, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854218, "dur": 4, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854225, "dur": 59, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854289, "dur": 3, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854296, "dur": 45, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854345, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854352, "dur": 50, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854407, "dur": 3, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854412, "dur": 46, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854463, "dur": 3, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854469, "dur": 55, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854528, "dur": 4, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854535, "dur": 54, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854594, "dur": 4, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854601, "dur": 52, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854658, "dur": 3, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854663, "dur": 48, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854717, "dur": 3, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854723, "dur": 53, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854781, "dur": 3, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854787, "dur": 50, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854842, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854848, "dur": 43, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854896, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854901, "dur": 54, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854959, "dur": 3, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867854965, "dur": 51, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855021, "dur": 3, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855027, "dur": 49, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855081, "dur": 3, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855087, "dur": 36, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855127, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855132, "dur": 43, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855180, "dur": 3, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855185, "dur": 52, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855241, "dur": 3, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855248, "dur": 50, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855302, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855309, "dur": 48, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855362, "dur": 4, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855369, "dur": 51, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855425, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855431, "dur": 58, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855493, "dur": 4, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855500, "dur": 36, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855540, "dur": 3, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855545, "dur": 40, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855590, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855596, "dur": 54, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855655, "dur": 3, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855712, "dur": 63, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855783, "dur": 5, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855810, "dur": 79, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855897, "dur": 8, "ph": "X", "name": "ProcessMessages 1588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855909, "dur": 54, "ph": "X", "name": "ReadAsync 1588", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855968, "dur": 3, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867855974, "dur": 51, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856030, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856035, "dur": 65, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856108, "dur": 5, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856117, "dur": 70, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856195, "dur": 5, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856204, "dur": 68, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856277, "dur": 4, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856284, "dur": 62, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856351, "dur": 4, "ph": "X", "name": "ProcessMessages 1386", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856359, "dur": 58, "ph": "X", "name": "ReadAsync 1386", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856421, "dur": 4, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856429, "dur": 54, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856489, "dur": 4, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856496, "dur": 56, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856556, "dur": 3, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856561, "dur": 37, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856604, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856608, "dur": 43, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856655, "dur": 2, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856659, "dur": 40, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856702, "dur": 2, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856707, "dur": 39, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856750, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856755, "dur": 23, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856780, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856784, "dur": 32, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856819, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856823, "dur": 36, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856862, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856866, "dur": 37, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856906, "dur": 2, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856910, "dur": 32, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856945, "dur": 2, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856949, "dur": 40, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856992, "dur": 2, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867856997, "dur": 38, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857038, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857043, "dur": 38, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857084, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857088, "dur": 33, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857125, "dur": 2, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857128, "dur": 35, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857168, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857172, "dur": 55, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857235, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857239, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857309, "dur": 6, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857319, "dur": 40, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857362, "dur": 3, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857368, "dur": 50, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857422, "dur": 3, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857427, "dur": 34, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857464, "dur": 2, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857468, "dur": 35, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857506, "dur": 2, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857511, "dur": 35, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857550, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857553, "dur": 40, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857597, "dur": 2, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857601, "dur": 38, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857642, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857646, "dur": 34, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857684, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857687, "dur": 38, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857729, "dur": 2, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857733, "dur": 38, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857774, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857778, "dur": 36, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857818, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857822, "dur": 33, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857859, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857863, "dur": 38, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857904, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857909, "dur": 36, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857949, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857954, "dur": 37, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857993, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867857997, "dur": 34, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858035, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858039, "dur": 37, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858079, "dur": 2, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858083, "dur": 37, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858124, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858127, "dur": 34, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858165, "dur": 2, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858169, "dur": 25, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858197, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858200, "dur": 29, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858232, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858235, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858272, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858275, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858311, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858314, "dur": 41, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858358, "dur": 2, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858362, "dur": 30, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858395, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858398, "dur": 52, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858456, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858498, "dur": 2, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858502, "dur": 34, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858539, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858543, "dur": 46, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858593, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858596, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858629, "dur": 2, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858633, "dur": 33, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858669, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858673, "dur": 62, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858739, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858742, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858784, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858789, "dur": 35, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858827, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858832, "dur": 52, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858891, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858934, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858938, "dur": 34, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858975, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867858979, "dur": 45, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859030, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859074, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859078, "dur": 35, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859117, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859121, "dur": 45, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859172, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859216, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859220, "dur": 37, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859261, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859265, "dur": 41, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859310, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859313, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859355, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859360, "dur": 37, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859400, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859404, "dur": 37, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859448, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859490, "dur": 2, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859494, "dur": 34, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859531, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859536, "dur": 40, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859582, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859625, "dur": 3, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859630, "dur": 46, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859680, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859685, "dur": 38, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859727, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859730, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859781, "dur": 5, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859789, "dur": 49, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859846, "dur": 5, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859856, "dur": 43, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859903, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859907, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859968, "dur": 3, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867859974, "dur": 49, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860026, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860030, "dur": 34, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860069, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860072, "dur": 41, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860117, "dur": 2, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860121, "dur": 33, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860159, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860163, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860215, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860219, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860275, "dur": 6, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860284, "dur": 26, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860313, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860317, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860347, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860350, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860392, "dur": 3, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860397, "dur": 36, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860436, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860440, "dur": 48, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860495, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860536, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860541, "dur": 38, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860582, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860586, "dur": 41, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860631, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860634, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860677, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860681, "dur": 32, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860716, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860721, "dur": 33, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860757, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860761, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860801, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860804, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860848, "dur": 2, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860853, "dur": 39, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860895, "dur": 2, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860900, "dur": 28, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860931, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860935, "dur": 34, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860973, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867860977, "dur": 34, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861014, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861018, "dur": 31, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861053, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861056, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861100, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861103, "dur": 54, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861162, "dur": 3, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861167, "dur": 36, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861206, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861210, "dur": 58, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861281, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861332, "dur": 4, "ph": "X", "name": "ProcessMessages 1211", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861339, "dur": 51, "ph": "X", "name": "ReadAsync 1211", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861396, "dur": 4, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861402, "dur": 54, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861463, "dur": 4, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861469, "dur": 45, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861520, "dur": 3, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861526, "dur": 58, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861591, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861596, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861656, "dur": 3, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861662, "dur": 37, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861705, "dur": 3, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861710, "dur": 38, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861754, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861801, "dur": 2, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861806, "dur": 36, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861845, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861850, "dur": 47, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861903, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861946, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861951, "dur": 27, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861981, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867861985, "dur": 53, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862047, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862087, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862091, "dur": 37, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862132, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862137, "dur": 44, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862187, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862233, "dur": 2, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862237, "dur": 37, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862277, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862282, "dur": 42, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862330, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862373, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862378, "dur": 36, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862417, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862421, "dur": 44, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862472, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862516, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862520, "dur": 34, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862558, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862562, "dur": 40, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862606, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862612, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862652, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862657, "dur": 36, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862696, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862700, "dur": 39, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862745, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862789, "dur": 2, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862793, "dur": 32, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862829, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862833, "dur": 38, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862877, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862918, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862922, "dur": 43, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862969, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867862973, "dur": 29, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863007, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863010, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863052, "dur": 2, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863057, "dur": 32, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863092, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863096, "dur": 39, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863141, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863183, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863188, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863224, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863228, "dur": 46, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863280, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863320, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863324, "dur": 33, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863361, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863365, "dur": 40, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863413, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863455, "dur": 2, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863459, "dur": 32, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863496, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863500, "dur": 36, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863541, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863544, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863586, "dur": 2, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863590, "dur": 30, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863623, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863626, "dur": 40, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863670, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863673, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863710, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863715, "dur": 34, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863752, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863756, "dur": 38, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863800, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863845, "dur": 2, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863849, "dur": 32, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863884, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863888, "dur": 35, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863930, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863975, "dur": 3, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867863981, "dur": 35, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864019, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864024, "dur": 31, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864058, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864061, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864102, "dur": 2, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864106, "dur": 32, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864141, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864146, "dur": 51, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864201, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864205, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864251, "dur": 3, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864256, "dur": 25, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864284, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864288, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864329, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864332, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864365, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864369, "dur": 33, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864405, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864409, "dur": 49, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864464, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864509, "dur": 3, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864515, "dur": 36, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864555, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864559, "dur": 30, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864595, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864640, "dur": 2, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864645, "dur": 33, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864681, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864685, "dur": 51, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864743, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864791, "dur": 2, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864796, "dur": 34, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864834, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864838, "dur": 34, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864879, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864920, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864924, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864952, "dur": 2, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867864956, "dur": 54, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865014, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865017, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865062, "dur": 2, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865066, "dur": 33, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865102, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865106, "dur": 39, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865152, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865194, "dur": 2, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865198, "dur": 21, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865222, "dur": 1, "ph": "X", "name": "ProcessMessages 111", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865225, "dur": 34, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865265, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865307, "dur": 2, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865312, "dur": 37, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865352, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865356, "dur": 33, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865392, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865396, "dur": 35, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865435, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865438, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865483, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865526, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865530, "dur": 34, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865567, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865571, "dur": 43, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865620, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865662, "dur": 2, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865667, "dur": 47, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865721, "dur": 3, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865726, "dur": 32, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865761, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865765, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865804, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865807, "dur": 40, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865852, "dur": 2, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865856, "dur": 33, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865892, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865896, "dur": 33, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865932, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865935, "dur": 39, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865978, "dur": 3, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867865982, "dur": 51, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866040, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866046, "dur": 59, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866109, "dur": 2, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866114, "dur": 44, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866161, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866165, "dur": 35, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866204, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866207, "dur": 32, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866242, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866246, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866367, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866372, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866440, "dur": 3, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866446, "dur": 57, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866507, "dur": 4, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866513, "dur": 47, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866564, "dur": 3, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866570, "dur": 40, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866615, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866620, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866664, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866704, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866710, "dur": 109, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866832, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866874, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867866880, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867060, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867064, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867122, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867129, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867191, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867198, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867262, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867270, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867332, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867339, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867398, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867406, "dur": 50, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867463, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867470, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867525, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867532, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867587, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867595, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867650, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867658, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867727, "dur": 5, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867736, "dur": 53, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867798, "dur": 5, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867806, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867853, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867861, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867920, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867867927, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868029, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868037, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868101, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868110, "dur": 54, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868172, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868181, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868239, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868245, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868301, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868309, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868370, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868379, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868442, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868451, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868522, "dur": 5, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868532, "dur": 55, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868595, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868603, "dur": 57, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868668, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868678, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868724, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868732, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868788, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868797, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868860, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868871, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868917, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868924, "dur": 45, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868978, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867868988, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869047, "dur": 5, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869056, "dur": 49, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869112, "dur": 9, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869124, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869181, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869190, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869246, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869255, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869318, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869328, "dur": 52, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869387, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869396, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869432, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869437, "dur": 39, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869484, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869491, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869551, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869560, "dur": 54, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869621, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869631, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869690, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869698, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869757, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869765, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869829, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869838, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869901, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869908, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869966, "dur": 4, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867869974, "dur": 49, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870031, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870039, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870098, "dur": 5, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870107, "dur": 58, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870171, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870181, "dur": 56, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870244, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870253, "dur": 55, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870315, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870325, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870389, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870399, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870462, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870471, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870530, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870538, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870597, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870606, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870667, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870676, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870736, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870745, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870804, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870811, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870871, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870879, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870941, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867870950, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871010, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871019, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871077, "dur": 4, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871085, "dur": 48, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871140, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871149, "dur": 51, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871207, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871216, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871284, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871293, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871358, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871366, "dur": 75, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871448, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871455, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871512, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871519, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871810, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871816, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871870, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867871878, "dur": 306, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872192, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872198, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872262, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872270, "dur": 548, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872825, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872831, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872876, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867872882, "dur": 8456, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867881353, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867881364, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867881430, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867881439, "dur": 801, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882251, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882257, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882328, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882337, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882402, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882409, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882481, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882487, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882544, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867882550, "dur": 5757, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888317, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888325, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888394, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888404, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888515, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888521, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888582, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888589, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888655, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867888662, "dur": 1708, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890379, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890385, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890447, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890454, "dur": 166, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890628, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890634, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890697, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890705, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890762, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867890768, "dur": 355, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891129, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891134, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891192, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891199, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891261, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891267, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891322, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891327, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891385, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891392, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891455, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891462, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891519, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891526, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891703, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891709, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891769, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891775, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891831, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891838, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891952, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867891959, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892023, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892029, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892075, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892079, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892148, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892205, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892213, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892268, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892275, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892341, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892347, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892422, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892430, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892470, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892478, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892553, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892559, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892588, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892593, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892710, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892716, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892757, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892763, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892809, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867892815, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893082, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893088, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893154, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893162, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893227, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893234, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893301, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893309, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893369, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893377, "dur": 55, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893440, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893449, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893512, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893520, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893576, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893582, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893645, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893651, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893707, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893714, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893755, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893761, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893848, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893854, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893912, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893919, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893977, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867893984, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894048, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894054, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894106, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894113, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894173, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894179, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894292, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894348, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894355, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894492, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894498, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894561, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894568, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894649, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894655, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894716, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894723, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894786, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867894792, "dur": 970, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867895770, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867895776, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867895837, "dur": 7, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867895847, "dur": 46, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867895898, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867895903, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896096, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896103, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896166, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896174, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896318, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896323, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896369, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896375, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896422, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896428, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896558, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896564, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896619, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896626, "dur": 287, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896922, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896928, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896991, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867896998, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897074, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897082, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897143, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897149, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897218, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897223, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897287, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897294, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897350, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897357, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897469, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897474, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897516, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897522, "dur": 243, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897774, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897820, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897825, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897940, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867897997, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898005, "dur": 90, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898102, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898108, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898162, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898168, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898223, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898227, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898309, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898315, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898352, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867898358, "dur": 816, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899181, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899186, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899226, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899232, "dur": 266, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899505, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899509, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899560, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899566, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899635, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899640, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899686, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899690, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899781, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899786, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899825, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867899831, "dur": 333, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900170, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900176, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900211, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900216, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900383, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900387, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900418, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900422, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900473, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900515, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900521, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900555, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900558, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900612, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900617, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900660, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900664, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900705, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900709, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900854, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900859, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900905, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867900909, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901004, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901041, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901046, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901121, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901124, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901151, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901154, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901186, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901189, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901222, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901225, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901259, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901262, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901287, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901289, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901421, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901424, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901465, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901470, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901518, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901521, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901591, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901627, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901632, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867901971, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902012, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902016, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902052, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902055, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902093, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902096, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902170, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902208, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902212, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902265, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902302, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902306, "dur": 142, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902456, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902463, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902518, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902526, "dur": 285, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902817, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902821, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902866, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902870, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902908, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902912, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902949, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902952, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902985, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867902989, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903022, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903025, "dur": 527, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903557, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903560, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903588, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903591, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903623, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903627, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903773, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903778, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903805, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867903809, "dur": 1012, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867904827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867904831, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867904874, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867904880, "dur": 42320, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867947210, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867947217, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867947252, "dur": 36, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867947290, "dur": 5935, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953236, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953244, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953322, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953333, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953371, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953376, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953435, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953443, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953476, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867953481, "dur": 537, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867954028, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867954034, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867954069, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867954074, "dur": 1142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955226, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955233, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955281, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955287, "dur": 170, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955465, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955471, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955533, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955540, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955577, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955581, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955640, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955646, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955692, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867955699, "dur": 696, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867956399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867956404, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867956456, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867956463, "dur": 773, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957243, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957248, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957282, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957287, "dur": 186, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957482, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957535, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957544, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957652, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957658, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957711, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957717, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957768, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957775, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957945, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957952, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957993, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867957999, "dur": 382, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867958386, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867958391, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867958421, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867958425, "dur": 811, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959246, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959304, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959312, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959400, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959405, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959435, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959440, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959502, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959506, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959561, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959568, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959606, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959612, "dur": 333, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959949, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959954, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867959995, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960000, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960069, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960075, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960108, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960113, "dur": 847, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960965, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867960970, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961030, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961037, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961251, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961256, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961314, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961321, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961356, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961360, "dur": 302, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961671, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961678, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961745, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961753, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961800, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961807, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961842, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961849, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961899, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961907, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961961, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867961968, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962030, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962039, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962102, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962110, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962176, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962184, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962247, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962256, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962320, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962329, "dur": 63, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962399, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962410, "dur": 61, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962480, "dur": 6, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962490, "dur": 59, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962557, "dur": 6, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962567, "dur": 42, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962615, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962623, "dur": 55, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962686, "dur": 6, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962695, "dur": 38, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962738, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962747, "dur": 56, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962808, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867962813, "dur": 483, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963303, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963309, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963346, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963352, "dur": 92, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963452, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963457, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963488, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363867963492, "dur": 1017079, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363868980586, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363868980593, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363868980636, "dur": 1140, "ph": "X", "name": "ProcessMessages 2059", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363868981782, "dur": 449011, "ph": "X", "name": "ReadAsync 2059", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869430811, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869430819, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869430871, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869430880, "dur": 127557, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869558454, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869558464, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869558538, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869558548, "dur": 170413, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869728976, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869728984, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869729022, "dur": 343, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869729371, "dur": 91752, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869821140, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869821150, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869821208, "dur": 48, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869821260, "dur": 14900, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869836180, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869836188, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869836228, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869836235, "dur": 2298, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869838546, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869838554, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869838615, "dur": 53, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869838671, "dur": 151897, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869990585, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869990593, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869990669, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869990677, "dur": 1374, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869992063, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869992070, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869992111, "dur": 45, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363869992162, "dur": 453757, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870445934, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870445941, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870446002, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870446010, "dur": 89258, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870535293, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870535306, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870535398, "dur": 62, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870535464, "dur": 16050, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870551536, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870551547, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870551615, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870551626, "dur": 929, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870552567, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870552574, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870552649, "dur": 58, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870552712, "dur": 240411, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870793141, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870793151, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870793228, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870793237, "dur": 1289, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870794539, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870794546, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870794713, "dur": 51, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870794768, "dur": 390, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870795165, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870795170, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870795208, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3416, "tid": 34359738368, "ts": 1748363870795213, "dur": 13799, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3416, "tid": 4028, "ts": 1748363870810270, "dur": 14575, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3416, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3416, "tid": 30064771072, "ts": 1748363867827101, "dur": 192857, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3416, "tid": 30064771072, "ts": 1748363868019960, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3416, "tid": 30064771072, "ts": 1748363868019963, "dur": 81, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3416, "tid": 4028, "ts": 1748363870824854, "dur": 52, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3416, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3416, "tid": 25769803776, "ts": 1748363867823737, "dur": 2985360, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3416, "tid": 25769803776, "ts": 1748363867823874, "dur": 2810, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3416, "tid": 25769803776, "ts": 1748363870809104, "dur": 109, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3416, "tid": 25769803776, "ts": 1748363870809129, "dur": 38, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3416, "tid": 25769803776, "ts": 1748363870809216, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3416, "tid": 4028, "ts": 1748363870824914, "dur": 49, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748363867841525, "dur": 1804, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363867843340, "dur": 912, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363867844358, "dur": 76, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748363867844434, "dur": 294, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363867851079, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748363867856647, "dur": 153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748363867844748, "dur": 22814, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363867867577, "dur": 2927851, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363870795430, "dur": 220, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363870795650, "dur": 92, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363870795747, "dur": 56, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363870795941, "dur": 75, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363870796051, "dur": 3878, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748363867844900, "dur": 22710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867867623, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867867798, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748363867867737, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_91C7B02DD7E937E1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867868135, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363867868133, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_2F2FB047803CA83B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867868934, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_9C87E32EB3F2A719.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867869026, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363867869024, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_140A21A116E2DF85.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867869337, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867869524, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748363867869651, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867869726, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363867869724, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_39330D1DD49ABBAC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867869916, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748363867870071, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867872203, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867872817, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867873605, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867874196, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867874745, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867875592, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867876698, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867877293, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867877882, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867878457, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867879044, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867879622, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867880693, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867881219, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867881775, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867882580, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867883232, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867883829, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867884402, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867884966, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867885558, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867886147, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867886723, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867887320, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867887870, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867888468, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867889024, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867889637, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867890212, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867890887, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867891442, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867891619, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867892176, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867892658, "dur": 371, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363867892298, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867893327, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867893439, "dur": 540, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867893986, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867894070, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867894866, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_EditorPanel.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748363867894947, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_EditorUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748363867894252, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867895061, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867895210, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867895920, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867896570, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867897415, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867898194, "dur": 2327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867900536, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867900675, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867901216, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867901311, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867901426, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867901830, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867901921, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748363867902021, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867902289, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867902367, "dur": 49636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867952007, "dur": 2017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867954026, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867954125, "dur": 1910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867956036, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867956387, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867958227, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867958538, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867960434, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867962136, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363867960497, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363867962489, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867962627, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867963114, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363867963361, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363867963641, "dur": 1028333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363868991978, "dur": 563940, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363868991977, "dur": 565299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363869558892, "dur": 345, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748363869559354, "dur": 262635, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748363869836615, "dur": 154761, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363869836614, "dur": 154764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363869991401, "dur": 1536, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748363869992944, "dur": 802484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867845006, "dur": 22643, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867867713, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748363867867654, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4C09D1522AE95AC4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868151, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867868237, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868235, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_46AE687975566C6A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868373, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867868467, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868465, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_538E8D352E59CE79.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868725, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868707, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0AAA9749F1127471.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867868801, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867869090, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867869088, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DDE8E3DC54C0DB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867869158, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867869216, "dur": 716, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DDE8E3DC54C0DB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867869949, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867870157, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867870622, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867870855, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867870982, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867871090, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867871158, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867871503, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867871659, "dur": 544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867872244, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867872442, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867872593, "dur": 469, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867873064, "dur": 2363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867875475, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867875806, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867875860, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867875960, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867877097, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867877241, "dur": 1423, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363867879864, "dur": 481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867870248, "dur": 12775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867883024, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867883179, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867883886, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\AnalyticsReporter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867887673, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\RecompileScripts.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867887725, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\WaitForDomainReload.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867887817, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\CachingTestListProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867883365, "dur": 5718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867889084, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867889256, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867889464, "dur": 1691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867891156, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867891317, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867892086, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\AxisComposite.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867892262, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\ButtonWithOneModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867892393, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\ButtonWithTwoModifiers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867892466, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\OneModifierComposite.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867892560, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\Vector2Composite.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867892875, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionParameters.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893130, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893193, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionTrace.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893373, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBindingComposite.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893484, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBindingResolver.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893605, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputInteractionContext.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893767, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Interactions\\MultiTapInteraction.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867893919, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Interactions\\PressInteraction.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867894059, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Interactions\\TapInteraction.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867894169, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\AxisControl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867894450, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DpadControl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867894747, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlList.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867894983, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\AxisDeadzoneProcessor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867895177, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\EditorWindowSpaceProcessor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867895630, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\TouchPhaseControl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867896643, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Utilities\\SavedState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748363867891508, "dur": 5253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867896762, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867897027, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867897282, "dur": 1472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867898755, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867898856, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867899017, "dur": 1334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867900352, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867900521, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867900682, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867901236, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867901379, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867901537, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867902039, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867902165, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867902318, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867902765, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867902899, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867903067, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867903590, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867903758, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867903918, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867904352, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867904503, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748363867904699, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363867905532, "dur": 166, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363867905807, "dur": 1075706, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363868992284, "dur": 438303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363868991970, "dur": 438785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363869431485, "dur": 133, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363869431640, "dur": 298188, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748363869734991, "dur": 709105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748363869734989, "dur": 710379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748363870446574, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748363870446771, "dur": 89338, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748363870552157, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748363870552155, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748363870552347, "dur": 1097, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748363870553452, "dur": 241977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867844888, "dur": 22705, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867867619, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867867724, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867867800, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748363867867712, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4764A97CA20C124C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748363867868671, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867868670, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_209FA7789E0977C1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748363867868842, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867868956, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867868954, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_B2A85112FF760EF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748363867869470, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748363867869684, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748363867869797, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867870433, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867870853, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867870969, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867871052, "dur": 639, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748363867872217, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867873656, "dur": 1147, "ph": "X", "name": "File", "args": {"detail": "Assets\\Script\\BoatMovementController.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867873160, "dur": 2434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867875765, "dur": 1370, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Core.Api.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867877135, "dur": 4270, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867875594, "dur": 6140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867881734, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867882376, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867883031, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867883889, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867884508, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867885233, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867885859, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867886484, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867887096, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867888092, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867888735, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867889698, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867890411, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867891111, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867892327, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748363867893338, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetMenu\\AssetsSelection.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867893554, "dur": 391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\Cache\\AssetStatusCache.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867893946, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\Cache\\BuildPathDictionary.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867894121, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\DrawAssetOverlay.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867894421, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetsUtils\\Processor\\PlasticAssetsProcessor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867894696, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AutoRefresh.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867894767, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\BuildGetEventExtraInfoFunction.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867896722, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\PlasticConnectionMonitor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867896854, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\PlasticWindow.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867897115, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\QueryVisualElementsExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867897229, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\BoolSetting.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748363867892588, "dur": 5210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867897799, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867897929, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748363867898096, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867898578, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867898657, "dur": 1884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867900541, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867901383, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748363867901566, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867902025, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867902137, "dur": 49941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867952081, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867954020, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867954101, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867954335, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867956295, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867958847, "dur": 1911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867960759, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867962034, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867962275, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Quic.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867962943, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748363867960848, "dur": 2634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748363867963483, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748363867963664, "dur": 2831750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867844939, "dur": 22683, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867867646, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748363867867712, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748363867867628, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867868078, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867868148, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748363867868146, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C4EA87A8705F2EB3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867868746, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748363867868744, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_7FEBE6A2654794CE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867868807, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867868915, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867868977, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748363867868975, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0D59EE4A35C68DB6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867869299, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867869389, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867869937, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867870218, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867870456, "dur": 1121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748363867871757, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867871895, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867872024, "dur": 1670, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867873705, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867874322, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867874903, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867875563, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867876095, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867876606, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867877174, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867877788, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867878365, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867878951, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867879515, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867880560, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867881107, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867881648, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867882323, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867882933, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867883473, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867884032, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867884570, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867885172, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867885740, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867886310, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867886894, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867887440, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867888009, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867888606, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867889177, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867889758, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867890321, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867890866, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867891430, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867891624, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867892313, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867892605, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867892703, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867893957, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\TimelineWindow_TrackGui.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748363867892878, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867894030, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867894361, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867895016, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867895577, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867896009, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867896692, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867896922, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867897029, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867897203, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867898029, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867898187, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867898363, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867898969, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867899092, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867899193, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867900323, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867900423, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748363867900533, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867901656, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867901774, "dur": 50190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867951967, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867954006, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867954105, "dur": 1899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867956006, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867956103, "dur": 1951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867958055, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867958147, "dur": 2061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867960209, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867962059, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748363867962135, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748363867960296, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748363867962631, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867962756, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867962883, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867963149, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748363867963148, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748363867963452, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867963558, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748363867963626, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748363867964389, "dur": 2831175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867844964, "dur": 22672, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867867655, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867867720, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748363867867643, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867868160, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867868159, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_94A46A2A182BAD18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867868353, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867868351, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_21CF1C58830621C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867868462, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867868616, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867868955, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867868953, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_14536D8C3E47E1E6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867869171, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867869154, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_F13D2A3973149590.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867869349, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867869402, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_F13D2A3973149590.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867869634, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867869693, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748363867869753, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867869815, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867869955, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867870116, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867870279, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.ExtensionMethods.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867870504, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.PbfReader.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867870647, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.Json\\Net35\\Mapbox.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867870987, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867871114, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867871205, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867871357, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867871473, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867871595, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867871659, "dur": 463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867872125, "dur": 402, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867872599, "dur": 462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867873063, "dur": 2364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867875475, "dur": 1222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867877112, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867879855, "dur": 483, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748363867870230, "dur": 11851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867882082, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867882261, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867882850, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867883399, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867883983, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867884523, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867885065, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867885645, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867886209, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867886764, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867887336, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867887891, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867888457, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867889008, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867889560, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867890154, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867890745, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867891298, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867891993, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867892159, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748363867892334, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867892894, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 710, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\MapboxAccounts\\net35\\MapboxAccountsUnity.dll"}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\UnityEngine.UI.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 1571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 1118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ****************, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867896917, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867897161, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867898192, "dur": 2335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867900527, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867901390, "dur": 50579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867951971, "dur": 2050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867954022, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867954333, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867954326, "dur": 1984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867956311, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867956435, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867958532, "dur": 739, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867959282, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867961199, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748363867962136, "dur": 744, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867963146, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748363867961858, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748363867964373, "dur": 2831222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867845036, "dur": 22627, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867867678, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867867737, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748363867867668, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F372D6AB03EB9C8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868085, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868083, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0CB3585AEAFFAB32.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868284, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868282, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5D282878EB567DDE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868412, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867868550, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867868913, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_86C139E5AAFE1760.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868969, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867868967, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_B19AEDDBA5CF00C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867870095, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867870244, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748363867870356, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867870602, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867870771, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867870879, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867871188, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748363867871616, "dur": 1035, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867872657, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867873608, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867874183, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867874762, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867875495, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867876054, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867876469, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867877071, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867877682, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867878248, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867878869, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867879449, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867880476, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867880991, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867881492, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867882102, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867882773, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867883517, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867884079, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867884745, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867885329, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867885924, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867886489, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867887081, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867887630, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867888439, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867888997, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867889671, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867890263, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867890848, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867891423, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867891629, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867892187, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867892363, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867892576, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867892667, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867892910, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867893207, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867893958, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867893599, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867894173, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867894325, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867894921, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867895570, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867896162, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867896916, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867897065, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867897227, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867897742, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867897950, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867898201, "dur": 2322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867900525, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867900660, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867900996, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867901088, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867901384, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867901560, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867902286, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867902376, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867902483, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867903100, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867903193, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748363867903337, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867903795, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867903860, "dur": 48121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867951986, "dur": 2019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867954006, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867954129, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867956050, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867956131, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867956120, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867958065, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867958159, "dur": 1900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867960059, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867960141, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867962027, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748363867962563, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867962862, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-handle-l1-1-0.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748363867962108, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748363867964208, "dur": 2831200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867845075, "dur": 22605, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867867698, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867867763, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748363867867686, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3B5B0D1C936F55AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748363867868057, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867868170, "dur": 714, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867868967, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867868965, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_10AF8199D68E983D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748363867869411, "dur": 623, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748363867870036, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867870909, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748363867871071, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748363867871278, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867871416, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867871553, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867872093, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12244479590358847285.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748363867872188, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867872277, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867873268, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867873877, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867874438, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867874996, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867875602, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867876315, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867876877, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867877472, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867878060, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867878631, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867879452, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867880491, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867880993, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867881474, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867882062, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867882724, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867883273, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867883868, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867884409, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867884970, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867885524, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867886110, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867886695, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867887264, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867887811, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867888398, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867888954, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867889505, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867890077, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867890655, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867891211, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867891912, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748363867892354, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867892657, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867892083, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748363867893108, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867893217, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867893451, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748363867893608, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867893664, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748363867894522, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867894836, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867895009, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867895800, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867895851, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867896012, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867896667, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867897170, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867898191, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867898865, "dur": 1672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867900538, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867901385, "dur": 3123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867904509, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748363867904660, "dur": 47314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867951978, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748363867954015, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867954115, "dur": 2024, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748363867956373, "dur": 1905, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867958838, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Html.Abstractions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867956142, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748363867960156, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867960455, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748363867960400, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748363867962621, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363867962945, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748363867962944, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748363867963212, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748363867963643, "dur": 1872975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748363869836621, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748363869836620, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748363869837044, "dur": 2366, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748363869839417, "dur": 956018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867845105, "dur": 22591, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867867714, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748363867867771, "dur": 298, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748363867867702, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_61E29B66A87F06EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867868153, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867868937, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_638B50AF35071B35.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867869038, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748363867869037, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B7E76160FABBEBBF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867869478, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867869728, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748363867869726, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_8CDD503792CD44AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867870010, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867870408, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867870495, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748363867870549, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867870689, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867871585, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748363867871974, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4231056368997063253.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748363867872076, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867872266, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867873268, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867873868, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867874414, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867875148, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748363867875060, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867876587, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867877396, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867878066, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867878687, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867879408, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867880520, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867881053, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867881549, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867882500, "dur": 10173, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\TimelineWindow_PlayableLookup.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748363867892762, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\TimelineWindow_Gui.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748363867882180, "dur": 11263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867893444, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867894134, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867894328, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867895034, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867896075, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867896918, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867897032, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867897180, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867897240, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867897768, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867897958, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867898197, "dur": 2339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867900536, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867901384, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867902172, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867902348, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867902823, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867902983, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748363867903164, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867903668, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867903807, "dur": 48183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867951994, "dur": 2017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867954012, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867956133, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748363867954122, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867956345, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867956532, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Postprocessing.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748363867956529, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867958722, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867958832, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867960879, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867960958, "dur": 2094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748363867963053, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363867963634, "dur": 60477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748363868024112, "dur": 2771337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867845141, "dur": 22604, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867867763, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748363867867817, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748363867867750, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1D4B133AF68A73C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867868113, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867868338, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748363867868336, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_DE2A9AC6D714602F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867868580, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748363867868578, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B9BA886FA66D1477.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867868809, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_847CCDCB962CD0E3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867868862, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867868927, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_847CCDCB962CD0E3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867869007, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748363867869005, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867869404, "dur": 481, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748363867870317, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867870748, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748363867870842, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867871088, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1748363867871238, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867871330, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748363867871389, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867871702, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748363867871801, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867871957, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867872136, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867872198, "dur": 853, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\Editor\\VectorSubLayerTreeView.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748363867872198, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867873611, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867874186, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867874731, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867875279, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867875993, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867876539, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867877120, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867877735, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867878292, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867878874, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867879450, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867880549, "dur": 1989, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\SceneViewDrawMode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748363867880515, "dur": 2530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867883045, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867883597, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867884168, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867884712, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867885327, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867885974, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867886538, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867887107, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867887674, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867888382, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867888956, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867889520, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867890098, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867890649, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867891820, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x4.gen.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748363867891226, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867892851, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867893080, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867893654, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748363867893258, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867894231, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867894575, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748363867895042, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867895641, "dur": 1622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867897264, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867897474, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867898195, "dur": 2332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867900528, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867901392, "dur": 50575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867951969, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867954024, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867954179, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867954272, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867956253, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867956383, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867958488, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867958641, "dur": 2170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867960812, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867962862, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748363867960982, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748363867963116, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867963604, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748363867964227, "dur": 2831180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867845180, "dur": 22584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867867783, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748363867867840, "dur": 1235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748363867867768, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C1B7931C83CAC136.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748363867869076, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867869529, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867869868, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867870508, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867870614, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748363867870798, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1748363867870909, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1748363867871589, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748363867872181, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867872317, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867873285, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867873962, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867875037, "dur": 1668, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748363867874519, "dur": 2426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867876945, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867877521, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867878086, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867878635, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867879240, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867880311, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867881378, "dur": 3423, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Editor\\Decorators\\TrackballDecorator.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867881040, "dur": 3929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867884970, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867885568, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867886141, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867886725, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867887278, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867887865, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867888445, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867889002, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867889616, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867890198, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867890752, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867891352, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748363867892215, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\BurstString.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867892641, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Editor\\BurstAssemblyDisable.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867893244, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Avx.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867893480, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\F16C.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867893531, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Fma.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867893929, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Sse3.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748363867891532, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867894230, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867894392, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748363867894602, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748363867894665, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867895265, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867895617, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748363867895372, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1748363867896045, "dur": 112, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867896950, "dur": 51138, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1748363867951966, "dur": 2048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867954015, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867954108, "dur": 1829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867955938, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867956158, "dur": 1872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867958031, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867958130, "dur": 1916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867960047, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867960135, "dur": 1912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748363867962052, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867962215, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867962908, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363867963647, "dur": 2588514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748363870552164, "dur": 241775, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748363870552163, "dur": 241779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748363870793974, "dur": 1396, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748363867845230, "dur": 22552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867867872, "dur": 361, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748363867867786, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_934EE5D4B70C0964.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867868971, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748363867868970, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_0C0EDCF4D8373D10.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867869201, "dur": 671, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_24FE18831D700F3B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867869906, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867869979, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748363867870232, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748363867870483, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867871014, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867871676, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867871830, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14811776502145285846.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748363867872142, "dur": 490, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13937618220218904785.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748363867872633, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867873514, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867874144, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867874694, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867875570, "dur": 1716, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748363867875264, "dur": 2288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867877553, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867878635, "dur": 1697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\SamplesLinkPackageManagerExtension.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748363867878127, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867880370, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867880902, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867881390, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867881967, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867882752, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867883506, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867884078, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867884617, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867885199, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867885781, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867886373, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867886925, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867887484, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867888102, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867888701, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867889279, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867889400, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867889520, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867890113, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867890670, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867891266, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867891812, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867892010, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867892592, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867892873, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867893048, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867893321, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867893916, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867894121, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867894736, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867894924, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867895860, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867896477, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867897040, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867897212, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867897702, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867897841, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867898188, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748363867898378, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867898863, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867899034, "dur": 1511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867900546, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867901391, "dur": 50581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867951973, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867954010, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867954113, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867955999, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867956411, "dur": 1862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867958274, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867958375, "dur": 1941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867960321, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867962271, "dur": 612, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748363867960501, "dur": 2599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748363867963101, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363867963357, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748363867963628, "dur": 57306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363868020937, "dur": 3169, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748363868024107, "dur": 2771435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867845262, "dur": 22536, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867867856, "dur": 370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748363867867799, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5AD924FEB2B8122.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867868228, "dur": 710, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867868996, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867868994, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C8C0272C381EE5BB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867869289, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867869287, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867869614, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748363867869865, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867870003, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867870691, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867870986, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867871091, "dur": 609, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1748363867871795, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867872216, "dur": 843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867873070, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867873641, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867874214, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867874813, "dur": 867, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867876191, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867874759, "dur": 2231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867876990, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867877589, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867878163, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867879089, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867879757, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867880780, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867881273, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867881803, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867882428, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867883006, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867883578, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867884137, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867884690, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867885267, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867885840, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867886419, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867886998, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867887573, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867888311, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867888904, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867889489, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867890036, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867890593, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867891171, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867891814, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867892657, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Utils\\XRSettings.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748363867892014, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867892875, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867893079, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867893286, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867893935, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867894222, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867894322, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867894887, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867895865, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867896482, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867897211, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867898190, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867898351, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867898679, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867898858, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867898983, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867899997, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867900089, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867900526, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867901318, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748363867901438, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867901501, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867901935, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867902077, "dur": 50489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867952568, "dur": 2128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867954701, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867956131, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867954914, "dur": 2257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867957171, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867958666, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867958839, "dur": 3313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748363867957296, "dur": 5564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748363867962862, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867963012, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867963303, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748363867963655, "dur": 2831760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748363870805495, "dur": 3911, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3416, "tid": 4028, "ts": 1748363870825029, "dur": 47, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3416, "tid": 4028, "ts": 1748363870825191, "dur": 2710, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3416, "tid": 4028, "ts": 1748363870810192, "dur": 17786, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}