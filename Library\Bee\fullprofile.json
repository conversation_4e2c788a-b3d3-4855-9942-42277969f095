{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3416, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3416, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3416, "tid": 3816, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3416, "tid": 3816, "ts": 1748360124993570, "dur": 895, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3416, "tid": 3816, "ts": 1748360124997696, "dur": 856, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3416, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3416, "tid": 1, "ts": 1748360122805787, "dur": 5650, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748360122811444, "dur": 78043, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748360122889510, "dur": 97942, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3416, "tid": 3816, "ts": 1748360124998559, "dur": 24, "ph": "X", "name": "", "args": {}}, {"pid": 3416, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122803512, "dur": 9072, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122812587, "dur": 2169244, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122813553, "dur": 3004, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122816568, "dur": 1589, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818165, "dur": 194, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818363, "dur": 25, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818391, "dur": 51, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818446, "dur": 3, "ph": "X", "name": "ProcessMessages 1076", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818450, "dur": 36, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818489, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818493, "dur": 46, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818542, "dur": 2, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818546, "dur": 35, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818585, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818588, "dur": 31, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818622, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818626, "dur": 58, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818691, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818696, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818764, "dur": 4, "ph": "X", "name": "ProcessMessages 1305", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818770, "dur": 50, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818825, "dur": 3, "ph": "X", "name": "ProcessMessages 1239", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818830, "dur": 41, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818874, "dur": 3, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818879, "dur": 40, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818923, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818928, "dur": 44, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818976, "dur": 3, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122818981, "dur": 45, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819030, "dur": 3, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819036, "dur": 36, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819077, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819081, "dur": 45, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819130, "dur": 3, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819135, "dur": 48, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819186, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819191, "dur": 39, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819234, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819239, "dur": 45, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819288, "dur": 3, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819293, "dur": 42, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819339, "dur": 3, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819345, "dur": 41, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819389, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819394, "dur": 35, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819433, "dur": 3, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819438, "dur": 37, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819480, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819485, "dur": 41, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819530, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819534, "dur": 41, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819579, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819584, "dur": 41, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819629, "dur": 3, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819634, "dur": 38, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819675, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819680, "dur": 45, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819727, "dur": 2, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819731, "dur": 34, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819768, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819773, "dur": 33, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819809, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819812, "dur": 33, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819849, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819853, "dur": 36, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819891, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819895, "dur": 35, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819933, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819938, "dur": 33, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819973, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122819977, "dur": 39, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820024, "dur": 4, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820031, "dur": 38, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820072, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820076, "dur": 26, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820106, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820110, "dur": 44, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820157, "dur": 2, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820161, "dur": 38, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820202, "dur": 2, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820208, "dur": 34, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820245, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820249, "dur": 122, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820374, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820377, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820416, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820420, "dur": 31, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820454, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820459, "dur": 34, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820496, "dur": 2, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820500, "dur": 28, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820531, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820534, "dur": 34, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820571, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820576, "dur": 33, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820612, "dur": 2, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820616, "dur": 35, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820655, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820659, "dur": 30, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820693, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820696, "dur": 36, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820735, "dur": 2, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820739, "dur": 33, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820775, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820779, "dur": 35, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820817, "dur": 2, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820820, "dur": 29, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820853, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820856, "dur": 38, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820897, "dur": 2, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820901, "dur": 34, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820938, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820942, "dur": 32, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820978, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122820981, "dur": 37, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821021, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821025, "dur": 31, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821059, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821062, "dur": 30, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821096, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821099, "dur": 37, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821139, "dur": 2, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821143, "dur": 28, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821174, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821178, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821219, "dur": 2, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821223, "dur": 35, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821261, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821265, "dur": 32, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821300, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821304, "dur": 34, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821341, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821344, "dur": 29, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821376, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821380, "dur": 35, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821418, "dur": 2, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821422, "dur": 30, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821455, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821458, "dur": 36, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821498, "dur": 3, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821503, "dur": 30, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821536, "dur": 2, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821540, "dur": 28, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821572, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821576, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821600, "dur": 1, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821603, "dur": 30, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821637, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821642, "dur": 39, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821684, "dur": 2, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821688, "dur": 36, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821727, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821730, "dur": 31, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821764, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821768, "dur": 34, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821805, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821809, "dur": 36, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821848, "dur": 2, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821852, "dur": 27, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821882, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821885, "dur": 28, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821917, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821920, "dur": 30, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821953, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821957, "dur": 36, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122821996, "dur": 2, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822000, "dur": 34, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822037, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822041, "dur": 34, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822079, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822082, "dur": 35, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822121, "dur": 2, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822125, "dur": 35, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822163, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822167, "dur": 32, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822202, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822205, "dur": 32, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822241, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822244, "dur": 37, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822284, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822288, "dur": 35, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822326, "dur": 2, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822330, "dur": 29, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822362, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822366, "dur": 34, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822403, "dur": 2, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822406, "dur": 36, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822445, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822449, "dur": 30, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822482, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822486, "dur": 31, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822519, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822523, "dur": 37, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822563, "dur": 2, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822567, "dur": 35, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822606, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822612, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822642, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822646, "dur": 49, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822703, "dur": 5, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822712, "dur": 52, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822769, "dur": 6, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822777, "dur": 32, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822813, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822819, "dur": 36, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822859, "dur": 4, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822866, "dur": 32, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822902, "dur": 3, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822908, "dur": 28, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822940, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822947, "dur": 32, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822984, "dur": 3, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122822990, "dur": 32, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823026, "dur": 3, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823033, "dur": 30, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823067, "dur": 3, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823073, "dur": 31, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823109, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823114, "dur": 33, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823152, "dur": 3, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823159, "dur": 32, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823195, "dur": 3, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823201, "dur": 37, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823243, "dur": 3, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823250, "dur": 24, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823278, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823283, "dur": 32, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823320, "dur": 3, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823327, "dur": 37, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823369, "dur": 3, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823375, "dur": 32, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823411, "dur": 3, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823418, "dur": 26, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823448, "dur": 2, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823453, "dur": 34, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823492, "dur": 3, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823500, "dur": 37, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823542, "dur": 4, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823549, "dur": 29, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823583, "dur": 4, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823590, "dur": 31, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823626, "dur": 2, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823632, "dur": 32, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823669, "dur": 3, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823676, "dur": 49, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823730, "dur": 4, "ph": "X", "name": "ProcessMessages 1115", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823738, "dur": 29, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823772, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823778, "dur": 34, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823817, "dur": 4, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823823, "dur": 32, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823860, "dur": 3, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823866, "dur": 29, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823899, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823906, "dur": 29, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823940, "dur": 3, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823946, "dur": 34, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823985, "dur": 3, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122823991, "dur": 32, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824028, "dur": 3, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824035, "dur": 30, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824070, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824075, "dur": 32, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824112, "dur": 3, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824119, "dur": 34, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824158, "dur": 4, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824165, "dur": 29, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824198, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824204, "dur": 34, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824243, "dur": 3, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824249, "dur": 31, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824284, "dur": 3, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824291, "dur": 37, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824333, "dur": 3, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824339, "dur": 31, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824374, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824380, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824417, "dur": 3, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824423, "dur": 36, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824463, "dur": 4, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824470, "dur": 30, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824505, "dur": 3, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824511, "dur": 26, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824541, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824546, "dur": 34, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824584, "dur": 199, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824809, "dur": 84, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824898, "dur": 16, "ph": "X", "name": "ProcessMessages 5868", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824917, "dur": 39, "ph": "X", "name": "ReadAsync 5868", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824961, "dur": 4, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122824969, "dur": 38, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825011, "dur": 4, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825018, "dur": 35, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825059, "dur": 3, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825066, "dur": 33, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825103, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825111, "dur": 39, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825154, "dur": 4, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825161, "dur": 36, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825202, "dur": 3, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825209, "dur": 33, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825247, "dur": 3, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825253, "dur": 33, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825290, "dur": 3, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825297, "dur": 34, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825335, "dur": 3, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825341, "dur": 30, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825376, "dur": 4, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825383, "dur": 33, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825420, "dur": 3, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825426, "dur": 33, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825464, "dur": 4, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825471, "dur": 35, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825511, "dur": 3, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825517, "dur": 29, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825550, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825556, "dur": 33, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825593, "dur": 3, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825600, "dur": 34, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825639, "dur": 3, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825645, "dur": 36, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825685, "dur": 3, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825692, "dur": 27, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825723, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825729, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825770, "dur": 3, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825776, "dur": 33, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825814, "dur": 3, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825820, "dur": 31, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825856, "dur": 4, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825863, "dur": 33, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825899, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825903, "dur": 31, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825939, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122825944, "dur": 49, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826002, "dur": 4, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826009, "dur": 53, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826069, "dur": 4, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826075, "dur": 36, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826114, "dur": 3, "ph": "X", "name": "ProcessMessages 1091", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826119, "dur": 31, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826155, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826160, "dur": 34, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826197, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826201, "dur": 35, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826241, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826246, "dur": 58, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826308, "dur": 3, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826315, "dur": 46, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826367, "dur": 4, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826375, "dur": 33, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826410, "dur": 2, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826414, "dur": 34, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826453, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826458, "dur": 27, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826487, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826490, "dur": 30, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826524, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826527, "dur": 34, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826566, "dur": 3, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826572, "dur": 24, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826598, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826601, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826630, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826634, "dur": 39, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826677, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826682, "dur": 29, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826715, "dur": 2, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826718, "dur": 42, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826768, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826810, "dur": 2, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826815, "dur": 29, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826847, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826852, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826893, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826942, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826946, "dur": 35, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826985, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122826989, "dur": 32, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827026, "dur": 1, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827029, "dur": 36, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827069, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827073, "dur": 30, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827108, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827112, "dur": 40, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827156, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827159, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827202, "dur": 2, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827206, "dur": 32, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827243, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827247, "dur": 33, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827284, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827287, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827325, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827329, "dur": 33, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827367, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827371, "dur": 37, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827414, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827458, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827462, "dur": 34, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827500, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827504, "dur": 33, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827546, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827586, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827590, "dur": 33, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827626, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827630, "dur": 39, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827677, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827720, "dur": 2, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827724, "dur": 21, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827748, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827751, "dur": 43, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827800, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827844, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827848, "dur": 28, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827879, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827883, "dur": 36, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827925, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827976, "dur": 3, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122827981, "dur": 31, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828016, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828020, "dur": 28, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828051, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828054, "dur": 37, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828096, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828100, "dur": 34, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828138, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828142, "dur": 33, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828181, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828222, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828226, "dur": 32, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828262, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828266, "dur": 34, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828306, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828347, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828351, "dur": 32, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828388, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828392, "dur": 37, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828435, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828472, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828476, "dur": 30, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828509, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828513, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828562, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828601, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828605, "dur": 31, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828639, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828644, "dur": 47, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828697, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828739, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828744, "dur": 35, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828782, "dur": 3, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828788, "dur": 27, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828818, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828822, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828858, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828861, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828902, "dur": 2, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828906, "dur": 38, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828948, "dur": 2, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828952, "dur": 34, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828991, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122828995, "dur": 32, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829032, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829036, "dur": 30, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829070, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829074, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829103, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829106, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829144, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829148, "dur": 33, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829185, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829189, "dur": 36, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829232, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829271, "dur": 2, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829276, "dur": 31, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829310, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829315, "dur": 42, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829364, "dur": 3, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829369, "dur": 49, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829423, "dur": 3, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829428, "dur": 24, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829454, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829457, "dur": 55, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829520, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829651, "dur": 3, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829657, "dur": 32, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829692, "dur": 2, "ph": "X", "name": "ProcessMessages 1336", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829696, "dur": 40, "ph": "X", "name": "ReadAsync 1336", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829745, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829787, "dur": 2, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829791, "dur": 30, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829825, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829828, "dur": 29, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829864, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829902, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829905, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829940, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829944, "dur": 51, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122829998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830001, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830042, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830046, "dur": 29, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830079, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830083, "dur": 35, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830122, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830125, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830167, "dur": 2, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830171, "dur": 31, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830205, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830209, "dur": 33, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830248, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830289, "dur": 3, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830294, "dur": 32, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830331, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830334, "dur": 30, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830372, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830409, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830413, "dur": 33, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830451, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830455, "dur": 36, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830497, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830528, "dur": 2, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830532, "dur": 32, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830567, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830571, "dur": 44, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830621, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830659, "dur": 2, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830663, "dur": 31, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830698, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830702, "dur": 33, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830741, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830781, "dur": 2, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830785, "dur": 29, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830818, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830824, "dur": 67, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830897, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830903, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830974, "dur": 4, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122830981, "dur": 34, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831019, "dur": 3, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831025, "dur": 75, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831107, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831111, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831175, "dur": 4, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831181, "dur": 44, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831229, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831233, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831279, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831283, "dur": 41, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831330, "dur": 4, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831337, "dur": 50, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831393, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831398, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831454, "dur": 4, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831462, "dur": 67, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831536, "dur": 4, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831543, "dur": 42, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831590, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831594, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831641, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831646, "dur": 53, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831706, "dur": 4, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831713, "dur": 52, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831773, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831779, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831817, "dur": 19, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831838, "dur": 37, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831879, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831882, "dur": 45, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831931, "dur": 2, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831936, "dur": 37, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831977, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122831981, "dur": 36, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832024, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832069, "dur": 2, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832074, "dur": 33, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832111, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832115, "dur": 63, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832185, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832189, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832240, "dur": 3, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832246, "dur": 40, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832292, "dur": 3, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832297, "dur": 26, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832325, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832328, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832369, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832373, "dur": 34, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832411, "dur": 3, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832417, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832455, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832503, "dur": 2, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832508, "dur": 33, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832545, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832549, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832584, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832629, "dur": 2, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832634, "dur": 33, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832670, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832674, "dur": 46, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832724, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832727, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832764, "dur": 2, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832769, "dur": 31, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832803, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832807, "dur": 42, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832853, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832856, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832891, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832895, "dur": 35, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832934, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832938, "dur": 38, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832981, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122832983, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833022, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833026, "dur": 30, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833060, "dur": 3, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833065, "dur": 41, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833113, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833156, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833160, "dur": 29, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833193, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833196, "dur": 31, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833231, "dur": 2, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833235, "dur": 38, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833277, "dur": 2, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833281, "dur": 35, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833320, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833325, "dur": 30, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833360, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833364, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833402, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833441, "dur": 2, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833445, "dur": 21, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833469, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833472, "dur": 47, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833525, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833566, "dur": 2, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833571, "dur": 33, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833608, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833612, "dur": 47, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833662, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833665, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833706, "dur": 2, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833710, "dur": 32, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833746, "dur": 3, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833750, "dur": 35, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833791, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833820, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833823, "dur": 43, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833872, "dur": 3, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833877, "dur": 46, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833927, "dur": 3, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833932, "dur": 37, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833974, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122833978, "dur": 30, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834013, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834017, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834085, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834127, "dur": 2, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834132, "dur": 35, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834172, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834176, "dur": 38, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834218, "dur": 3, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834223, "dur": 34, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834261, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834265, "dur": 28, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834297, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834301, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834336, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834339, "dur": 40, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834387, "dur": 3, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834393, "dur": 76, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834474, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834480, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834536, "dur": 449, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122834992, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835062, "dur": 10, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835075, "dur": 46, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835129, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835138, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835195, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835203, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835254, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835263, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835319, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835326, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835386, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835391, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835437, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835443, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835497, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835505, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835563, "dur": 5, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835571, "dur": 45, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835624, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835632, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835687, "dur": 5, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835695, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835737, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835744, "dur": 59, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835809, "dur": 5, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835817, "dur": 49, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835873, "dur": 7, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835883, "dur": 49, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835938, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122835946, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836001, "dur": 5, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836010, "dur": 52, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836068, "dur": 5, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836076, "dur": 47, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836130, "dur": 6, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836139, "dur": 51, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836196, "dur": 5, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836205, "dur": 53, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836263, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836270, "dur": 47, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836323, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836330, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836381, "dur": 5, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836391, "dur": 48, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836446, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836453, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836508, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836517, "dur": 50, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836572, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836580, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836634, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836644, "dur": 52, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836701, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836710, "dur": 48, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836765, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836775, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836831, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836840, "dur": 46, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836893, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836903, "dur": 52, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122836964, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": ****************, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837031, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837041, "dur": 58, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837105, "dur": 6, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837115, "dur": 47, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837169, "dur": 5, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837177, "dur": 48, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837232, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837240, "dur": 49, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837296, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837304, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837347, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837356, "dur": 43, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837405, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837411, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837463, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837471, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837520, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122837527, "dur": 6346, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122843883, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122843889, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122843952, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122843959, "dur": 2000, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122845968, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122845974, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846032, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846040, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846097, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846104, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846175, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846181, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846234, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122846241, "dur": 5513, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851765, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851773, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851841, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851849, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851906, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851913, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851969, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122851976, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122852030, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122852036, "dur": 1567, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853611, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853617, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853676, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853683, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853830, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853836, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853893, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853901, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853943, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122853949, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854009, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854016, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854081, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854088, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854141, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854147, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854201, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854207, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854255, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854260, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854293, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854299, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854494, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854500, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854556, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854563, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854624, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854630, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854683, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854689, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854737, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854744, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854810, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854817, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854937, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122854944, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855005, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855011, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855050, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855057, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855148, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855153, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855207, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855213, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855248, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855253, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855313, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855319, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855379, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855385, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855483, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855489, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855523, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855530, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855618, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855623, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855708, "dur": 11, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855723, "dur": 258, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855987, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122855991, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856049, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856056, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856191, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856197, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856251, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856258, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856313, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856320, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856374, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856382, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856434, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856440, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856492, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856499, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856553, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856560, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856618, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856624, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856672, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856678, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856728, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856735, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856787, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856792, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856970, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122856976, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857038, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857049, "dur": 214, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857271, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857277, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857336, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857343, "dur": 250, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857601, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857607, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857668, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857674, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857742, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857748, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857827, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857833, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857943, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122857949, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122858008, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122858015, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122858074, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122858080, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122858141, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122858148, "dur": 857, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122859015, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122859022, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122859090, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122859097, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122859134, "dur": 1134, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860276, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860360, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860372, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860413, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860419, "dur": 119, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860547, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860554, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860607, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860613, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860750, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860807, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860813, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860872, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860878, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122860959, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861030, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861036, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861090, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861095, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861142, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861146, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861191, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861195, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861329, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861335, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861386, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122861390, "dur": 1028, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862426, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862432, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862486, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862493, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862529, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862534, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862620, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862665, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862669, "dur": 261, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862937, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862940, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862979, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122862982, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863118, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863156, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863160, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863207, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863258, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863262, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863313, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863320, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863378, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863386, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863423, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863429, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863504, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863509, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863559, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863565, "dur": 291, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863864, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863870, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863928, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122863934, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864084, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864090, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864129, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864136, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864195, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864202, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864239, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864245, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864326, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864331, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864383, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864388, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864440, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864447, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864494, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864499, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864594, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864600, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864661, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864668, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864731, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864737, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864776, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864783, "dur": 82, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864872, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864878, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864916, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122864922, "dur": 302, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865231, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865237, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865279, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865287, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865354, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865359, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865415, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865421, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865498, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865504, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865554, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865559, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865702, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865707, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865735, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865738, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865823, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865873, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865877, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122865997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866000, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866046, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866050, "dur": 220, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866277, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866318, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122866323, "dur": 1443, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122867777, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122867784, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122867847, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122867854, "dur": 58340, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122926211, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122926221, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122926299, "dur": 4056, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122930367, "dur": 4377, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122934761, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122934768, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122934836, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122934845, "dur": 46, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122934899, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122934905, "dur": 1518, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936435, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936487, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936493, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936547, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936554, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936591, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936596, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936634, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936640, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936803, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936842, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122936848, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122937108, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122937113, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122937144, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122937148, "dur": 1156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938312, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938318, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938395, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938402, "dur": 115, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938525, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938530, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938606, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938612, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938665, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938674, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938775, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938821, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122938827, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939112, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939117, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939169, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939175, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939348, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939352, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939398, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939405, "dur": 512, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939927, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939979, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122939986, "dur": 228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940221, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940226, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940279, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940286, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940461, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940465, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940513, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940519, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940569, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940573, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940615, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940621, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940665, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940669, "dur": 296, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940972, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122940977, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941029, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941035, "dur": 396, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941438, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941443, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941490, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941494, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941533, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122941538, "dur": 470, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942014, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942018, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942047, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942052, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942170, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942175, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942228, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942235, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942265, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942269, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942306, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942311, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942361, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942367, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942401, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942405, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942443, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942449, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942500, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942506, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942551, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942558, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942610, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942617, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942650, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942745, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942814, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942825, "dur": 56, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942889, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942897, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942958, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122942966, "dur": 51, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943025, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943033, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943076, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943082, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943129, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943137, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943201, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943210, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943270, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943279, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943348, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943356, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943398, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943405, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943437, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943442, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943472, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943478, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943522, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943527, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943579, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943583, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943677, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943683, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943737, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943741, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943783, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943787, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943937, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943943, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943991, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122943997, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122944029, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122944034, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122944351, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122944358, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122944404, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360122944412, "dur": 913696, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123858126, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123858133, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123858175, "dur": 6749, "ph": "X", "name": "ProcessMessages 2059", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123864935, "dur": 4905, "ph": "X", "name": "ReadAsync 2059", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123869846, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123869850, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123869896, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360123869901, "dur": 754577, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124624494, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124624502, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124624545, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124624551, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124624600, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124624608, "dur": 88719, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124713346, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124713355, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124713417, "dur": 46, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124713468, "dur": 13854, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124727343, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124727350, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124727393, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124727397, "dur": 35265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124762677, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124762684, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124762725, "dur": 43, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124762771, "dur": 14321, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124777106, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124777113, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124777175, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124777184, "dur": 2341, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124779538, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124779545, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124779611, "dur": 43, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124779658, "dur": 137307, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124916980, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124916987, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124917024, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124917029, "dur": 1318, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124918358, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124918363, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124918402, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124918442, "dur": 51731, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970192, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970201, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970300, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970309, "dur": 476, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970793, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970799, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124970860, "dur": 580, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748360124971449, "dur": 10303, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3416, "tid": 3816, "ts": 1748360124998586, "dur": 4891, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3416, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3416, "tid": 8589934592, "ts": 1748360122800077, "dur": 187443, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3416, "tid": 8589934592, "ts": 1748360122987526, "dur": 14, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3416, "tid": 8589934592, "ts": 1748360122987542, "dur": 1153, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3416, "tid": 3816, "ts": 1748360125003481, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3416, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3416, "tid": 4294967296, "ts": 1748360122782499, "dur": 2200792, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748360122786443, "dur": 6603, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748360124983313, "dur": 7057, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748360124987082, "dur": 206, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748360124990478, "dur": 23, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3416, "tid": 3816, "ts": 1748360125003503, "dur": 19, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748360122809978, "dur": 2041, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360122812029, "dur": 891, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360122813047, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748360122813126, "dur": 277, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360122814350, "dur": 1582, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_187779C7F7B3B4CE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748360122817028, "dur": 1883, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748360122825402, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748360122813422, "dur": 21480, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360122834916, "dur": 2135829, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360124970747, "dur": 336, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360124971332, "dur": 3293, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748360122813570, "dur": 21371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122834950, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122835083, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748360122835032, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1D4B133AF68A73C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122835389, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748360122835388, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1FF66EA0847C44F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122835762, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122835893, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122835990, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748360122835988, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122836044, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122836218, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122836476, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122837150, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748360122837301, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122838013, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122838797, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122839460, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122840070, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122840673, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122841131, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122841709, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122842321, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122842924, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122843940, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122844496, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122845029, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122845924, "dur": 826, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\TrackModifier.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748360122845556, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122846915, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122847761, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122848450, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122849047, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122849903, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122850508, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122851119, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122851735, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122852494, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122853568, "dur": 1048, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\IVolumeDebugSettings.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748360122853079, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122854751, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122855420, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748360122855044, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122855732, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122855894, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122856073, "dur": 1116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122857194, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122857719, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122857812, "dur": 724, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122858545, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122858625, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122859479, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122859618, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122859816, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122860239, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122860409, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122860790, "dur": 3105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122863895, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122864736, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122864874, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122865196, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122865308, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748360122865419, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122865730, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122865818, "dur": 67523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122933353, "dur": 1859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122935213, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122935297, "dur": 1629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122936927, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122937120, "dur": 1913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122939036, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122939149, "dur": 1754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122940903, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122941210, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748360122943010, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122943225, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122943290, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122943509, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748360122943969, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748360122944947, "dur": 2025803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122813561, "dur": 21367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122834950, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122835087, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748360122835029, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_91C7B02DD7E937E1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748360122835406, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122835404, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_2F2FB047803CA83B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748360122835870, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122836048, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122836107, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122836105, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\MapboxAccounts\\net35\\MapboxAccountsUnity.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122838002, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122838242, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122838441, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122838850, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122839225, "dur": 748, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122840114, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122840478, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122840603, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122840752, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122843520, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748360122836471, "dur": 7863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122844334, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122844466, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122844973, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122845482, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122846045, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122846588, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122847314, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122847839, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122848374, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122848901, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122849475, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122850015, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122850552, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122851084, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122851637, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122852205, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122852948, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122853532, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122854068, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122855568, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748360122855767, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748360122856104, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122856194, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122856792, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122856920, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122857114, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122857800, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122858346, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122858901, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122859625, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748360122859790, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122860214, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122860285, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122860793, "dur": 3109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122863902, "dur": 69430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122933334, "dur": 1863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122935198, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122935304, "dur": 1666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122936971, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122938970, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122937192, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122939089, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122939325, "dur": 2154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122941480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122943244, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748360122941547, "dur": 1883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748360122943431, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122943751, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748360122943943, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122944247, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748360122944304, "dur": 2026437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122813678, "dur": 21297, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122835040, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748360122834981, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4C09D1522AE95AC4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122835406, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122835657, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748360122835655, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A04142BA6B9BD514.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122835761, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0AAA9749F1127471.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122835899, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122836412, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748360122836411, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_387026818553F7BE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122836629, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122836787, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748360122836989, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122837102, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122837207, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122837325, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122837445, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122837902, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17626248429526979731.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748360122838033, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122838765, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122839350, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122839888, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122840518, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122841226, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122841764, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122842317, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122842905, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122843469, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122844351, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122844947, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122845458, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122846021, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122846720, "dur": 1171, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\RenderQueue.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748360122846607, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122848315, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122848849, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122849416, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122849974, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122850537, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122851096, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122851644, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122852209, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122852745, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122853264, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122853800, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122854352, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122854522, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122855034, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122855346, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122855605, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122855904, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122856217, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122856745, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122856825, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122857443, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122857564, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122858104, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122858603, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122859429, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122859681, "dur": 1104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122860786, "dur": 2238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122863025, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122863191, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122863685, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122863799, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122863897, "dur": 2519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122866417, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748360122866577, "dur": 68690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122935269, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122936917, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122937125, "dur": 1520, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748360122936982, "dur": 3399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122940382, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122940474, "dur": 2072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748360122942551, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122942777, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122942910, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122943058, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122943169, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122943395, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122943725, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122943938, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360122944074, "dur": 1783524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748360124727602, "dur": 243017, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748360124727600, "dur": 243021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748360124970622, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122813611, "dur": 21341, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122834975, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122835040, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748360122834958, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122835402, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122835840, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122835839, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_86C139E5AAFE1760.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122835907, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122836338, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748360122836808, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748360122836870, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122837715, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4082344215324493762.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748360122837989, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122838744, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122839316, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122839865, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122840509, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122841062, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122841597, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122842125, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122843085, "dur": 822, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\LoadTexture2DArray.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748360122842712, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122844475, "dur": 1483, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Data\\VFXDataOutputEvent.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748360122844113, "dur": 1982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122846095, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122846788, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122847388, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122847927, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122848468, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122849011, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122849569, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122850142, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122850672, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122851220, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122851870, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\AndHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748360122851767, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122852923, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122853509, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122854097, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122855843, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122856095, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122856183, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122856771, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122857057, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122857180, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122858233, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122858949, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122859668, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122860784, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122860967, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122861338, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122861411, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122861597, "dur": 1339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122862937, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122863063, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122863183, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122863715, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122863806, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122863924, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122864530, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122864645, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748360122864765, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122865078, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122865170, "dur": 68174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122933346, "dur": 1855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122935203, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122937060, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122935438, "dur": 1843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122937282, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122938702, "dur": 412, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122937354, "dur": 2030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122939386, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122939655, "dur": 1876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122941532, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122943014, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122943405, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748360122942069, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748360122944406, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748360122944514, "dur": 2026240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122813651, "dur": 21312, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122834977, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122835049, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748360122834968, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748360122835413, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122835411, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C4EA87A8705F2EB3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748360122835701, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122835694, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B9BA886FA66D1477.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748360122835890, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122836187, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122836359, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122836680, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748360122836879, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122836987, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122837272, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122837649, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122837985, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122838750, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122839339, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122839880, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122840423, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122841029, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122841559, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122842086, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122842639, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122843201, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122844133, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122844651, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122845140, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122845683, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122846249, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122846788, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122847324, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122847852, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122848386, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122848919, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122849466, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122850014, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122850546, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122851099, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122851661, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122852262, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122852841, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122853380, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122853916, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122854463, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748360122854631, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122855264, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122855565, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748360122855884, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122856645, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122857169, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122856779, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122857427, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122857579, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122858131, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122858496, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122859081, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122859572, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122859650, "dur": 1134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122860785, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122861417, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122861485, "dur": 2404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122863890, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748360122864054, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122864549, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122864696, "dur": 68633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122933331, "dur": 1865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122935197, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122935272, "dur": 1790, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122937067, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122937471, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122937778, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122937943, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122938640, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122938891, "dur": 4144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122943355, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360122937065, "dur": 6882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748360122943947, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360122944057, "dur": 925614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360123869674, "dur": 752817, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360123869673, "dur": 754018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748360124624980, "dur": 138, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748360124625165, "dur": 138034, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748360124777191, "dur": 140242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360124777190, "dur": 140245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360124917459, "dur": 1443, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748360124918907, "dur": 51856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122813704, "dur": 21282, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122834999, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122835055, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748360122834990, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F372D6AB03EB9C8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122835378, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_638B50AF35071B35.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122835793, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122835790, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FF7B422260E7EA7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122835889, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122835887, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_B19AEDDBA5CF00C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122836249, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": ****************, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122836699, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748360122836871, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748360122837134, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122837732, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122837898, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122837986, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122838836, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122839401, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122839964, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122840708, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122841268, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122841804, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122842356, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122842934, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122843523, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122844425, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122844962, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122845475, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122846063, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122846623, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122847157, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122847687, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122848221, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122848765, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122849318, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122849878, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122850421, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122850949, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122851498, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122852066, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122852716, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122853253, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122853789, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122854325, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122854483, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122855146, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122855271, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122855629, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122855747, "dur": 1271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122857019, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122857147, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122857518, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122858045, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122858217, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122858502, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122859435, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122859657, "dur": 1130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122860787, "dur": 3025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122863813, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748360122863964, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122864290, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122864438, "dur": 68917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122933359, "dur": 1856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122935216, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122935302, "dur": 1625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122936928, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122937002, "dur": 1826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122938830, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122938918, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122940697, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122940775, "dur": 1642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122942421, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122943014, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122943278, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Google.Protobuf.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122943365, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.ClientFactory.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122943842, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748360122942567, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748360122944828, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748360122944929, "dur": 2025814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122813728, "dur": 21268, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122835054, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748360122835001, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3B5B0D1C936F55AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748360122835390, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122835898, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122836007, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748360122836005, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B7E76160FABBEBBF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748360122836602, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748360122837498, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122837604, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10061567675400582526.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748360122837907, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122838017, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122838811, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122839467, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122839989, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122840541, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122841120, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122841653, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122842207, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122843330, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\AgeOverLifetime.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748360122842811, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122844026, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122844555, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122845056, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122845584, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122846151, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122847030, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122847562, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122848093, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122848627, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122849165, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122849709, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122850260, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122850796, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122851347, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122851908, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122852740, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122853313, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122853848, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122854632, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748360122855317, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748360122855419, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMPro_UGUI_Private.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748360122855496, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_CoroutineTween.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748360122854809, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122855760, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122855936, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122856147, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748360122856303, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122856457, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122857055, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122857227, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122857771, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122858327, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122859086, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122859428, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122859659, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122860782, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748360122860913, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122861231, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122861330, "dur": 2573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122863903, "dur": 69431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122933337, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122935217, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122935309, "dur": 1640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122936950, "dur": 702, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122937662, "dur": 1950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122939613, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122939902, "dur": 2008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122941911, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122941994, "dur": 2148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748360122944143, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748360122944274, "dur": 2026474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122813768, "dur": 21238, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122835023, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122835089, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748360122835013, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_61E29B66A87F06EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122835416, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122835886, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122836117, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748360122836200, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122836345, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122836950, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122837072, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122837255, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122837365, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122837526, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122837593, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122837810, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122838230, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122838437, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122840478, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122840603, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122840695, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122840748, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122840920, "dur": 1544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122843541, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122844399, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122844836, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\UnityTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122846333, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122836410, "dur": 10012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122846423, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122846566, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122846725, "dur": 5482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122852209, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122852352, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122852535, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122854059, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122854209, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122855315, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionProperty.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122855414, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionReference.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122855635, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBindingComposite.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856085, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\CommonUsages.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856197, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DeltaControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856362, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DiscreteButtonControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856577, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DoubleControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856713, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856787, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856861, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlLayout.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122856926, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlLayoutAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857039, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlLayoutChange.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857102, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlList.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857270, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\KeyControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857322, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\AxisDeadzoneProcessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857511, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\InvertProcessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857566, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\InvertVector2Processor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857812, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\ScaleVector3Processor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122857866, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\StickDeadzoneProcessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748360122858163, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\EnableDeviceCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\IInputDeviceCommandInfo.cs"}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\InitiateUserAccountPairingCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\SetIMECursorPositionCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 4882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ****************, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122861248, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122861408, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122861565, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122862865, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122863019, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122863181, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122863737, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122863887, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122864064, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122864591, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122864730, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122864888, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122865238, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122865328, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122865427, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122865716, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122865809, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122865910, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122866296, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122866411, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748360122866553, "dur": 66806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122933360, "dur": 1852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122935213, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122935294, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122936927, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122937125, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122937675, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122936995, "dur": 2013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122939009, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122940680, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122940756, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748360122939076, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122941438, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122941526, "dur": 1761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748360122943288, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122943510, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748360122943702, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122943902, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122943981, "dur": 45287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122989269, "dur": 3200, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748360122992470, "dur": 1978281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122813791, "dur": 21230, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122835087, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748360122835027, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4764A97CA20C124C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122835397, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122835821, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_0EFC0BA480375449.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122835931, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360122835929, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_0C0EDCF4D8373D10.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122836115, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122836359, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748360122837476, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122837865, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10756517099639384098.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748360122837967, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122838970, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122839537, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122840056, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122840617, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122841151, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122841693, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122842242, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122843538, "dur": 968, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\Clamp.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748360122842795, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122844506, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122844999, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122845511, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122846074, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122846650, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122847284, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122848270, "dur": 3638, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Artistic\\Blend\\BlendNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748360122847810, "dur": 4156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122852330, "dur": 1254, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseOverMessageListener.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748360122851967, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122853759, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122854330, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122855323, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360122855426, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360122855820, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360122856006, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360122856592, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\UnityEditor.TestRunner.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360122854508, "dur": 2197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122856706, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122856814, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122857105, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122857660, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122858232, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122858789, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122859437, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122859615, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122859788, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122860629, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122860785, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122860918, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122861590, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122861716, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122861874, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122863409, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122863530, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122863677, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122865006, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122865185, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122865320, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122865942, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122866082, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748360122866253, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122866734, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122866838, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360122868178, "dur": 121, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360122868414, "dur": 990264, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360123869981, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360123869666, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748360123870301, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360123870366, "dur": 752104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748360123870364, "dur": 753332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748360124624714, "dur": 131, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748360124625140, "dur": 88718, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748360124727592, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748360124727590, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748360124727811, "dur": 242957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122813836, "dur": 21202, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122835108, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748360122835043, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C1B7931C83CAC136.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748360122835822, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4D8A662031E48D37.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748360122835942, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748360122835941, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0D59EE4A35C68DB6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748360122836407, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122836809, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748360122836979, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122837904, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7480027478895629309.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748360122838004, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122838935, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122839496, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122840026, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122840583, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122841162, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122841717, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122842241, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122842826, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122843396, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122844297, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122844814, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122845312, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122845885, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122846445, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122847177, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122847699, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122848250, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122848784, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122849334, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122849898, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122850435, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122850972, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122851519, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122852069, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122852621, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122853167, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122853704, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122854237, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748360122855441, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Editor\\BurstEditorOptions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122855594, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Arm\\NEON.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122855835, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Arm\\NEON_ctor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122855963, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\SimdDebugViews.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122856215, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Bmi1.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122856381, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Csr.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122856502, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\F16C.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122856680, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Popcnt.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748360122854409, "dur": 2594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122857004, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122857137, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748360122857332, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122858080, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122858379, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1748360122858954, "dur": 113, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122859683, "dur": 67057, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1748360122933328, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122935226, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122935335, "dur": 1769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122937105, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122937183, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122939091, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122939173, "dur": 1754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122940928, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122941012, "dur": 1725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748360122942738, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122943165, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122943241, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122943338, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122943951, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122944492, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748360122944550, "dur": 2026189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122813865, "dur": 21195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122835112, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748360122835063, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_934EE5D4B70C0964.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748360122835914, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122836274, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748360122836559, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1748360122836810, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748360122837038, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748360122837973, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8213421625503935943.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748360122838025, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122838737, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122839314, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122839844, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122840382, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122840961, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122841497, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122842019, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122842573, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122843146, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122844136, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122844738, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122845288, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122845933, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122846603, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122847409, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122848399, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122848937, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122849482, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122850025, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122850593, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122851143, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122851694, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122852254, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122852828, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122853363, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122853897, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122854584, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748360122855420, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\random.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748360122854758, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122855580, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122855742, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748360122855998, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122856063, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122856699, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122857171, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748360122857603, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748360122856865, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122857694, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122857871, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122858702, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122859537, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122859616, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748360122859939, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748360122859820, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122860516, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122860655, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122860794, "dur": 3093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122863895, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748360122864018, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122864074, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122864891, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122865014, "dur": 68325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122933341, "dur": 1855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122935197, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122935286, "dur": 1620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122936907, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122936977, "dur": 1678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122938656, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122940669, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748360122938869, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122940843, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122941089, "dur": 1822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748360122942912, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122943121, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122943320, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122943567, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122943805, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122943866, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122944024, "dur": 48450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748360122992475, "dur": 1978282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122813924, "dur": 21145, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122835124, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748360122835070, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5AD924FEB2B8122.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748360122835597, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1825307E4DEB52FB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748360122835887, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122836157, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122836506, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122836704, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122836947, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122837325, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748360122837377, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122837459, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122837999, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122838777, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122839357, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122839896, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122840558, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122841195, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122841749, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122842422, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Slots\\Implementations\\VFXSlotUint.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748360122842307, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122843421, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122844327, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122844831, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122845334, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122845902, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122846491, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122847022, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122847555, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122848082, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122848644, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122849188, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122849725, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122850269, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122850815, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122851364, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122851917, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122852557, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122853159, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122853719, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122854306, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748360122854487, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122855006, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122855183, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122855268, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748360122855487, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122855696, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748360122856107, "dur": 1021, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748360122858649, "dur": 788, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\CloudEdition\\Welcome\\WaitingSignInPanel.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748360122855578, "dur": 4600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122860179, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122860342, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748360122860535, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122861005, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122861092, "dur": 2809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122863902, "dur": 69435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122933339, "dur": 1858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122935198, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122935290, "dur": 1620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122936911, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122936988, "dur": 1785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122938774, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122938864, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122940716, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122941147, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748360122942883, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122943156, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122943327, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122943406, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122943671, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122943830, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360122944085, "dur": 1833111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748360124777199, "dur": 399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748360124777198, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748360124777654, "dur": 2411, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748360124780069, "dur": 190686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748360124978603, "dur": 3209, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3416, "tid": 3816, "ts": 1748360125004353, "dur": 2295, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3416, "tid": 3816, "ts": 1748360125006849, "dur": 2158, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3416, "tid": 3816, "ts": 1748360124996174, "dur": 13621, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}