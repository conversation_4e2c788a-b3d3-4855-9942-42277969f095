{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3416, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3416, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3416, "tid": 236, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3416, "tid": 236, "ts": 1748345764308289, "dur": 828, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3416, "tid": 236, "ts": 1748345764311942, "dur": 1193, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3416, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3416, "tid": 1, "ts": 1748345759395433, "dur": 6240, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748345759401682, "dur": 83204, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748345759484911, "dur": 85295, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3416, "tid": 236, "ts": 1748345764313146, "dur": 37, "ph": "X", "name": "", "args": {}}, {"pid": 3416, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759393191, "dur": 6814, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759400009, "dur": 4897970, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759401037, "dur": 3718, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759404769, "dur": 1994, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759406771, "dur": 316, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407097, "dur": 36, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407136, "dur": 67, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407211, "dur": 5, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407219, "dur": 33, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407254, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407258, "dur": 31, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407299, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407331, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407335, "dur": 34, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407376, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407381, "dur": 49, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407435, "dur": 3, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407440, "dur": 47, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407492, "dur": 2, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407496, "dur": 43, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407543, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407547, "dur": 46, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407597, "dur": 4, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407604, "dur": 45, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407656, "dur": 5, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407664, "dur": 54, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407724, "dur": 3, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407731, "dur": 42, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407778, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407782, "dur": 35, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407822, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407826, "dur": 30, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407860, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407863, "dur": 53, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407925, "dur": 4, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759407933, "dur": 69, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408008, "dur": 4, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408014, "dur": 49, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408068, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408073, "dur": 51, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408129, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408133, "dur": 48, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408187, "dur": 3, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408192, "dur": 43, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408243, "dur": 4, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408250, "dur": 36, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408290, "dur": 3, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408295, "dur": 32, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408331, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408335, "dur": 48, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408389, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408395, "dur": 81, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408483, "dur": 5, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408490, "dur": 62, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408559, "dur": 5, "ph": "X", "name": "ProcessMessages 1161", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408567, "dur": 59, "ph": "X", "name": "ReadAsync 1161", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408634, "dur": 5, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408642, "dur": 63, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408711, "dur": 4, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408719, "dur": 63, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408789, "dur": 6, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408799, "dur": 63, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408868, "dur": 5, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408876, "dur": 60, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408943, "dur": 5, "ph": "X", "name": "ProcessMessages 1329", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759408952, "dur": 58, "ph": "X", "name": "ReadAsync 1329", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409016, "dur": 4, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409022, "dur": 47, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409073, "dur": 4, "ph": "X", "name": "ProcessMessages 1279", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409080, "dur": 56, "ph": "X", "name": "ReadAsync 1279", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409144, "dur": 5, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409152, "dur": 63, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409224, "dur": 5, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409232, "dur": 49, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409286, "dur": 3, "ph": "X", "name": "ProcessMessages 1035", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409292, "dur": 47, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409350, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409354, "dur": 54, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409424, "dur": 4, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409431, "dur": 57, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409493, "dur": 3, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409498, "dur": 131, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409635, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409639, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409708, "dur": 3, "ph": "X", "name": "ProcessMessages 1307", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409713, "dur": 71, "ph": "X", "name": "ReadAsync 1307", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409790, "dur": 4, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409796, "dur": 48, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409848, "dur": 2, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409853, "dur": 53, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409910, "dur": 3, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409916, "dur": 48, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409968, "dur": 2, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759409972, "dur": 37, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410013, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410017, "dur": 46, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410068, "dur": 2, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410072, "dur": 37, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410114, "dur": 2, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410118, "dur": 35, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410157, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410161, "dur": 38, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410204, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410208, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410252, "dur": 3, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410258, "dur": 39, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410301, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410307, "dur": 32, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410343, "dur": 2, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410347, "dur": 44, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410398, "dur": 4, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410404, "dur": 34, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410441, "dur": 2, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410444, "dur": 47, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410495, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410499, "dur": 35, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410541, "dur": 4, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410547, "dur": 34, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410585, "dur": 2, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410590, "dur": 24, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410616, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410620, "dur": 33, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410656, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410660, "dur": 33, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410697, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410701, "dur": 43, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410748, "dur": 2, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410752, "dur": 47, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410804, "dur": 3, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410808, "dur": 38, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410850, "dur": 2, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410855, "dur": 44, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410902, "dur": 2, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410906, "dur": 38, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410948, "dur": 2, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759410952, "dur": 46, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411001, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411005, "dur": 37, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411046, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411049, "dur": 40, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411093, "dur": 2, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411097, "dur": 42, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411143, "dur": 5, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411150, "dur": 46, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411204, "dur": 4, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411211, "dur": 65, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411283, "dur": 5, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411291, "dur": 50, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411346, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411349, "dur": 38, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411392, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411396, "dur": 28, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411427, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411430, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411464, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411468, "dur": 35, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411507, "dur": 2, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411511, "dur": 50, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411565, "dur": 3, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411569, "dur": 35, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411607, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411611, "dur": 42, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411658, "dur": 2, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411662, "dur": 25, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411690, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411693, "dur": 48, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411749, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411753, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411783, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411787, "dur": 31, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411825, "dur": 3, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411830, "dur": 46, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411879, "dur": 2, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411885, "dur": 38, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411930, "dur": 3, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411936, "dur": 30, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411968, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759411971, "dur": 33, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412008, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412011, "dur": 37, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412052, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412056, "dur": 31, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412090, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412094, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412127, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412131, "dur": 37, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412171, "dur": 2, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412176, "dur": 37, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412216, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412220, "dur": 32, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412256, "dur": 2, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412260, "dur": 33, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412296, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412300, "dur": 35, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412338, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412342, "dur": 44, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412394, "dur": 4, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412400, "dur": 48, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412452, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412457, "dur": 43, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412504, "dur": 3, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412509, "dur": 39, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412552, "dur": 3, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412557, "dur": 33, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412594, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412599, "dur": 39, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412642, "dur": 3, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412647, "dur": 39, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412690, "dur": 2, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412695, "dur": 34, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412732, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412737, "dur": 39, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412780, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412785, "dur": 42, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412831, "dur": 2, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412836, "dur": 35, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412875, "dur": 2, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412879, "dur": 36, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412919, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412923, "dur": 38, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412965, "dur": 2, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759412969, "dur": 36, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413009, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413014, "dur": 34, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413052, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413056, "dur": 39, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413099, "dur": 2, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413103, "dur": 37, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413145, "dur": 3, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413150, "dur": 35, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413188, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413192, "dur": 38, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413234, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413238, "dur": 34, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413277, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413280, "dur": 37, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413321, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413325, "dur": 31, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413361, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413364, "dur": 35, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413403, "dur": 2, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413408, "dur": 37, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413449, "dur": 2, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413453, "dur": 32, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413489, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413493, "dur": 35, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413532, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413535, "dur": 37, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413576, "dur": 2, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413580, "dur": 36, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413621, "dur": 2, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413625, "dur": 33, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413662, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413666, "dur": 37, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413708, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413712, "dur": 37, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413752, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413757, "dur": 32, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413792, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413796, "dur": 35, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413834, "dur": 2, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413838, "dur": 34, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413877, "dur": 2, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413881, "dur": 33, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413918, "dur": 2, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413922, "dur": 37, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413963, "dur": 2, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759413967, "dur": 35, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414005, "dur": 2, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414010, "dur": 34, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414048, "dur": 2, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414052, "dur": 36, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414093, "dur": 166, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414282, "dur": 56, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414341, "dur": 7, "ph": "X", "name": "ProcessMessages 4419", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414350, "dur": 38, "ph": "X", "name": "ReadAsync 4419", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414392, "dur": 2, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414397, "dur": 39, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414442, "dur": 4, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414448, "dur": 41, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414494, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414499, "dur": 39, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414542, "dur": 3, "ph": "X", "name": "ProcessMessages 1241", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414547, "dur": 38, "ph": "X", "name": "ReadAsync 1241", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414589, "dur": 2, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414594, "dur": 33, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414631, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414634, "dur": 41, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414680, "dur": 2, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414684, "dur": 38, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414726, "dur": 2, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414730, "dur": 22, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414755, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414758, "dur": 32, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414794, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414798, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414837, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414841, "dur": 35, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414880, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414884, "dur": 37, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414925, "dur": 2, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414928, "dur": 36, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414971, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759414975, "dur": 38, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415017, "dur": 2, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415021, "dur": 32, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415057, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415060, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415085, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415089, "dur": 37, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415133, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415176, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415180, "dur": 35, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415219, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415223, "dur": 54, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415283, "dur": 3, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415289, "dur": 191, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415498, "dur": 10, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415511, "dur": 177, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415707, "dur": 14, "ph": "X", "name": "ProcessMessages 1552", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415727, "dur": 167, "ph": "X", "name": "ReadAsync 1552", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415901, "dur": 5, "ph": "X", "name": "ProcessMessages 1918", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415908, "dur": 46, "ph": "X", "name": "ReadAsync 1918", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415959, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759415964, "dur": 47, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416015, "dur": 2, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416020, "dur": 38, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416064, "dur": 2, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416068, "dur": 32, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416103, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416107, "dur": 35, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416146, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416151, "dur": 38, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416193, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416197, "dur": 47, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416249, "dur": 3, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416253, "dur": 41, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416300, "dur": 3, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416305, "dur": 43, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416354, "dur": 3, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416360, "dur": 39, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416403, "dur": 3, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416408, "dur": 30, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416442, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416447, "dur": 35, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416486, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416491, "dur": 33, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416528, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416532, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416569, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416573, "dur": 37, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416614, "dur": 2, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416619, "dur": 60, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416682, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416685, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416727, "dur": 2, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416731, "dur": 33, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416768, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416773, "dur": 39, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416816, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416819, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416864, "dur": 2, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416868, "dur": 32, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416904, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416908, "dur": 33, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416947, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416987, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759416991, "dur": 34, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417029, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417032, "dur": 41, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417078, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417081, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417124, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417128, "dur": 32, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417164, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417168, "dur": 34, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417208, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417249, "dur": 2, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417253, "dur": 32, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417289, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417293, "dur": 34, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417334, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417374, "dur": 2, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417378, "dur": 34, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417415, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417420, "dur": 36, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417463, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417502, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417507, "dur": 31, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417542, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417546, "dur": 81, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417633, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417637, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417689, "dur": 5, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417698, "dur": 45, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417749, "dur": 3, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417755, "dur": 43, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417806, "dur": 5, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417814, "dur": 52, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417872, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417876, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417927, "dur": 4, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417933, "dur": 25, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417962, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759417966, "dur": 51, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418024, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418067, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418071, "dur": 35, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418111, "dur": 3, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418116, "dur": 44, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418166, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418209, "dur": 3, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418214, "dur": 32, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418249, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418253, "dur": 33, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418290, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418292, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418335, "dur": 2, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418340, "dur": 32, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418376, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418380, "dur": 35, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418419, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418421, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418464, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418468, "dur": 33, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418504, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418508, "dur": 33, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418548, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418592, "dur": 4, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418599, "dur": 29, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418631, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418636, "dur": 71, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418711, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418714, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418763, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418769, "dur": 50, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418824, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418829, "dur": 40, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418872, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418876, "dur": 43, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418928, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418977, "dur": 3, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759418982, "dur": 39, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419025, "dur": 3, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419029, "dur": 34, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419069, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419072, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419109, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419113, "dur": 31, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419148, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419152, "dur": 147, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419305, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419309, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419352, "dur": 4, "ph": "X", "name": "ProcessMessages 2027", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419358, "dur": 31, "ph": "X", "name": "ReadAsync 2027", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419394, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419398, "dur": 34, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419438, "dur": 4, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419444, "dur": 41, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419491, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419495, "dur": 32, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419531, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419535, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419570, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419611, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419615, "dur": 34, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419653, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419657, "dur": 59, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419722, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419771, "dur": 3, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419776, "dur": 44, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419827, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419877, "dur": 3, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419883, "dur": 39, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419927, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419932, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419975, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759419979, "dur": 37, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420020, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420024, "dur": 46, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420074, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420077, "dur": 39, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420121, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420125, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420151, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420155, "dur": 64, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420227, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420371, "dur": 3, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420376, "dur": 43, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420425, "dur": 4, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420432, "dur": 39, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420477, "dur": 3, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420482, "dur": 49, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420538, "dur": 5, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420545, "dur": 52, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420604, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420608, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420658, "dur": 4, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420664, "dur": 55, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420724, "dur": 2, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420728, "dur": 38, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420771, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420774, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420821, "dur": 3, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420826, "dur": 30, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420863, "dur": 10, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420877, "dur": 66, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420950, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420994, "dur": 3, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759420998, "dur": 32, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421034, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421038, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421079, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421120, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421124, "dur": 36, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421164, "dur": 3, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421168, "dur": 41, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421213, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421216, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421263, "dur": 3, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421268, "dur": 35, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421307, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421311, "dur": 47, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421366, "dur": 2, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421374, "dur": 53, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421434, "dur": 5, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421441, "dur": 44, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421490, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421494, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421544, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421548, "dur": 40, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421594, "dur": 4, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421600, "dur": 48, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421656, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421697, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421702, "dur": 37, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421743, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421747, "dur": 37, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421791, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421840, "dur": 4, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421846, "dur": 26, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421874, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421877, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421947, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421952, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421988, "dur": 3, "ph": "X", "name": "ProcessMessages 1195", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759421993, "dur": 60, "ph": "X", "name": "ReadAsync 1195", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422058, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422062, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422101, "dur": 3, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422106, "dur": 31, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422141, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422145, "dur": 46, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422198, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422237, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422241, "dur": 31, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422276, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422279, "dur": 42, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422325, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422328, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422366, "dur": 2, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422370, "dur": 31, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422404, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422408, "dur": 41, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422452, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422455, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422494, "dur": 2, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422498, "dur": 58, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422560, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422565, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422604, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422608, "dur": 37, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422650, "dur": 4, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422656, "dur": 28, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422688, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422691, "dur": 23, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422718, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422720, "dur": 35, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422758, "dur": 2, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422762, "dur": 31, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422797, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422800, "dur": 38, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422845, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422883, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422887, "dur": 30, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422921, "dur": 3, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422926, "dur": 41, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759422973, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423011, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423015, "dur": 29, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423048, "dur": 2, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423053, "dur": 40, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423099, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423137, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423141, "dur": 23, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423167, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423170, "dur": 27, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423201, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423203, "dur": 34, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423241, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423245, "dur": 32, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423280, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423284, "dur": 28, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423316, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423319, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423351, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423355, "dur": 32, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423393, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423431, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423435, "dur": 30, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423469, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423472, "dur": 43, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423522, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423558, "dur": 2, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423562, "dur": 29, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423596, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423600, "dur": 51, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423658, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423687, "dur": 2, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423691, "dur": 31, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423725, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423729, "dur": 20, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423752, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423755, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423795, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423833, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423837, "dur": 34, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423875, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423879, "dur": 33, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423915, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423919, "dur": 32, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423954, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423958, "dur": 28, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423989, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759423993, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424024, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424028, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424071, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424074, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424111, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424115, "dur": 32, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424151, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424154, "dur": 33, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424191, "dur": 2, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424195, "dur": 33, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424233, "dur": 3, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424239, "dur": 30, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424273, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424276, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424319, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424322, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424371, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424377, "dur": 111, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424494, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424500, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424561, "dur": 419, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759424988, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425052, "dur": 10, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425066, "dur": 44, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425116, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425124, "dur": 42, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425173, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425180, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425230, "dur": 5, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425238, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425288, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425297, "dur": 45, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425347, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425355, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425413, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425420, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425480, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425488, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425544, "dur": 5, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425553, "dur": 43, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425601, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425608, "dur": 56, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425672, "dur": 6, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425681, "dur": 49, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425737, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425746, "dur": 47, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425799, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425809, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425854, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425861, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425910, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425921, "dur": 44, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425971, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759425979, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426032, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426041, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426093, "dur": 6, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426103, "dur": 47, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426157, "dur": 7, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426168, "dur": 53, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426228, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426237, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426298, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426307, "dur": 53, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426367, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426377, "dur": 50, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426435, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426445, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426504, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426514, "dur": 63, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426584, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426594, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426656, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426667, "dur": 69, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426745, "dur": 9, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426758, "dur": 60, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426827, "dur": 6, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426837, "dur": 66, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426910, "dur": 7, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426921, "dur": 48, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426976, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759426986, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427046, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427056, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427116, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427125, "dur": 50, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427182, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427191, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427252, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427261, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427321, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427332, "dur": 48, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427386, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427395, "dur": 47, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427449, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427458, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427511, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427520, "dur": 50, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427576, "dur": 4, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427584, "dur": 46, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427637, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427643, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427719, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427725, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427781, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759427787, "dur": 7066, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759434865, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759434872, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759434943, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759434950, "dur": 403, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435360, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435366, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435432, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435439, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435497, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435504, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435630, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435636, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435689, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759435695, "dur": 5506, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441213, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441222, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441320, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441328, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441386, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441395, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441459, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441465, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441523, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441530, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441596, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759441603, "dur": 1747, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443360, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443366, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443416, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443423, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443624, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443630, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443694, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443704, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443767, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443773, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443827, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443833, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443943, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759443950, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444015, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444022, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444075, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444082, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444133, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444138, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444190, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444197, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444360, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444366, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444422, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444428, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444549, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444555, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444606, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444612, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444668, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444675, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444724, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444731, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444859, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444865, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444920, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444927, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444986, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759444993, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445046, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445053, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445226, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445280, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445287, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445404, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445410, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445455, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445461, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445497, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445503, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445755, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445813, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445820, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445875, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445883, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445938, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759445945, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446139, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446144, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446192, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446197, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446241, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446246, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446289, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446293, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446335, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446339, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446384, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446389, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446432, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446439, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446490, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446496, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446545, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446551, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446656, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446662, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446712, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446718, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446798, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446804, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446861, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446870, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446927, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446935, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446986, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759446993, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447042, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447094, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447101, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447161, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447167, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447220, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447226, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447281, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447289, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447341, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447349, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447440, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447445, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447494, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447501, "dur": 116, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447625, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447630, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447683, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759447690, "dur": 527, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448225, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448233, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448289, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448297, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448351, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448358, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448414, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448421, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448475, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448481, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448533, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448540, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448638, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448646, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759448702, "dur": 1146, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759449856, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759449922, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759449930, "dur": 1252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451191, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451198, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451262, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451269, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451359, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451363, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451417, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451425, "dur": 167, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451600, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451605, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451662, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451669, "dur": 220, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451898, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451903, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451960, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759451965, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452171, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452176, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452235, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452242, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452283, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452287, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452439, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452444, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452507, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452513, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452597, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452604, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452671, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452678, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452861, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452867, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452926, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759452934, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453007, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453013, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453067, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453073, "dur": 154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453235, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453240, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453306, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453312, "dur": 130, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453453, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453459, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453525, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453529, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453576, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453580, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453628, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453632, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453674, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453678, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453739, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453779, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759453783, "dur": 724, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454517, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454523, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454584, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454589, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454673, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454677, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454727, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454734, "dur": 221, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454963, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759454969, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455025, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455032, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455099, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455104, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455167, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455174, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455229, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455234, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455278, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455283, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455327, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455331, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455374, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455379, "dur": 292, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455677, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455681, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455729, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455735, "dur": 88, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455832, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455883, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455890, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455945, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759455949, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456006, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456012, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456065, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456069, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456193, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456199, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456248, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456254, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456346, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456352, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456413, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456421, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456486, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456491, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456570, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456577, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456632, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456637, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456692, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456697, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456747, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456751, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456804, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456812, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456858, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456864, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456909, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456916, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759456997, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457002, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457033, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457038, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457066, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457071, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457216, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457222, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457282, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457288, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457407, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457413, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457468, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457474, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457611, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457616, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457669, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457675, "dur": 240, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457921, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457926, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457985, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759457992, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458155, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458160, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458219, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458224, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458269, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458272, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458539, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458593, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759458597, "dur": 1281, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759459886, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759459890, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759459939, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759459946, "dur": 45683, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759505641, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759505649, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759505691, "dur": 2726, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759508426, "dur": 3201, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511638, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511646, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511703, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511711, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511764, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511769, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511840, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511845, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511889, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759511895, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512290, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512295, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512348, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512353, "dur": 475, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512834, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512839, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512882, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759512887, "dur": 633, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513527, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513532, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513594, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513602, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513656, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513666, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513717, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513722, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513766, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513772, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513817, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513822, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513895, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513900, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513945, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759513951, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514026, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514031, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514077, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514082, "dur": 549, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514640, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514647, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514689, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514695, "dur": 269, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514973, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759514978, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515017, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515022, "dur": 556, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515587, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515592, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515654, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515661, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515715, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515721, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515906, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515911, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515965, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759515971, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516278, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516284, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516335, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516340, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516424, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516429, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516477, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516482, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516887, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516892, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516936, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759516942, "dur": 484, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517432, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517438, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517482, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517487, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517661, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517666, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517743, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759517748, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518004, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518010, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518070, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518078, "dur": 280, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518364, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518368, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518417, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518424, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518593, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518598, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518653, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518661, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518714, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759518720, "dur": 783, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519511, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519518, "dur": 208, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519734, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519741, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519799, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519806, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519862, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759519868, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520069, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520076, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520137, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520143, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520199, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520206, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520263, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520269, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520336, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520342, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520402, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520409, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520466, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520476, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520544, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520553, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520609, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520617, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520675, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520682, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520754, "dur": 5, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520764, "dur": 51, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520821, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520827, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520884, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520894, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520950, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759520957, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521013, "dur": 5, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521021, "dur": 48, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521076, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521083, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521136, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521144, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521201, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521208, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521264, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521273, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521331, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521339, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521391, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521400, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521456, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521462, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521519, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521528, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521599, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521605, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521640, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521643, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521685, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521690, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521741, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521745, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521795, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521799, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521871, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521878, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521918, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759521924, "dur": 221, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759522153, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759522159, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759522195, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345759522201, "dur": 1685523, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345761207738, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345761207746, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345761207781, "dur": 8123, "ph": "X", "name": "ProcessMessages 2059", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345761215913, "dur": 1099259, "ph": "X", "name": "ReadAsync 2059", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762315189, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762315198, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762315249, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762315257, "dur": 144433, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762459704, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762459711, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762459747, "dur": 33, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762459782, "dur": 13757, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762473553, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762473560, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762473594, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762473599, "dur": 3043, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762476658, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762476665, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762476696, "dur": 40, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762476739, "dur": 9038, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762485794, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762485802, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762485873, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762485881, "dur": 144348, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762630244, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762630251, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762630299, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762630306, "dur": 1338, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762631653, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762631659, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762631712, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762631752, "dur": 289341, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762921106, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762921114, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762921155, "dur": 332, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345762921493, "dur": 1055217, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345763976725, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345763976732, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345763976765, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345763976771, "dur": 67462, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764044251, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764044261, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764044321, "dur": 44, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764044370, "dur": 12868, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764057253, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764057260, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764057294, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764057300, "dur": 916, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764058225, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764058231, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764058279, "dur": 76, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764058360, "dur": 225322, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764283699, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764283707, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764283778, "dur": 10, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764283793, "dur": 1090, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764284894, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764284901, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764284992, "dur": 49, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764285048, "dur": 539, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764285594, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764285600, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764285658, "dur": 745, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3416, "tid": 12884901888, "ts": 1748345764286411, "dur": 11493, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3416, "tid": 236, "ts": 1748345764313190, "dur": 4908, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3416, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3416, "tid": 8589934592, "ts": 1748345759390543, "dur": 179708, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3416, "tid": 8589934592, "ts": 1748345759570254, "dur": 10, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3416, "tid": 8589934592, "ts": 1748345759570265, "dur": 1169, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3416, "tid": 236, "ts": 1748345764318102, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3416, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3416, "tid": 4294967296, "ts": 1748345759373034, "dur": 4926399, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748345759376943, "dur": 6838, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748345764299454, "dur": 5617, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748345764302382, "dur": 187, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3416, "tid": 4294967296, "ts": 1748345764305180, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3416, "tid": 236, "ts": 1748345764318123, "dur": 19, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748345759398992, "dur": 1650, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345759400652, "dur": 954, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345759401725, "dur": 74, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748345759401799, "dur": 329, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345759403343, "dur": 1572, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_187779C7F7B3B4CE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748345759406306, "dur": 2458, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748345759416944, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748345759417240, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_634EB3DA1CD7E5BE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748345759417487, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748345759402150, "dur": 23873, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345759426036, "dur": 4860632, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345764286670, "dur": 212, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345764286882, "dur": 90, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345764286977, "dur": 53, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345764287160, "dur": 67, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345764287265, "dur": 3284, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748345759402496, "dur": 23613, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759426174, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748345759426113, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F372D6AB03EB9C8E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748345759426526, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748345759426525, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_2F2FB047803CA83B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748345759427012, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748345759427010, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_B19AEDDBA5CF00C4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748345759427366, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748345759427653, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748345759428415, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748345759428582, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748345759428721, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759429184, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759429952, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759430534, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759431081, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759431661, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759432220, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759432782, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759433378, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759433937, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759434504, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759435377, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759435994, "dur": 3103, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Data\\RenderStateData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748345759435933, "dur": 3653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759439586, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759440151, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759440693, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759441243, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759441792, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759442379, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759443302, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759443965, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759444520, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759445157, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748345759445408, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748345759446060, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\BurstCompiler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748345759446382, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\CompilerServices\\Aliasing.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748345759447111, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Common.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748345759447167, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\f16.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748345759447389, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Avx2.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748345759445326, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759447940, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759448092, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A9D47564F0C574EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748345759448155, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748345759448497, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759449065, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759449157, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748345759449729, "dur": 119, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759450347, "dur": 56963, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748345759511221, "dur": 1985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759513207, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759513295, "dur": 1838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759515134, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759515270, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748345759515210, "dur": 1958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759517169, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759517257, "dur": 1918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759519181, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759519675, "dur": 1723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759521399, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748345759521518, "dur": 1837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748345759523432, "dur": 4763189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759402423, "dur": 23650, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759426090, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759426178, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748345759426078, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759426520, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759426518, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1FF66EA0847C44F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759427012, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759427108, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759427107, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759427329, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748345759427554, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759428422, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748345759428799, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748345759428978, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759429218, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759430075, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759430639, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759431188, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759431747, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759432462, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759433065, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759433639, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759434587, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\AppendVector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759434179, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759435467, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759436021, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759436528, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759437133, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759437697, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759438234, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759438766, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759439337, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759439884, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759440437, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759441007, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759441771, "dur": 866, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\LongInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759441562, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759443119, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759443772, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759444345, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759444900, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759445491, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759445629, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759446348, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Attributes\\PostProcessAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759446858, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\ScreenSpaceReflections.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759447079, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Monitors\\HistogramMonitor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759447963, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Utils\\TargetPool.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759448167, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Utils\\XRSettings.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748345759445696, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759448226, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759448581, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759448837, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Postprocessing.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748345759448660, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759449141, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759449916, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759450044, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759450614, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759451180, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759451747, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759452472, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759453108, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759453276, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759453761, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759453911, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759454355, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759454755, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759454948, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759456263, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759456674, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759456839, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759457560, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759457695, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759457864, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759458476, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759458590, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748345759458751, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759459213, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759459327, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759459676, "dur": 51563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759511241, "dur": 1984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759513226, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759515271, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759513328, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759515396, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759515464, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759517864, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759518034, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759519372, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759517954, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759519969, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759520040, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748345759521955, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759522246, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759522416, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748345759522414, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748345759522829, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748345759523257, "dur": 53199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748345759576457, "dur": 4710216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759402459, "dur": 23626, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759426105, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759426194, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748345759426091, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759426554, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759426552, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_94A46A2A182BAD18.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759426834, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759427572, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759428286, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759428373, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759428473, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759428601, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759428823, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759428880, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759429521, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759429714, "dur": 755, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759430516, "dur": 718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759431630, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759431686, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759431743, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759431905, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759432079, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759432133, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759432250, "dur": 1181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759434201, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748345759434958, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748345759427721, "dur": 8708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759436430, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759436573, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759437147, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759437670, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759438190, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759438730, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759439281, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759439835, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759440392, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759440959, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759441538, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759442125, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759442698, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759443266, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759443826, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759444376, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759444936, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759446883, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759447077, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759447770, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759448109, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759448176, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759448478, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759449031, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759449617, "dur": 2747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Utilities\\CoreMatrixUtils.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748345759449149, "dur": 3333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759452483, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759453106, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759453302, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759453994, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759454150, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759454361, "dur": 2473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759456835, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748345759456963, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759457280, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759457377, "dur": 2285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759459663, "dur": 51559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759511224, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759513216, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759513419, "dur": 2084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759515504, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759515633, "dur": 1569, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759515582, "dur": 3426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759519009, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759519374, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759519117, "dur": 1965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759521083, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748345759521216, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759522405, "dur": 677, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.JSInterop.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748345759521194, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748345759523858, "dur": 4762761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759402410, "dur": 23654, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759426074, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759426195, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748345759426261, "dur": 592, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748345759426185, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_934EE5D4B70C0964.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748345759427032, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748345759427031, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C8C0272C381EE5BB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748345759427732, "dur": 522, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748345759428272, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759428337, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759428473, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748345759429174, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759429894, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759430473, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759431029, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759431684, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759432263, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759432889, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759433607, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759434156, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759434752, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759435669, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759436302, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759436873, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759437681, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759438293, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759438878, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759439587, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759440306, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759440931, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759441558, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759442182, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759442778, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759443518, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759444094, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759444652, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759445239, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748345759445441, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759446030, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759446232, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759446332, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748345759446541, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759446643, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748345759447587, "dur": 408, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.vscode@1.2.5\\Editor\\ProjectGeneration\\AssemblyNameProvider.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748345759446895, "dur": 1124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759448020, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759448145, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748345759448144, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748345759448336, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748345759448335, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748345759448559, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759448638, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759449342, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759450106, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759450712, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759451336, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759452386, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759453185, "dur": 1182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759454367, "dur": 2474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759456841, "dur": 2817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759459664, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748345759459895, "dur": 51335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759511234, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759513236, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759513321, "dur": 1193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759515271, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpOverrides.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748345759514518, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759516564, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759516645, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759518485, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759518559, "dur": 787, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759519373, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748345759519349, "dur": 2093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759521443, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759521523, "dur": 1954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748345759523477, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748345759523581, "dur": 4763019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759402375, "dur": 23674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759426070, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759426190, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748345759426262, "dur": 324, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748345759426180, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C1B7931C83CAC136.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748345759426932, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748345759426930, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_847CCDCB962CD0E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748345759427418, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748345759427554, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759427825, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748345759428209, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748345759428397, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748345759428824, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5068094731376506261.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748345759428887, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759429043, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6468990220793547653.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748345759429095, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759429214, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759429287, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759430182, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759430747, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759431299, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759431882, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759432301, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759432939, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759433503, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759434054, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759435311, "dur": 2479, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\GraphView\\Elements\\Controllers\\VFXGroupNodeController.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748345759434799, "dur": 3325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759438124, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759438735, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759439333, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759439913, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759440468, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759441011, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759441582, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759442540, "dur": 1295, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\QuaternionPropertyDrawer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748345759442156, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759443977, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759445372, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UI\\UnityOnScrollbarValueChangedMessageListener.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748345759444628, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759447100, "dur": 505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_Dropdown.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748345759446005, "dur": 2821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759448827, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759448960, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748345759449144, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759449806, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759449936, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759450532, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759451094, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759451651, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759452384, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759453190, "dur": 1160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759454351, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748345759454540, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759455022, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759455170, "dur": 1662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759456850, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748345759457044, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759457920, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759458069, "dur": 1601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759459670, "dur": 51565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759511236, "dur": 1985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759513223, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759513313, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759515277, "dur": 695, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748345759513525, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759516237, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759519372, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748345759516307, "dur": 3691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759519999, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759521449, "dur": 777, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748345759520271, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748345759523055, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759523242, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748345759523591, "dur": 4763006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759402485, "dur": 23612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759426112, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759426189, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748345759426102, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4C09D1522AE95AC4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759426531, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759426529, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C4EA87A8705F2EB3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759427034, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759427138, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_F13D2A3973149590.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759427193, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759427365, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748345759427585, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748345759427745, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759427825, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759427929, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759428020, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759428098, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759428246, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759428421, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748345759428651, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759429207, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5331522906421752218.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748345759429394, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759430154, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759430718, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759431271, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759431881, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759432397, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759433099, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759433651, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759434213, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759434772, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759435704, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759436287, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759436867, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759437466, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759438085, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759438628, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759439189, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759439737, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759440298, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759440866, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759441417, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759441973, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759442627, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759443220, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759444132, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759444717, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759445447, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759445630, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759446208, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759446350, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759446533, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759446703, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759446893, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759447512, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759448084, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759448198, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\ISpriteEditorModule.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748345759447603, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759448373, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759448479, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759448842, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759450288, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759450974, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759451718, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759452363, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759453169, "dur": 1225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759454395, "dur": 2441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759456836, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759457410, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759457544, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759457604, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759458101, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759458298, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748345759458457, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759458793, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759458931, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759459677, "dur": 52348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759512027, "dur": 1853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759513881, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759515705, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759513975, "dur": 2238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759516215, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759516302, "dur": 2164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759518467, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759519373, "dur": 868, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.IISIntegration.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759520329, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759520770, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345759518575, "dur": 3009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748345759521585, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759521933, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759522386, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759522648, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759523215, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345759523334, "dur": 4535360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748345764058697, "dur": 226593, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345764058696, "dur": 226597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748345764285318, "dur": 1241, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748345759402521, "dur": 23594, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759426132, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748345759426196, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748345759426121, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3B5B0D1C936F55AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748345759426535, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759427024, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748345759427022, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_0C0EDCF4D8373D10.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748345759427593, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759427684, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759428199, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748345759428698, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759429210, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759429948, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759430609, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759431224, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759431808, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759432403, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759433007, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759433626, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759434209, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759434791, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759435719, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759436251, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759436761, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759437349, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759437887, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759438420, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759438968, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759439529, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759440071, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759440624, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759441176, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759441741, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759442313, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759442846, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759443378, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759443938, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759444523, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759445078, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759446135, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748345759446915, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetMenu\\AssetMenuItems.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759447296, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\Cache\\RemoteStatusCache.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759447423, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetsUtils\\GetSelectedPaths.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759447581, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetsUtils\\Processor\\AssetsProcessor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759448128, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\CloudEdition\\Welcome\\WaitingSignInPanel.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759448282, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\CredentialsUIImpl.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759448923, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\GetRelativePath.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759449605, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\PlasticNotification.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759449783, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Preferences\\DiffAndMergePreferencesFoldout.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759450214, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Tool\\IsExeAvailable.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759452081, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\UIElements\\ProgressControlsForDialogs.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759452448, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\UnityEvents.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748345759446304, "dur": 6444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759452749, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759452888, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748345759453043, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759453458, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759453560, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759454379, "dur": 2458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759456837, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759457703, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748345759457876, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759458263, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759458368, "dur": 1297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759459665, "dur": 51554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759511235, "dur": 1983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759513218, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759514497, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748345759513320, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759515587, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759515710, "dur": 2019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759517730, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759519380, "dur": 1844, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748345759518109, "dur": 3948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748345759522058, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759522218, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748345759522656, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759522857, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759522931, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759523002, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759523076, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Google.Android.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748345759523251, "dur": 49879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759573132, "dur": 3318, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748345759576451, "dur": 4710150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759402548, "dur": 23584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759426150, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748345759426236, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748345759426138, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_61E29B66A87F06EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748345759426529, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759427037, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759428001, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748345759428414, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748345759429219, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759429925, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759430492, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759431058, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759431641, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759432231, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759432943, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759433778, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759434325, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759434893, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759435767, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759436766, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759437712, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759438255, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759439074, "dur": 3728, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\ShaderPort.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759442979, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\PropertySheet.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759438773, "dur": 4965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759443738, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759444341, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759444949, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759445604, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748345759446607, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759446721, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759446912, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759447111, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double2x3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759447550, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2x4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759448119, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int2x2.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759448303, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int3x3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759448362, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int3x4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759448417, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759448537, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x2.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748345759445790, "dur": 4025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759449816, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759449959, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748345759450174, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759450689, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759450817, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759451394, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759451487, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759452357, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759453180, "dur": 1172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759454353, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748345759454723, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748345759454536, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759455165, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759455331, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759456853, "dur": 2803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759459657, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748345759459879, "dur": 53412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759513293, "dur": 1843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759515136, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759515197, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759515634, "dur": 2585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748345759519110, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748345759519372, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Channels.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748345759515251, "dur": 5013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759520265, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759520389, "dur": 1921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748345759522311, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759522403, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759522564, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759522642, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759522768, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759522882, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759523072, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748345759523247, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748345759523873, "dur": 4762725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759402728, "dur": 23418, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759426161, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759426240, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748345759426150, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4764A97CA20C124C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759426865, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759427020, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759427119, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B7E76160FABBEBBF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759427205, "dur": 352, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B7E76160FABBEBBF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759427572, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428237, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428290, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428465, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428619, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428674, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428726, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759428817, "dur": 548, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759429523, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759429698, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759431319, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759431733, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759432069, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759432592, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759432980, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759433047, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759433125, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759434198, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759434951, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\UnityTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759435939, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759436088, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759427731, "dur": 9205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759436937, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759437045, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759437111, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759442362, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\Message.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759442579, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolListener.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759437305, "dur": 5464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759442770, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759442853, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759443062, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759443256, "dur": 1665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759444922, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759445095, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759446103, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\OneModifierComposite.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759446617, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionReference.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759447113, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759447166, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\AnyKeyControl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759447229, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\AxisControl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759447410, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DeltaControl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759447524, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DiscreteButtonControl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759447715, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759448099, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlLayoutChange.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759448219, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlList.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759448326, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlPath.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759448477, "dur": 1172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\AxisDeadzoneProcessor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759449916, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\NormalizeVector3Processor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759449989, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\ScaleProcessor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759450061, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\ScaleVector2Processor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759450132, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\ScaleVector3Processor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759450203, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\StickDeadzoneProcessor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759450287, "dur": 1399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\QuaternionControl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759451883, "dur": 516, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\SetIMECursorPositionCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748345759445298, "dur": 7632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759452931, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759453101, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759453266, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759454557, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759454743, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759454901, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759456097, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759456217, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759456328, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759456725, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759456833, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759456954, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759457308, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759457404, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759457516, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759457937, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759458050, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759458159, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759458471, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759458557, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748345759458689, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759459010, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759459110, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759459674, "dur": 51553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759511229, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759513251, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759513362, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759515229, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759515389, "dur": 1841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759517231, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759519362, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759517328, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759519595, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759520288, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.ExtensionMethods.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759521827, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759519689, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748345759521914, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759522069, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759522215, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748345759522398, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759522646, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759523136, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759523234, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748345759523461, "dur": 4763165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759402729, "dur": 23423, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759426207, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748345759426157, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_91C7B02DD7E937E1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759426512, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759427013, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759427115, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748345759427114, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_140A21A116E2DF85.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759427283, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1748345759427375, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759427445, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759427704, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1748345759428151, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748345759428410, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1748345759428713, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748345759428795, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748345759429221, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759430184, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759430777, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759431331, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759432095, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759432991, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759433555, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759434140, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759435340, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759436135, "dur": 1219, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Decal\\DBuffer\\DBufferRenderPass.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748345759435888, "dur": 1921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759437810, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759438514, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759439074, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759439624, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759440180, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759440729, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759441278, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759441833, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759442599, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Analytics\\AnalyticsUtils.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748345759442570, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759443658, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759444296, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759444920, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759445642, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759445820, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759446036, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759446576, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759446709, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759446883, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759447152, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759447712, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759448141, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748345759448335, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748345759447840, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759448802, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759448946, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759449568, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759450278, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759450833, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759451468, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759451741, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759452541, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759453113, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759453279, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759453750, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759453841, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759454355, "dur": 1868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759456224, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748345759456353, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759456707, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759456848, "dur": 2815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759459664, "dur": 51560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759511226, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759513212, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759513281, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759513339, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759515211, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759515633, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748345759515319, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759517177, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759518034, "dur": 535, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748345759519374, "dur": 1565, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748345759517261, "dur": 3916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759521178, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759521281, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748345759523178, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345759523310, "dur": 2951544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748345762474856, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748345762474855, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748345762475219, "dur": 3099, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748345762478322, "dur": 1808303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759402784, "dur": 23381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759426180, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426257, "dur": 345, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426170, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1D4B133AF68A73C6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426702, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759426772, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426770, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6D1F6C0CAAFE17A8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426835, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759426904, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426902, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4D8A662031E48D37.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759426993, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759427214, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759427212, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759428306, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759429213, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759429986, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759430580, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759431145, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759431730, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759432311, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759432978, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759433602, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759434169, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759434725, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759435602, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759436161, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759436775, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759437743, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759438310, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759438830, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759439377, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759440138, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759440686, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759441239, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759441808, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759442408, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759443132, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759443755, "dur": 1099, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\CustomEventArgs.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748345759443754, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759445422, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759445842, "dur": 1055, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759447141, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759445623, "dur": 1710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759447333, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759448140, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345759447489, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759448460, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759448628, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759449544, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759450116, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759450776, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759451349, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759451824, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759452533, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759453103, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759453283, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759454154, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759454350, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759454529, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759455098, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759455254, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759455425, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759456789, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759456925, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759457039, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759458383, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759458507, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759458682, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759459534, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759459655, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748345759459827, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759460155, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759460218, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345759461433, "dur": 116, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345759461619, "dur": 1747808, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345761220594, "dur": 1266118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345761220283, "dur": 1266589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345762487324, "dur": 89, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345762487429, "dur": 435332, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748345762928299, "dur": 1047559, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748345762928297, "dur": 1048869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748345763978192, "dur": 151, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748345763978360, "dur": 67532, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748345764058689, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748345764058687, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748345764058888, "dur": 1016, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748345764059908, "dur": 226710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759402833, "dur": 23369, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759426257, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748345759426203, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5AD924FEB2B8122.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748345759427030, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345759427028, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0D59EE4A35C68DB6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748345759428067, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748345759428413, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1748345759428477, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759428939, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759429253, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759429944, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759430514, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759431090, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759431659, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759432251, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759432807, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759433402, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\Renderer2DDataEditor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748345759433374, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759434764, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759435661, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759436209, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759436742, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759437308, "dur": 2476, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Runtime\\Utilities\\PropertyBinding\\Implementation\\VFXPlaneBinder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748345759437279, "dur": 3016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759440296, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759440828, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759441384, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759442011, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759442552, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759443153, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759444223, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759444769, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759445361, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748345759445552, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759447559, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Playables\\NotificationFlags.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748345759448056, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\TrackAsset.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748345759448348, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Utilities\\NotificationUtilities.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748345759445627, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759448576, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759448725, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748345759448916, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759449971, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759450128, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759450273, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759450873, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759451521, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759452351, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759453174, "dur": 1214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759454389, "dur": 2468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759456858, "dur": 2809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759459668, "dur": 51570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759511238, "dur": 1983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759513223, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759513332, "dur": 1881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759515214, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759515577, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345759515435, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759517491, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759517658, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345759519373, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345759517583, "dur": 2063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759519647, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759521216, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345759519720, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345759521711, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759521933, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759522018, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759522138, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759522236, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759522337, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759522781, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759522906, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759523110, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759523221, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345759523287, "dur": 1697000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345761220291, "dur": 1093753, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345761220289, "dur": 1095113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345762316641, "dur": 154, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748345762316850, "dur": 144510, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748345762474834, "dur": 157000, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345762474833, "dur": 157002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345762631859, "dur": 1471, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748345762633334, "dur": 1653297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748345764296257, "dur": 2940, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3416, "tid": 236, "ts": 1748345764318790, "dur": 2702, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3416, "tid": 236, "ts": 1748345764321723, "dur": 2433, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3416, "tid": 236, "ts": 1748345764310534, "dur": 14883, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}