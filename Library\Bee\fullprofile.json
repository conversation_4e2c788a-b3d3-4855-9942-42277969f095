{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3416, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3416, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3416, "tid": 3450, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3416, "tid": 3450, "ts": 1748358899632172, "dur": 19, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3416, "tid": 3450, "ts": 1748358899632214, "dur": 10, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3416, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3416, "tid": 1, "ts": 1748358896338383, "dur": 2838, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748358896341232, "dur": 94363, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3416, "tid": 1, "ts": 1748358896435600, "dur": 93525, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3416, "tid": 3450, "ts": 1748358899632227, "dur": 32, "ph": "X", "name": "", "args": {}}, {"pid": 3416, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896338312, "dur": 15586, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896353901, "dur": 3277458, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896353920, "dur": 74, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896354000, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896354005, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896354418, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896354424, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896354472, "dur": 10, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896354485, "dur": 3763, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358266, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358276, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358396, "dur": 8, "ph": "X", "name": "ProcessMessages 1565", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358408, "dur": 76, "ph": "X", "name": "ReadAsync 1565", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358491, "dur": 6, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358500, "dur": 65, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358573, "dur": 6, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358583, "dur": 50, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358638, "dur": 4, "ph": "X", "name": "ProcessMessages 1208", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358646, "dur": 47, "ph": "X", "name": "ReadAsync 1208", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358699, "dur": 3, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358706, "dur": 84, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358796, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358802, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358856, "dur": 4, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358865, "dur": 40, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358909, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358916, "dur": 53, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358974, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896358980, "dur": 43, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359027, "dur": 4, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359035, "dur": 54, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359095, "dur": 4, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359101, "dur": 40, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359146, "dur": 4, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359153, "dur": 53, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359211, "dur": 4, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359218, "dur": 64, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359290, "dur": 5, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359300, "dur": 55, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359361, "dur": 3, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359367, "dur": 54, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359427, "dur": 4, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359435, "dur": 49, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359489, "dur": 5, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359497, "dur": 49, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359552, "dur": 4, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359559, "dur": 57, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359623, "dur": 5, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359632, "dur": 49, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359686, "dur": 5, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359694, "dur": 49, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359749, "dur": 4, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359756, "dur": 51, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359812, "dur": 3, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359818, "dur": 37, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359860, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359866, "dur": 41, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359911, "dur": 4, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359919, "dur": 40, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359963, "dur": 3, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896359969, "dur": 42, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360016, "dur": 3, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360021, "dur": 57, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360086, "dur": 5, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360094, "dur": 75, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360174, "dur": 5, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360182, "dur": 79, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360266, "dur": 6, "ph": "X", "name": "ProcessMessages 1484", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360276, "dur": 36, "ph": "X", "name": "ReadAsync 1484", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360316, "dur": 3, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360322, "dur": 47, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360375, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360382, "dur": 48, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360435, "dur": 3, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360439, "dur": 44, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360489, "dur": 4, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360496, "dur": 59, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360564, "dur": 5, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360573, "dur": 67, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360645, "dur": 6, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360654, "dur": 56, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360716, "dur": 4, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360723, "dur": 49, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360776, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360781, "dur": 50, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360835, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360840, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360888, "dur": 4, "ph": "X", "name": "ProcessMessages 1211", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360896, "dur": 29, "ph": "X", "name": "ReadAsync 1211", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360928, "dur": 3, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360934, "dur": 29, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360967, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896360973, "dur": 31, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361008, "dur": 3, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361014, "dur": 38, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361072, "dur": 5, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361080, "dur": 62, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361147, "dur": 5, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361155, "dur": 59, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361223, "dur": 4, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361231, "dur": 56, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361293, "dur": 4, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361301, "dur": 63, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361372, "dur": 5, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361383, "dur": 43, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361431, "dur": 4, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361438, "dur": 47, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361493, "dur": 4, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361501, "dur": 70, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361579, "dur": 6, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361588, "dur": 66, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361661, "dur": 6, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361670, "dur": 38, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361712, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361716, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361748, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361753, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361784, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361789, "dur": 48, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361842, "dur": 3, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361848, "dur": 57, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361911, "dur": 5, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361920, "dur": 48, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361972, "dur": 4, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896361979, "dur": 54, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362040, "dur": 4, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362046, "dur": 50, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362101, "dur": 3, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362106, "dur": 43, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362153, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362158, "dur": 40, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362202, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362205, "dur": 39, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362250, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362257, "dur": 53, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362317, "dur": 5, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362325, "dur": 62, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362392, "dur": 5, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362400, "dur": 32, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362436, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362442, "dur": 47, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362494, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362500, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362553, "dur": 4, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362560, "dur": 35, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362599, "dur": 4, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362606, "dur": 30, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362640, "dur": 3, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362646, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362680, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362685, "dur": 28, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362716, "dur": 3, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362722, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362753, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362758, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362787, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362792, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362819, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362823, "dur": 49, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362880, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362886, "dur": 57, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362952, "dur": 5, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896362960, "dur": 55, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363023, "dur": 5, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363033, "dur": 54, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363094, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363099, "dur": 51, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363154, "dur": 3, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363159, "dur": 41, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363205, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363209, "dur": 49, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363266, "dur": 4, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363274, "dur": 56, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363337, "dur": 3, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363344, "dur": 59, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363410, "dur": 5, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363418, "dur": 52, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363476, "dur": 5, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363484, "dur": 53, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363543, "dur": 5, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363552, "dur": 53, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363611, "dur": 4, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363619, "dur": 49, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363674, "dur": 4, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363681, "dur": 56, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363743, "dur": 5, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363753, "dur": 48, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363807, "dur": 4, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363815, "dur": 53, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363874, "dur": 5, "ph": "X", "name": "ProcessMessages 1332", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363882, "dur": 51, "ph": "X", "name": "ReadAsync 1332", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363939, "dur": 5, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896363947, "dur": 48, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364001, "dur": 4, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364009, "dur": 53, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364067, "dur": 5, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364076, "dur": 49, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364131, "dur": 5, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364140, "dur": 56, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364202, "dur": 5, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364210, "dur": 57, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364273, "dur": 5, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364281, "dur": 58, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364343, "dur": 3, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364348, "dur": 52, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364406, "dur": 4, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364415, "dur": 56, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364478, "dur": 5, "ph": "X", "name": "ProcessMessages 1091", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364486, "dur": 45, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364537, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364544, "dur": 128, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364677, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364683, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364744, "dur": 5, "ph": "X", "name": "ProcessMessages 1203", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364753, "dur": 51, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364809, "dur": 5, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364818, "dur": 39, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364861, "dur": 3, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364867, "dur": 48, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364921, "dur": 36, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896364966, "dur": 75, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365048, "dur": 8, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365060, "dur": 74, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365139, "dur": 3, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365144, "dur": 54, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365208, "dur": 5, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365216, "dur": 62, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365283, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365289, "dur": 57, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365354, "dur": 5, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365363, "dur": 89, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365461, "dur": 5, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365470, "dur": 60, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365537, "dur": 4, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365543, "dur": 45, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365593, "dur": 3, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365598, "dur": 45, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365646, "dur": 2, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365651, "dur": 37, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365693, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365697, "dur": 43, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365744, "dur": 3, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365750, "dur": 50, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365809, "dur": 6, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365818, "dur": 62, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365888, "dur": 4, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365894, "dur": 54, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365955, "dur": 3, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896365961, "dur": 74, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366042, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366047, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366110, "dur": 3, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366116, "dur": 46, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366167, "dur": 4, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366174, "dur": 50, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366228, "dur": 2, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366233, "dur": 52, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366294, "dur": 6, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366304, "dur": 57, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366367, "dur": 6, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366377, "dur": 52, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366433, "dur": 4, "ph": "X", "name": "ProcessMessages 1191", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366440, "dur": 49, "ph": "X", "name": "ReadAsync 1191", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366496, "dur": 4, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366502, "dur": 50, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366556, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366561, "dur": 47, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366612, "dur": 3, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366618, "dur": 47, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366669, "dur": 3, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366674, "dur": 48, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366730, "dur": 6, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366740, "dur": 64, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366811, "dur": 6, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366820, "dur": 41, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366865, "dur": 5, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366873, "dur": 57, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366935, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366942, "dur": 48, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896366995, "dur": 5, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367003, "dur": 55, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367063, "dur": 4, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367071, "dur": 63, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367139, "dur": 4, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367146, "dur": 67, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367220, "dur": 4, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367227, "dur": 56, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367289, "dur": 5, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367298, "dur": 65, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367369, "dur": 5, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367377, "dur": 59, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367442, "dur": 5, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367450, "dur": 56, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367514, "dur": 5, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367523, "dur": 64, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367594, "dur": 6, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367603, "dur": 65, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367676, "dur": 5, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367685, "dur": 74, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367768, "dur": 6, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367778, "dur": 64, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367850, "dur": 6, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367859, "dur": 59, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367925, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896367934, "dur": 66, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368009, "dur": 5, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368018, "dur": 75, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368100, "dur": 5, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368109, "dur": 67, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368183, "dur": 4, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368191, "dur": 73, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368271, "dur": 5, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368280, "dur": 65, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368350, "dur": 4, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368357, "dur": 51, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368413, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368420, "dur": 53, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368477, "dur": 4, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368484, "dur": 61, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368551, "dur": 4, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368559, "dur": 59, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368626, "dur": 5, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368635, "dur": 98, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368741, "dur": 5, "ph": "X", "name": "ProcessMessages 1525", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368748, "dur": 55, "ph": "X", "name": "ReadAsync 1525", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368807, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368812, "dur": 37, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368853, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368856, "dur": 42, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368903, "dur": 2, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368908, "dur": 54, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368966, "dur": 3, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896368971, "dur": 40, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369014, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369019, "dur": 43, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369065, "dur": 2, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369069, "dur": 46, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369121, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369128, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369184, "dur": 2, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369189, "dur": 53, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369248, "dur": 3, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369255, "dur": 37, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369297, "dur": 4, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369304, "dur": 65, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369376, "dur": 4, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369383, "dur": 66, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369458, "dur": 4, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369465, "dur": 61, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369533, "dur": 6, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369542, "dur": 56, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369604, "dur": 3, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369611, "dur": 60, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369679, "dur": 4, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369686, "dur": 70, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369764, "dur": 5, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369772, "dur": 71, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369848, "dur": 4, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369856, "dur": 61, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369923, "dur": 5, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896369931, "dur": 75, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370015, "dur": 5, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370024, "dur": 66, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370097, "dur": 6, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370106, "dur": 65, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370177, "dur": 12, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370193, "dur": 73, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370272, "dur": 5, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370281, "dur": 78, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370367, "dur": 6, "ph": "X", "name": "ProcessMessages 1288", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370376, "dur": 75, "ph": "X", "name": "ReadAsync 1288", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370460, "dur": 6, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370470, "dur": 68, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370547, "dur": 6, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370558, "dur": 68, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370635, "dur": 6, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370645, "dur": 64, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370718, "dur": 5, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370727, "dur": 88, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370824, "dur": 4, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370832, "dur": 76, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370915, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370921, "dur": 41, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896370968, "dur": 5, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371454, "dur": 117, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371580, "dur": 16, "ph": "X", "name": "ProcessMessages 4518", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371600, "dur": 76, "ph": "X", "name": "ReadAsync 4518", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371682, "dur": 4, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371689, "dur": 64, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371761, "dur": 5, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371770, "dur": 66, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371843, "dur": 4, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371850, "dur": 79, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371938, "dur": 5, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896371947, "dur": 76, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372032, "dur": 6, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372041, "dur": 83, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372132, "dur": 4, "ph": "X", "name": "ProcessMessages 1145", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372139, "dur": 60, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372206, "dur": 4, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372214, "dur": 74, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372295, "dur": 5, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372304, "dur": 65, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372376, "dur": 3, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372383, "dur": 60, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372450, "dur": 5, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372492, "dur": 70, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372569, "dur": 4, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372576, "dur": 57, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372639, "dur": 4, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372646, "dur": 62, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372714, "dur": 4, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372721, "dur": 34, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372759, "dur": 3, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372766, "dur": 62, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372836, "dur": 4, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372844, "dur": 62, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372913, "dur": 5, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372922, "dur": 69, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896372997, "dur": 5, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373004, "dur": 58, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373067, "dur": 3, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373075, "dur": 46, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373126, "dur": 4, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373132, "dur": 61, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373200, "dur": 4, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373207, "dur": 71, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373286, "dur": 6, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373296, "dur": 65, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373368, "dur": 4, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373375, "dur": 51, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373433, "dur": 5, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373441, "dur": 66, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373513, "dur": 4, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373520, "dur": 67, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373594, "dur": 4, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373603, "dur": 65, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373673, "dur": 3, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373680, "dur": 71, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373758, "dur": 4, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373765, "dur": 124, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373898, "dur": 6, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373908, "dur": 49, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373963, "dur": 4, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896373971, "dur": 58, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374038, "dur": 3, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374044, "dur": 66, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374116, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374123, "dur": 62, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374190, "dur": 5, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374199, "dur": 66, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374271, "dur": 4, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374278, "dur": 55, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374339, "dur": 3, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374345, "dur": 65, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374416, "dur": 4, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374423, "dur": 65, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374494, "dur": 4, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374501, "dur": 69, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374579, "dur": 5, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374588, "dur": 65, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374662, "dur": 6, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374673, "dur": 73, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374752, "dur": 4, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374761, "dur": 76, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374843, "dur": 5, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374851, "dur": 59, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374915, "dur": 5, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374923, "dur": 53, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374984, "dur": 4, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896374992, "dur": 55, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375056, "dur": 4, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375064, "dur": 44, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375112, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375118, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375174, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375180, "dur": 59, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375244, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375250, "dur": 51, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375306, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375312, "dur": 110, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375427, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375432, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375497, "dur": 4, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375503, "dur": 43, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375551, "dur": 3, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375557, "dur": 149, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375712, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375717, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375790, "dur": 5, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375798, "dur": 58, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375862, "dur": 4, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375869, "dur": 63, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375937, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896375942, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376008, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376014, "dur": 59, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376079, "dur": 4, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376086, "dur": 47, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376143, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376258, "dur": 6, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376269, "dur": 75, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376351, "dur": 4, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376358, "dur": 74, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376438, "dur": 5, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376447, "dur": 38, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376490, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376496, "dur": 86, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376588, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376593, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376649, "dur": 5, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376657, "dur": 82, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376748, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376754, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376832, "dur": 4, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376839, "dur": 66, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376913, "dur": 5, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376922, "dur": 37, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376964, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896376968, "dur": 36, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377009, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377015, "dur": 52, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377074, "dur": 3, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377080, "dur": 46, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377131, "dur": 4, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377138, "dur": 56, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377200, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377206, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377258, "dur": 4, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377265, "dur": 55, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377325, "dur": 3, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377332, "dur": 50, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377391, "dur": 4, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377399, "dur": 57, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377462, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377468, "dur": 43, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377515, "dur": 4, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377523, "dur": 48, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377578, "dur": 4, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377586, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377626, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377631, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377706, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377712, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377776, "dur": 4, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377784, "dur": 42, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377831, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377838, "dur": 65, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377910, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377916, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377982, "dur": 5, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896377990, "dur": 36, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378031, "dur": 3, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378037, "dur": 81, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378128, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378135, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378205, "dur": 6, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378214, "dur": 47, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378267, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378273, "dur": 56, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378335, "dur": 6, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378344, "dur": 56, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378409, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378415, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378485, "dur": 5, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378494, "dur": 66, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378567, "dur": 5, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378576, "dur": 96, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378681, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378687, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378769, "dur": 5, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378778, "dur": 59, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378847, "dur": 6, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378857, "dur": 75, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378940, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896378946, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379028, "dur": 5, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379037, "dur": 66, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379108, "dur": 5, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379116, "dur": 66, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379191, "dur": 5, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379200, "dur": 69, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379276, "dur": 5, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379284, "dur": 63, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379353, "dur": 3, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379360, "dur": 52, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379417, "dur": 2, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379422, "dur": 73, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379503, "dur": 6, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379513, "dur": 65, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379585, "dur": 4, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379593, "dur": 69, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379668, "dur": 3, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379677, "dur": 60, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379742, "dur": 5, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379755, "dur": 69, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379832, "dur": 5, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379843, "dur": 74, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379926, "dur": 6, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896379936, "dur": 71, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380014, "dur": 4, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380022, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380103, "dur": 7, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380114, "dur": 68, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380191, "dur": 5, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380200, "dur": 87, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380296, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380303, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380392, "dur": 6, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380402, "dur": 64, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380472, "dur": 3, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380478, "dur": 60, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380547, "dur": 4, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380556, "dur": 81, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380644, "dur": 5, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380652, "dur": 60, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380718, "dur": 3, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380725, "dur": 69, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380804, "dur": 5, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380813, "dur": 70, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380890, "dur": 5, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380900, "dur": 49, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380956, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896380962, "dur": 51, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381019, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381025, "dur": 65, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381097, "dur": 6, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381108, "dur": 52, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381168, "dur": 4, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381176, "dur": 94, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381280, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381286, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381378, "dur": 6, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381388, "dur": 65, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381460, "dur": 3, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381467, "dur": 63, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381536, "dur": 2, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381542, "dur": 76, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381623, "dur": 6, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381632, "dur": 95, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381736, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381743, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381824, "dur": 5, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381833, "dur": 71, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381912, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381918, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896381994, "dur": 5, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382004, "dur": 92, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382105, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382112, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382200, "dur": 6, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382210, "dur": 44, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382259, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382265, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382350, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382357, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382470, "dur": 5, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382479, "dur": 69, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382556, "dur": 5, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382565, "dur": 71, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382644, "dur": 3, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382650, "dur": 74, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382729, "dur": 5, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382737, "dur": 56, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382802, "dur": 4, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382810, "dur": 104, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382922, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382927, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896382995, "dur": 4, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383003, "dur": 57, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383065, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383070, "dur": 74, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383153, "dur": 5, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383163, "dur": 87, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383259, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383265, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383348, "dur": 5, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383357, "dur": 67, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383431, "dur": 4, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383440, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383509, "dur": 2, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383514, "dur": 62, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383585, "dur": 6, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383595, "dur": 69, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383671, "dur": 4, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383679, "dur": 62, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383749, "dur": 4, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383756, "dur": 67, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383832, "dur": 6, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383841, "dur": 51, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383898, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383904, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383972, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896383977, "dur": 88, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384072, "dur": 5, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384081, "dur": 60, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384149, "dur": 4, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384155, "dur": 57, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384217, "dur": 3, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384224, "dur": 48, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384279, "dur": 5, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384287, "dur": 41, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384333, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384337, "dur": 51, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384394, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384400, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384466, "dur": 4, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384473, "dur": 33, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384511, "dur": 3, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384518, "dur": 65, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384589, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384595, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384652, "dur": 4, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384660, "dur": 70, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384737, "dur": 5, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384745, "dur": 43, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384794, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384800, "dur": 111, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384919, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384925, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896384997, "dur": 4, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385005, "dur": 50, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385061, "dur": 5, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385070, "dur": 82, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385158, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385163, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385221, "dur": 3, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385226, "dur": 44, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385273, "dur": 3, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385280, "dur": 78, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385367, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385374, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385455, "dur": 5, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385464, "dur": 47, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385518, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385524, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385591, "dur": 4, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385598, "dur": 64, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385670, "dur": 5, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385679, "dur": 52, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385740, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385745, "dur": 65, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385816, "dur": 6, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385826, "dur": 49, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385881, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385887, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385958, "dur": 5, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896385967, "dur": 50, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386020, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386027, "dur": 46, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386078, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386085, "dur": 59, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386152, "dur": 5, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386161, "dur": 64, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386232, "dur": 2, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386237, "dur": 61, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386305, "dur": 5, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386314, "dur": 54, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386377, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386382, "dur": 57, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386447, "dur": 5, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386455, "dur": 61, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386522, "dur": 4, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386530, "dur": 43, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386580, "dur": 4, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386587, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386653, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386709, "dur": 3, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386714, "dur": 39, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386758, "dur": 3, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386763, "dur": 108, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386878, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386882, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386940, "dur": 3, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386945, "dur": 38, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386987, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896386993, "dur": 113, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387113, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387119, "dur": 158, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387285, "dur": 4, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387293, "dur": 63, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387364, "dur": 7, "ph": "X", "name": "ProcessMessages 1358", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387375, "dur": 67, "ph": "X", "name": "ReadAsync 1358", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387449, "dur": 3, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387456, "dur": 71, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387534, "dur": 5, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387542, "dur": 76, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387624, "dur": 5, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387633, "dur": 63, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387703, "dur": 5, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387711, "dur": 65, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387784, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387790, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387860, "dur": 4, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387868, "dur": 102, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387977, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896387982, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388057, "dur": 6, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388067, "dur": 70, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388145, "dur": 5, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388153, "dur": 75, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388238, "dur": 6, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388249, "dur": 46, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388300, "dur": 4, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388307, "dur": 78, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388394, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388401, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388467, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388476, "dur": 95, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388581, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388587, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388655, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388663, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388731, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388739, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388805, "dur": 5, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388814, "dur": 60, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388883, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388891, "dur": 55, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388955, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896388963, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389044, "dur": 5, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389052, "dur": 56, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389117, "dur": 6, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389126, "dur": 78, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389212, "dur": 5, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389220, "dur": 61, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389289, "dur": 7, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389299, "dur": 57, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389364, "dur": 5, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389373, "dur": 41, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389420, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389427, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389479, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389485, "dur": 55, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389549, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389559, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389622, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389631, "dur": 107, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389746, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389753, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389880, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389890, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389961, "dur": 8, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896389973, "dur": 60, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390042, "dur": 5, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390050, "dur": 59, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390118, "dur": 7, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390129, "dur": 58, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390194, "dur": 6, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390203, "dur": 59, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390271, "dur": 7, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390284, "dur": 42, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390332, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390340, "dur": 52, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390400, "dur": 6, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390410, "dur": 42, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390458, "dur": 5, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390467, "dur": 51, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390525, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390533, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390607, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390617, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390685, "dur": 5, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390693, "dur": 62, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390764, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390774, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390838, "dur": 5, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390847, "dur": 53, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390909, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390919, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390987, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896390996, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391064, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391072, "dur": 55, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391132, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391140, "dur": 50, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391198, "dur": 6, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391208, "dur": 58, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391274, "dur": 5, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391282, "dur": 88, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391378, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391386, "dur": 57, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391450, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391460, "dur": 57, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391526, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391536, "dur": 61, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391607, "dur": 6, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391617, "dur": 62, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391687, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391695, "dur": 56, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391757, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391765, "dur": 54, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391827, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391835, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391897, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391905, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391965, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896391974, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392023, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392029, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392088, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392097, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392172, "dur": 7, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392183, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392250, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392259, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392318, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392325, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392383, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392392, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392467, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392475, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392546, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392556, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392604, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392612, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392669, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392677, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392742, "dur": 5, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392750, "dur": 56, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392815, "dur": 5, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392824, "dur": 55, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392887, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392895, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392981, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896392987, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896393046, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896393053, "dur": 458, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896393523, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896393536, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896393608, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896393618, "dur": 2582, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896396209, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896396216, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896396288, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896396296, "dur": 8888, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896405200, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896405212, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896405276, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896405283, "dur": 1183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896406477, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896406484, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896406534, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896406541, "dur": 1407, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896407957, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896407964, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408033, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408042, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408154, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408161, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408206, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408213, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408320, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408327, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408369, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896408375, "dur": 6014, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414398, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414405, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414471, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414479, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414527, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414533, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414676, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414682, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414742, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896414750, "dur": 1708, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416474, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416538, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416545, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416704, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416710, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416752, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416758, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416920, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416925, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416977, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896416984, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417042, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417048, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417100, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417107, "dur": 217, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417330, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417334, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417362, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417367, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417506, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417513, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417554, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417560, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417664, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417670, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417709, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417715, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417765, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417771, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417823, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417829, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417867, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417872, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417955, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896417961, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418023, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418029, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418077, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418082, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418135, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418141, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418196, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418204, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418258, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418266, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418316, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418320, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418394, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418400, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418442, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418449, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418494, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418499, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418553, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418561, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418629, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418634, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418708, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418714, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418769, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418775, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418815, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418820, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418863, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418868, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896418998, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419003, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419058, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419068, "dur": 105, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419181, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419187, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419264, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419272, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419334, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419341, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419403, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419411, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419476, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419483, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419541, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419547, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419604, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419610, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419646, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419652, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419704, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419711, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419770, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419776, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419837, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419842, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419894, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419901, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419950, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896419956, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420084, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420089, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420118, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420124, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420381, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420386, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420428, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420434, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420483, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420490, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420716, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420722, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420787, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420794, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420870, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420877, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420937, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420945, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420982, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896420986, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421261, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421267, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421302, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421308, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421419, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421425, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421489, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421497, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421556, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421563, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421628, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421636, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421747, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421753, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421876, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421884, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421932, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896421938, "dur": 130, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422077, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422084, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422130, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422136, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422248, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422255, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422299, "dur": 7, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422309, "dur": 349, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422667, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422673, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422712, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896422717, "dur": 457, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423183, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423189, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423245, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423252, "dur": 392, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423651, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423656, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423720, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423727, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423865, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423872, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423918, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423924, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423977, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896423983, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424065, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424073, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424190, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424198, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424292, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424309, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424401, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424411, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424652, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424657, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424715, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424722, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424785, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424794, "dur": 114, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424916, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424923, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424970, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896424977, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425032, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425038, "dur": 533, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425580, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425586, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425624, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425629, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425697, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425702, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425749, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425754, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425807, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425814, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425877, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425886, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425941, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425947, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425989, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896425994, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426090, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426096, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426134, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426139, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426280, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426324, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426330, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426383, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426390, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426452, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426459, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426515, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426520, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426562, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426567, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426671, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426678, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426716, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426720, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426781, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426787, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426829, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426834, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426886, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896426894, "dur": 151, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427053, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427061, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427120, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427126, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427179, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427184, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427248, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427255, "dur": 141, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427404, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427411, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427469, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427476, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427541, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427549, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427634, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427640, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427705, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427713, "dur": 119, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427840, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427846, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427906, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896427913, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428011, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428016, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428082, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428090, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428157, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428163, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428204, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428211, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428409, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428415, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428475, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428484, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428548, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428555, "dur": 428, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428992, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896428998, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896429035, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896429039, "dur": 656, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896429704, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896429711, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896429776, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896429786, "dur": 49667, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896479466, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896479473, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896479536, "dur": 38, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896479577, "dur": 5902, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896485488, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896485495, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896485572, "dur": 5, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896485580, "dur": 67, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896485657, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896485665, "dur": 1775, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487449, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487455, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487518, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487525, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487585, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487592, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487672, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487678, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487716, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487722, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487831, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487837, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487880, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896487886, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488089, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488094, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488180, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488188, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488341, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488347, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488396, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896488403, "dur": 1057, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489469, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489475, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489516, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489522, "dur": 166, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489697, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489703, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489760, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489767, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489932, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489938, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489985, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896489990, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896490036, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896490042, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896490225, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896490230, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896490284, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896490291, "dur": 772, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491069, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491074, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491106, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491110, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491362, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491367, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491411, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491417, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491516, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491521, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491568, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491573, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491671, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491676, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491709, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491714, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491992, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896491997, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492035, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492041, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492234, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492240, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492287, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492294, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492347, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492354, "dur": 209, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492570, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492575, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492623, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492629, "dur": 295, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492933, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492939, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896492998, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493004, "dur": 398, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493411, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493417, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493472, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493480, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493518, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896493523, "dur": 884, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494413, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494419, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494453, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494458, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494538, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494544, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494597, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494603, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494650, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494656, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494711, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494719, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494756, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494761, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494796, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494801, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494850, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494858, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494915, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494922, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494974, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896494979, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495029, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495035, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495098, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495105, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495167, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495175, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495236, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495243, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495305, "dur": 4, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495313, "dur": 51, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495379, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495387, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495454, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495461, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495532, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495541, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495609, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495619, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495686, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495696, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495763, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495772, "dur": 55, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495835, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495845, "dur": 63, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495916, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495926, "dur": 59, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896495994, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496005, "dur": 57, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496070, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496079, "dur": 60, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496146, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496153, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496206, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496210, "dur": 306, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496523, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496528, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496585, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496592, "dur": 101, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496701, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496708, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496749, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358896496756, "dur": 1091639, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358897588411, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358897588422, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358897588461, "dur": 1098, "ph": "X", "name": "ProcessMessages 1921", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358897589566, "dur": 498225, "ph": "X", "name": "ReadAsync 1921", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898087807, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898087816, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898087897, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898087904, "dur": 243707, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898331629, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898331636, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898331680, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898331687, "dur": 60775, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898392479, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898392487, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898392527, "dur": 333, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898392866, "dur": 46566, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898439452, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898439464, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898439552, "dur": 51, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898439607, "dur": 13062, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898452685, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898452693, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898452755, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898452762, "dur": 1802, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898454572, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898454578, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898454633, "dur": 36, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898454671, "dur": 129093, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898583779, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898583787, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898583831, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898583838, "dur": 1263, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898585110, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898585116, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898585155, "dur": 46, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358898585207, "dur": 716514, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899301738, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899301747, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899301789, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899301797, "dur": 68341, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899370156, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899370166, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899370219, "dur": 41, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899370264, "dur": 14089, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899384371, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899384381, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899384428, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899384437, "dur": 1063, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899385513, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899385520, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899385594, "dur": 54, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899385655, "dur": 232230, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899617906, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899617916, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899617985, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899617996, "dur": 1034, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619044, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619053, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619138, "dur": 50, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619192, "dur": 500, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619699, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619704, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619768, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3416, "tid": 47244640256, "ts": 1748358899619775, "dur": 11574, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3416, "tid": 3450, "ts": 1748358899632262, "dur": 6963, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3416, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3416, "tid": 42949672960, "ts": 1748358896338255, "dur": 190899, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3416, "tid": 42949672960, "ts": 1748358896529157, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3416, "tid": 42949672960, "ts": 1748358896529162, "dur": 95, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3416, "tid": 3450, "ts": 1748358899639230, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3416, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3416, "tid": 38654705664, "ts": 1748358896335051, "dur": 3296358, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3416, "tid": 38654705664, "ts": 1748358896335182, "dur": 2170, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3416, "tid": 38654705664, "ts": 1748358899631415, "dur": 83, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3416, "tid": 38654705664, "ts": 1748358899631436, "dur": 31, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3416, "tid": 38654705664, "ts": 1748358899631500, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3416, "tid": 3450, "ts": 1748358899639252, "dur": 28, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748358896354455, "dur": 2554, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358896357022, "dur": 936, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358896358103, "dur": 83, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748358896358187, "dur": 473, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358896358832, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_140A21A116E2DF85.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748358896360272, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D361BA92457D64F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748358896370016, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748358896371273, "dur": 153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748358896371668, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748358896371994, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_D525208FA1B27961.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748358896372231, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748358896372774, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748358896374308, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748358896374450, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748358896375698, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748358896377747, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748358896381573, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15985480389207366606.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748358896381833, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10540497235457876305.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748358896382668, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5147072719652273855.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748358896382907, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748358896383410, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748358896385093, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Profiler.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748358896385441, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualEffectGraph.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748358896358688, "dur": 30210, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358896388920, "dur": 3230678, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358899619599, "dur": 214, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358899619813, "dur": 149, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358899620093, "dur": 73, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358899620194, "dur": 2782, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748358896358757, "dur": 30174, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896388957, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896389105, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896389091, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_61E29B66A87F06EA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896389158, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896389354, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896390069, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896390067, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896390239, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896390303, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896390580, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748358896391182, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748358896391726, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748358896391952, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896392786, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17206416036114848451.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748358896393297, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896394197, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896394761, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896395338, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896395931, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896396674, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896397604, "dur": 711, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896398316, "dur": 2484, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.Messages.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896396536, "dur": 4264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896400801, "dur": 4671, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\AppendVector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748358896400801, "dur": 5393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896406195, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896406901, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896407532, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896408126, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896408685, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896409357, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896409933, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896410488, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896411040, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896411600, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896412152, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896412720, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896413308, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896413904, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896414604, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896415373, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896415955, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896417199, "dur": 927, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\AssetImporter\\InputActionImporter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748358896416575, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896418127, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896419104, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896419219, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896419323, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896420074, "dur": 853, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896419507, "dur": 1597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896421105, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896421239, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896421427, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896421920, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896422522, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896422722, "dur": 1984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896424707, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896424855, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896425045, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896425154, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896426678, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896426832, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896427010, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896428013, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896428177, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748358896428341, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896428823, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896428920, "dur": 55180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896484103, "dur": 1884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896485988, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896488031, "dur": 687, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896486068, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896488741, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896489118, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896488837, "dur": 2132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896490970, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896491588, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896492829, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896491570, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896493819, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896495358, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896493907, "dur": 1941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748358896495849, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896496104, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748358896496321, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896496509, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748358896496601, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748358896496672, "dur": 3122900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896358810, "dur": 30141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896388964, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896389085, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3B5B0D1C936F55AC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896389360, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896389358, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C4EA87A8705F2EB3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896389657, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896390267, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896390264, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_F13D2A3973149590.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896390332, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896390479, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_F13D2A3973149590.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896390613, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748358896390825, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748358896390880, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896391484, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896391753, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896392085, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748358896392155, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896392250, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896392456, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748358896392507, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896392585, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896392714, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896392905, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896393282, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896394177, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896394879, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896395917, "dur": 983, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Examples\\8_VoxelMap\\Scripts\\VoxelData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748358896395456, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896396987, "dur": 3852, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCompression.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896396987, "dur": 4356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896401669, "dur": 2635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Blocks\\Implementations\\Collision\\CollisionCone.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748358896401344, "dur": 3159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896404503, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896405556, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896406088, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896406604, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896407165, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896407727, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896408269, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896408966, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896409521, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896410044, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896410562, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896411086, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896411591, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896412845, "dur": 5423, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarSum.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748358896412100, "dur": 6204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896418305, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896418492, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896418679, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896419206, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896419530, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896419347, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896420049, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896420288, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896420519, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896420595, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896421202, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896421292, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896421948, "dur": 2323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.RenderPipelines.Core.Runtime.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896421455, "dur": 2834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896424290, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896424476, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896424567, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896424641, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_F7477446ADE85C14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748358896424717, "dur": 2748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896427466, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896428784, "dur": 55353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896484140, "dur": 1724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896485866, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896486008, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896485968, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896488105, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896488831, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896488232, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896490617, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896490730, "dur": 1898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896492629, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896493448, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896492773, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748358896494962, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495209, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495351, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495447, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495534, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495644, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495724, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896495802, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896495979, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896496101, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748358896496199, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896496598, "dur": 38925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748358896535523, "dur": 3084044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896358893, "dur": 30081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896388995, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358896388980, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748358896389177, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896389367, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896389526, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1FF66EA0847C44F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748358896389648, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358896389646, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6D1F6C0CAAFE17A8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748358896389867, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E11C5609C2206DA8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748358896389967, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358896390108, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748358896389965, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_A57E8B70117326A4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748358896390238, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896391112, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748358896391232, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896391316, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748358896391592, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896391674, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748358896391847, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896392082, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896393286, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896394233, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896394862, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896395509, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896396152, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896396794, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896397815, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896398367, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896398981, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896399551, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896400114, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896400668, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896401213, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896401766, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896402476, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\UniversalAdditionalCameraData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748358896402330, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896403498, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896404494, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\TriggerStateTransition.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748358896404065, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896405195, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896405968, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896406887, "dur": 2270, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Activation\\ActivationTrackEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748358896406538, "dur": 2784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896409322, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896409914, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896410491, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896411120, "dur": 1444, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\BoltProduct.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748358896411060, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896413023, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896413542, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896414382, "dur": 2158, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\WatchedList.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748358896414087, "dur": 2664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896416752, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896417355, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748358896417551, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748358896418189, "dur": 901, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896419104, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 566, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\MapboxAccounts\\net35\\MapboxAccountsUnity.dll"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 2354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Trim\\TrimItemModeReplace.cs"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 4009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 3208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 1305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896428777, "dur": 55244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896484025, "dur": 1912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748358896485939, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896486038, "dur": 1809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748358896487854, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896488031, "dur": 3039, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358896492023, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.UserSecrets.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358896487950, "dur": 5353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748358896493304, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896493425, "dur": 1893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748358896495319, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896495778, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896495830, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748358896496295, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358896496481, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748358896496650, "dur": 2888000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748358899384653, "dur": 233662, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358899384652, "dur": 233665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748358899618342, "dur": 1189, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896358852, "dur": 30113, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896388993, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896388972, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896389160, "dur": 742, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896389155, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_91C7B02DD7E937E1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896389945, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896389943, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E194AA87B50D9488.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896390258, "dur": 392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E194AA87B50D9488.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896391171, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748358896391776, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748358896391965, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896392233, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748358896393697, "dur": 5160, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Core\\mapbox-sdk-cs\\Tests\\UnitTests\\Editor\\MapboxUnitTests_AngleSmoothing.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748358896393322, "dur": 5790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896399112, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896399765, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896400391, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896401024, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896401596, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896402484, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Expressions\\VFXExpressionAbstract.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748358896402130, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896403238, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896403813, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896404396, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896405082, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896405663, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896406245, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896406814, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896407476, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896408046, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896408666, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896409248, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896409806, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896410385, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896410961, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896411543, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896412095, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896412662, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896413249, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896413804, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896414393, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896415027, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896415600, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896416141, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896416754, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896417410, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896417601, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896417725, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896417841, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896418598, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896418699, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896418754, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896418935, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896419489, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896419771, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896419869, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896420066, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896420263, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896421140, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896421284, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896421463, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896422269, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896422414, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896422577, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896425109, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896425250, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896425416, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896426290, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896426421, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896426590, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896427244, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896427368, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748358896427545, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896428051, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896428179, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896428734, "dur": 55284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896484022, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896485910, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896487919, "dur": 4833, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896485993, "dur": 6766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896492760, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896493519, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896492841, "dur": 1970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896494812, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896494934, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748358896494908, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748358896497121, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748358896497255, "dur": 3122335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896358926, "dur": 30108, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896389055, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896389041, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4C09D1522AE95AC4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896389188, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1D4B133AF68A73C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896389361, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896389360, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_94A46A2A182BAD18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896389637, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896389737, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896390079, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896390243, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896390241, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2167DFA7B720B742.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896390485, "dur": 403, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2167DFA7B720B742.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896390901, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896391490, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896391644, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896391749, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896391801, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896392157, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896392347, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896392624, "dur": 5982, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896398791, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896398992, "dur": 2118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896401160, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896401270, "dur": 609, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896401881, "dur": 603, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896402486, "dur": 555, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896403043, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896404205, "dur": 1649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\UnityTestMethodCommand.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896406380, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896406455, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896391070, "dur": 17277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896408348, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896408465, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896408530, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896411113, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\TestListBuilder\\TestFilterSettings.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896412790, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\Tasks\\PrebuildSetupTask.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896414430, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\TestRunnerApiMapper.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896408827, "dur": 5961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896414789, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896414901, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896414965, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896416477, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\SpriteStateDrawer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896415181, "dur": 1674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896416856, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896417023, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896417895, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Composites\\Vector2Composite.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896418308, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionProperty.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896418916, "dur": 675, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\InputSystem.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748358896417206, "dur": 2676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896419883, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896420040, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896420352, "dur": 1798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896422150, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896422236, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896422401, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896422574, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896424060, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896424190, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896424362, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896425089, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896425261, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896425419, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896425959, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896426113, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896426272, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896426772, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896426902, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896427071, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896427592, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896427725, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896427900, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896428404, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896428564, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896428728, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748358896428916, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896429400, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896429486, "dur": 54717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896486021, "dur": 6727, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.IO.Compression\\net35\\Mapbox.IO.Compression.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896484207, "dur": 8587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896492795, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896493401, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896493081, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748358896495037, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896495251, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896495412, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896495793, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748358896495898, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896495966, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896496570, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748358896497077, "dur": 3122524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896358974, "dur": 30086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896389074, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896389065, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F372D6AB03EB9C8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896389137, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896389291, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896389289, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0CB3585AEAFFAB32.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896389853, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896390103, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896390610, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748358896390685, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748358896390786, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748358896391009, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748358896391278, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896391423, "dur": 79, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748358896391889, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896392572, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748358896393333, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896394229, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896394868, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896395502, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896396163, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896396795, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896397432, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896398025, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896398557, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896399198, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896399823, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896401034, "dur": 1237, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\OrientedBoxVolume.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748358896400508, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896402488, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Passes\\PostProcessPass.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748358896402385, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896403587, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896404583, "dur": 818, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\IStateTransitionDebugData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748358896404134, "dur": 1845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896405980, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896406529, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896407196, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896407800, "dur": 1412, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Util\\GradientUtil.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748358896407770, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896410104, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896410655, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896411207, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896411764, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896412325, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896412940, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896413494, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896414057, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896414666, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896415249, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896415824, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896416370, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896416973, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896417829, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896418010, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896418354, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896418905, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896419529, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896419032, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896419603, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896419818, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896419886, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896420709, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896420910, "dur": 1088, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896420884, "dur": 1747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896422632, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896422767, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896423655, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896424196, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896424366, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896425036, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896425271, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896425453, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896426265, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896426391, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896426455, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748358896426638, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896427162, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896427342, "dur": 1593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896428935, "dur": 55117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896484056, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896485959, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896487920, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896486081, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896488131, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896488338, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896490415, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896490504, "dur": 2138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896492643, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896492737, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896493990, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896492848, "dur": 1982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896494831, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896495570, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.Json\\Net35\\Mapbox.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748358896494912, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748358896496962, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748358896497068, "dur": 3122501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896359011, "dur": 30409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896389421, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_0CA0CC3598913E32.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748358896389853, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896389923, "dur": 335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748358896389921, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_86C139E5AAFE1760.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748358896390444, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896390507, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748358896390627, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748358896391265, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896391707, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896392086, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896392192, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896392335, "dur": 1659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896394005, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896394617, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896395201, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896395768, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896396373, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896397091, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896397677, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896398218, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896398822, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896399399, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896399956, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896400549, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896401146, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896401685, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896402486, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Controls\\VFXVector3Field.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896402217, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896403361, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896403919, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896404469, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896405032, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896405634, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896406334, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896406922, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896407629, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896408220, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896408832, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896409445, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896410031, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896410586, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896411140, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896411729, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896412271, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896412868, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896413423, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896413988, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896414672, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896415281, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896415849, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896416413, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896417027, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748358896418072, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\CompilerServices\\IgnoreWarningAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896418360, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Editor\\BurstReflection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896418960, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Bmi1.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896419227, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Sse.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896419367, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Sse4_1.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896419736, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Ssse3.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748358896417211, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896419995, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896420142, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748358896420334, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896420853, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896421386, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748358896420978, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748358896421638, "dur": 94, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896422253, "dur": 549, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896422842, "dur": 57113, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748358896484019, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896485907, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896485988, "dur": 1928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896487917, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896488055, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896489855, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896489965, "dur": 1776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896491742, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896491879, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896493802, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896493898, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748358896495821, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896496595, "dur": 33171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896529768, "dur": 5747, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748358896535516, "dur": 3084123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896359052, "dur": 30040, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896389135, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748358896389105, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4764A97CA20C124C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896389238, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896389390, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748358896389388, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_99F5CB938A8C7E39.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896389694, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748358896389692, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_187779C7F7B3B4CE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896389764, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896389878, "dur": 125, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4D8A662031E48D37.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896390004, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896390093, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748358896390091, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_24FE18831D700F3B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896390347, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896390398, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896390480, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896390750, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_387026818553F7BE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896391193, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748358896391546, "dur": 1523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896393111, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896393237, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17278736735188021267.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748358896393350, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896393509, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896394503, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896395132, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896395772, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896396370, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896397109, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896397671, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896398097, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896398672, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896399240, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896399805, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896400809, "dur": 1442, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\TransformPosition.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896400358, "dur": 1983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896402465, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\RTHandleUtils.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896402341, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896403581, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896404475, "dur": 2468, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896404137, "dur": 3113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896407250, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896407809, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896408352, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896409140, "dur": 2038, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\CubemapShaderProperty.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896409105, "dur": 2598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896411703, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896412250, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896412872, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896413431, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896414010, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896414642, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896415232, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896415754, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896416479, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\DualShock\\DualShockGamepadHID.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896417411, "dur": 813, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\Android\\AndroidKeyCode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896418877, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\InputSystem.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896416265, "dur": 3289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896419579, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896419644, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896419729, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896419869, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896419948, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896421185, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896421309, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896422104, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896422235, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896422339, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896423034, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896423146, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896423208, "dur": 1502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\Avatar\\GetAvatar.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896425327, "dur": 2050, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\QueryVisualElementsExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748358896423208, "dur": 4578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896427787, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896428731, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748358896428995, "dur": 55044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896484044, "dur": 1918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896485963, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896487919, "dur": 570, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.IO.Compression\\net35\\Mapbox.IO.Compression.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748358896486065, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896488522, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896488610, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896490615, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896490742, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896492663, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896492739, "dur": 677, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896495172, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748358896493420, "dur": 2077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748358896495498, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896495592, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896495740, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896495845, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748358896496143, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896496224, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896496503, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896496592, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748358896497249, "dur": 3122343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896359098, "dur": 30145, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896389286, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896389254, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5AD924FEB2B8122.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748358896389364, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896389430, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_CF7D4E5FB72BD329.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748358896389579, "dur": 825, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896390429, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896390535, "dur": 3218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748358896393754, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896394047, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896394983, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896395538, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896396098, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896396647, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896397356, "dur": 8352, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostpolicy.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896397304, "dur": 8998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896406906, "dur": 7521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Move\\MoveItemModeReplace.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748358896406303, "dur": 8218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896414522, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896415292, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896415858, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896416428, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896417218, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748358896418025, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896417426, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896418129, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896418271, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748358896418458, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748358896418643, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748358896418808, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896419399, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896419622, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896420005, "dur": 934, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896421080, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896421179, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896419513, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896421568, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896422145, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896422725, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896424546, "dur": 2889, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Hub\\Operations\\DownloadRepository.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748358896423638, "dur": 3800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896427438, "dur": 1351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896428789, "dur": 57234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896486029, "dur": 1880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896487910, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896488018, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896488603, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896487999, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896490105, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896490557, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896490802, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896490202, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896492386, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896492828, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896493402, "dur": 953, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Features.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896495356, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896495872, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896496045, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748358896492499, "dur": 3734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748358896496233, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358896496488, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748358896496634, "dur": 1956128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748358898452765, "dur": 352, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748358898452763, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748358898453170, "dur": 1907, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748358898455080, "dur": 1164522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896359132, "dur": 30123, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896389259, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4CC1AAD81429682D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896389354, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_2F2FB047803CA83B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896389528, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_DE2A9AC6D714602F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896390087, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896390085, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DDE8E3DC54C0DB1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896390238, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896390405, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1748358896390906, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896391631, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896391746, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896391800, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896392143, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896392283, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896392383, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896392526, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896393713, "dur": 3234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896398511, "dur": 472, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896398994, "dur": 1895, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896401267, "dur": 615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896401884, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896402479, "dur": 553, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748358896403034, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748358896404199, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748358896391095, "dur": 14454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896405551, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896405670, "dur": 1280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896406978, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896407556, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896408120, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896408689, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896409386, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896409960, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896410550, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896411125, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896411713, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896412264, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896412888, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896413442, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896414001, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896414585, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896415173, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896415795, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896416382, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896416936, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896417746, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896418438, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896418701, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896418900, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896419507, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896419744, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896420410, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Views\\PendingChanges\\ChangelistTreeViewItem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748358896419920, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896421143, "dur": 600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896421759, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896421902, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896422016, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896422942, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896424239, "dur": 1487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896425726, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896426119, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896426258, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896426316, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896426819, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896427157, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748358896427351, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896427851, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896428026, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896428732, "dur": 55292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896484026, "dur": 1931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896485958, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896486076, "dur": 1869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896487946, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896488018, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896488016, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896489920, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896490009, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896491945, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896492829, "dur": 482, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.IISIntegration.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896493403, "dur": 1962, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Composite.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358896492029, "dur": 4316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358896496345, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358896496616, "dur": 1106791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358897603410, "dur": 726210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358897603409, "dur": 727481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358898331906, "dur": 153, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748358898332122, "dur": 107683, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748358898452759, "dur": 131437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358898452758, "dur": 131440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358898584221, "dur": 1387, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748358898585612, "dur": 1034038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896359170, "dur": 30226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896389403, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFFADC6AB16C7213.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748358896389839, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748358896389837, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_7FEBE6A2654794CE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748358896389993, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896390084, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896390485, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1748358896390646, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896390866, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896391123, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1748358896391727, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748358896391929, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748358896392020, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896392114, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748358896393710, "dur": 2214, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Core\\mapbox-sdk-cs\\Tests\\UnitTests\\Editor\\MapboxUnitTests_ReverseGeocodeResource.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896393310, "dur": 2793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896396103, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896396743, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896397394, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896398008, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896398561, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896399179, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896399842, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896400472, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896401208, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896401756, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896402457, "dur": 3715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Controls\\Element3D.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896402296, "dur": 4240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896406537, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896407135, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896407737, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896408302, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896409189, "dur": 1998, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\SamplerStateMaterialSlot.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896408853, "dur": 2647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896411500, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896412083, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896412649, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896413270, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896413858, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896414449, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896415039, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896415633, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896416215, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896416795, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896417370, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748358896417564, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358896418084, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896418242, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748358896418433, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896418545, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748358896418697, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896418802, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358896419420, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896419518, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358896420215, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896420956, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetMenu\\AssetFilesFilterPatternsMenuBuilder.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896421207, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetMenu\\AssetsSelection.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896421276, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetMenu\\AssetVcsOperations.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896421486, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\AssetStatus.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896421935, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetsUtils\\GetSelectedPaths.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896422123, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetsUtils\\Processor\\AssetsProcessor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896422573, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\ChannelCertificateUiImpl.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896423196, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Developer\\CheckinProgress.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896423445, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Developer\\UpdateReport\\UpdateReportListHeaderState.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896423692, "dur": 373, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Developer\\UpdateReport\\UpdateReportListView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896424071, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\DrawGuiModeSwitcher.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896424335, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Gluon\\UpdateReport\\UpdateReportListView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896424581, "dur": 3465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Hub\\Operations\\DownloadRepository.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748358896420342, "dur": 8176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358896428520, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896428723, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748358896428944, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358896429921, "dur": 209, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358896430270, "dur": 1158682, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358897603879, "dur": 483626, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748358897603399, "dur": 484268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358898088169, "dur": 79, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358898088263, "dur": 304703, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748358898398141, "dur": 901481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748358898398139, "dur": 902778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748358899301995, "dur": 175, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748358899302189, "dur": 68438, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748358899384646, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748358899384645, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748358899384817, "dur": 1189, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748358899386012, "dur": 233595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896359228, "dur": 30180, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896389413, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2B8B2EE252BAF17E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896389474, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896389530, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2B8B2EE252BAF17E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896389805, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748358896389802, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_5F7426AD1A2DD658.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896390105, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896390647, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896391617, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896391795, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748358896392040, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896392394, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748358896392879, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896393165, "dur": 3526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896396703, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896397736, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896398272, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896398996, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896399567, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896400136, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896400683, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896401447, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896402487, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Expressions\\VFXExpressionExtractComponent.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748358896401978, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896403040, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896403703, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896404373, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896404994, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896405562, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896406170, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896406882, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\ShaderGraphProjectSettings.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748358896406714, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896408042, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896408655, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896409278, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896409865, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896410422, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896410973, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896411590, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896412134, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896412687, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896413296, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896413860, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896414452, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896415023, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896415601, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896416150, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896416749, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896417407, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896417576, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896418063, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896418213, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896420195, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896420910, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748358896421147, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Runtime\\Utilities\\PropertyBinding\\VFXPropertyBinder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748358896420418, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896421247, "dur": 782, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896422267, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896423741, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\DrawActionButton.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748358896423139, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896424544, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896425301, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748358896425495, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896426023, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896426249, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896427216, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896428993, "dur": 57040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896486034, "dur": 1946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896487981, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896488196, "dur": 2146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896490343, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896490420, "dur": 1695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896492116, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896492186, "dur": 1761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896493948, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896494029, "dur": 1686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748358896495716, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748358896495819, "dur": 841, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Simulator.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748358896496662, "dur": 3122927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748358899627958, "dur": 3512, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3416, "tid": 3450, "ts": 1748358899639414, "dur": 37, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3416, "tid": 3450, "ts": 1748358899639528, "dur": 4187, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3416, "tid": 3450, "ts": 1748358899632195, "dur": 11580, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}