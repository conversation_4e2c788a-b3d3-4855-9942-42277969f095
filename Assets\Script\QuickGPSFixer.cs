using UnityEngine;

/// <summary>
/// Solution rapide pour ajouter CleanGPSSystem aux bateaux qui n'en ont pas
/// </summary>
public class QuickGPSFixer : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool fixOnStart = true;
    [SerializeField] private bool showLogs = true;
    
    private void Start()
    {
        if (fixOnStart)
        {
            Invoke(nameof(FixAllBoats), 0.5f);
        }
    }
    
    /// <summary>
    /// Ajoute CleanGPSSystem à tous les bateaux qui n'ont ni UnifiedBoatGPS ni CleanGPSSystem
    /// </summary>
    [ContextMenu("Corriger Tous les Bateaux")]
    public void FixAllBoats()
    {
        LogMessage("🔧 === CORRECTION RAPIDE DES BATEAUX GPS ===");
        
        // Trouver tous les GameObjects qui pourraient être des bateaux
        var allObjects = FindObjectsOfType<GameObject>();
        int fixedCount = 0;
        
        foreach (var obj in allObjects)
        {
            // Vérifier si c'est probablement un bateau
            if (IsLikelyABoat(obj))
            {
                if (FixBoatIfNeeded(obj))
                {
                    fixedCount++;
                }
            }
        }
        
        LogMessage($"✅ Correction terminée: {fixedCount} bateaux corrigés");
        LogMessage("🔧 === FIN DE LA CORRECTION ===");
    }
    
    /// <summary>
    /// Vérifie si un GameObject est probablement un bateau
    /// </summary>
    private bool IsLikelyABoat(GameObject obj)
    {
        if (obj == null) return false;
        
        string name = obj.name.ToLower();
        
        // Critères pour identifier un bateau
        return name.Contains("boat") || 
               name.Contains("cata") || 
               name.Contains("!da") ||  // ID Meshtastic
               obj.GetComponent<TeamInfo>() != null ||
               obj.GetComponent<Rigidbody>() != null && obj.transform.childCount > 0;
    }
    
    /// <summary>
    /// Corrige un bateau s'il a besoin d'un système GPS
    /// </summary>
    private bool FixBoatIfNeeded(GameObject boat)
    {
        if (boat == null) return false;
        
        // Vérifier s'il a déjà un système GPS
        var unifiedGPS = boat.GetComponent<UnifiedBoatGPS>();
        var cleanGPS = boat.GetComponent<CleanGPSSystem>();
        
        if (unifiedGPS != null || cleanGPS != null)
        {
            LogMessage($"✅ {boat.name}: Système GPS déjà présent");
            return false;
        }
        
        // Ajouter CleanGPSSystem
        var newGPS = boat.AddComponent<CleanGPSSystem>();
        
        // Configurer le Rigidbody
        var rb = boat.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = boat.AddComponent<Rigidbody>();
        }
        rb.isKinematic = true;
        rb.detectCollisions = false;
        
        LogMessage($"🔧 {boat.name}: CleanGPSSystem ajouté");
        return true;
    }
    
    /// <summary>
    /// Affiche l'état de tous les bateaux
    /// </summary>
    [ContextMenu("Afficher État des Bateaux")]
    public void ShowBoatsStatus()
    {
        LogMessage("📊 === ÉTAT DES BATEAUX ===");
        
        var allObjects = FindObjectsOfType<GameObject>();
        int totalBoats = 0;
        int withUnified = 0;
        int withClean = 0;
        int withoutGPS = 0;
        
        foreach (var obj in allObjects)
        {
            if (IsLikelyABoat(obj))
            {
                totalBoats++;
                
                var unifiedGPS = obj.GetComponent<UnifiedBoatGPS>();
                var cleanGPS = obj.GetComponent<CleanGPSSystem>();
                
                if (unifiedGPS != null)
                {
                    withUnified++;
                    LogMessage($"🟡 {obj.name}: UnifiedBoatGPS");
                }
                else if (cleanGPS != null)
                {
                    withClean++;
                    LogMessage($"🟢 {obj.name}: CleanGPSSystem");
                }
                else
                {
                    withoutGPS++;
                    LogMessage($"🔴 {obj.name}: AUCUN SYSTÈME GPS");
                }
            }
        }
        
        LogMessage($"📊 RÉSUMÉ:");
        LogMessage($"   - Total bateaux: {totalBoats}");
        LogMessage($"   - Avec UnifiedBoatGPS: {withUnified}");
        LogMessage($"   - Avec CleanGPSSystem: {withClean}");
        LogMessage($"   - Sans système GPS: {withoutGPS}");
        LogMessage("📊 === FIN DE L'ÉTAT ===");
    }
    
    /// <summary>
    /// Crée le gestionnaire GPS s'il n'existe pas
    /// </summary>
    [ContextMenu("Créer Gestionnaire GPS")]
    public void CreateGPSManager()
    {
        var existingManager = FindObjectOfType<CleanGPSManager>();
        if (existingManager != null)
        {
            LogMessage("✅ CleanGPSManager existe déjà");
            return;
        }
        
        GameObject managerObject = new GameObject("CleanGPSManager");
        managerObject.AddComponent<CleanGPSManager>();
        
        LogMessage("✅ CleanGPSManager créé");
    }
    
    /// <summary>
    /// Crée l'adaptateur Meshtastic s'il n'existe pas
    /// </summary>
    [ContextMenu("Créer Adaptateur Meshtastic")]
    public void CreateMeshtasticAdapter()
    {
        var meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticTracker == null)
        {
            LogMessage("❌ MeshtasticGPSTracker non trouvé");
            return;
        }
        
        var existingAdapter = meshtasticTracker.GetComponent<CleanMeshtasticAdapter>();
        if (existingAdapter != null)
        {
            LogMessage("✅ CleanMeshtasticAdapter existe déjà");
            return;
        }
        
        meshtasticTracker.gameObject.AddComponent<CleanMeshtasticAdapter>();
        LogMessage("✅ CleanMeshtasticAdapter créé");
    }
    
    /// <summary>
    /// Configuration complète du système
    /// </summary>
    [ContextMenu("Configuration Complète")]
    public void CompleteSetup()
    {
        LogMessage("🚀 === CONFIGURATION COMPLÈTE ===");
        
        CreateGPSManager();
        CreateMeshtasticAdapter();
        FixAllBoats();
        
        LogMessage("🚀 === CONFIGURATION TERMINÉE ===");
        LogMessage("✅ Système GPS propre installé et opérationnel!");
    }
    
    private void LogMessage(string message)
    {
        if (showLogs)
        {
            Debug.Log($"[QuickGPSFixer] {message}");
        }
    }
}
