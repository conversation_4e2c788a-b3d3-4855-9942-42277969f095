using UnityEngine;
using Mapbox.Utils;
using Mapbox.Unity.Map;
using System.Collections.Generic;

/// <summary>
/// Téléportation directe des bateaux vers des coordonnées GPS spécifiques
/// Résout le problème où ManualTeleportToLatestGPS ne fonctionne pas sans données GPS préalables
/// </summary>
public class DirectGPSTeleporter : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private AbstractMap map;
    [SerializeField] private Transform fleetContainer;
    [SerializeField] private KeyCode teleportKey = KeyCode.T;
    [SerializeField] private float heightOffset = 1f;
    
    [Header("Coordonnées GPS par défaut")]
    [SerializeField] private Vector2d defaultGPSPosition = new Vector2d(49.07237, -1.57763);
    [SerializeField] private float spreadDistance = 10f; // Distance entre les bateaux en mètres
    
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    
    private void Start()
    {
        if (map == null)
        {
            map = FindFirstObjectByType<AbstractMap>();
        }
        
        if (fleetContainer == null)
        {
            GameObject fleet = GameObject.Find("Fleet");
            if (fleet != null)
            {
                fleetContainer = fleet.transform;
            }
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(teleportKey))
        {
            PerformDirectTeleportation();
        }
    }
    
    /// <summary>
    /// Effectue la téléportation directe de tous les bateaux
    /// </summary>
    public void PerformDirectTeleportation()
    {
        if (map == null)
        {
            LogDebug("[DirectGPSTeleporter] ERREUR: Pas de carte Mapbox trouvée!");
            return;
        }
        
        LogDebug("[DirectGPSTeleporter] === TÉLÉPORTATION DIRECTE GPS ===");
        
        // Trouver tous les bateaux avec UnifiedBoatGPS
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allBoats.Length == 0)
        {
            LogDebug("[DirectGPSTeleporter] Aucun bateau avec UnifiedBoatGPS trouvé!");
            return;
        }
        
        LogDebug($"[DirectGPSTeleporter] {allBoats.Length} bateaux trouvés");
        
        for (int i = 0; i < allBoats.Length; i++)
        {
            var boat = allBoats[i];
            if (boat == null) continue;
            
            // Calculer une position GPS légèrement différente pour chaque bateau
            Vector2d targetGPS = CalculateBoatGPSPosition(i, allBoats.Length);
            
            // Téléporter directement
            TeleportBoatToGPS(boat, targetGPS);
        }
        
        LogDebug("[DirectGPSTeleporter] === TÉLÉPORTATION TERMINÉE ===");
    }
    
    /// <summary>
    /// Calcule une position GPS pour un bateau spécifique
    /// </summary>
    private Vector2d CalculateBoatGPSPosition(int boatIndex, int totalBoats)
    {
        // Disposer les bateaux en ligne horizontale
        float offsetMeters = (boatIndex - totalBoats / 2f) * spreadDistance;
        
        // Convertir le décalage en degrés (approximatif)
        // 1 degré de longitude ≈ 111000 mètres à l'équateur
        double offsetLongitude = offsetMeters / 111000.0;
        
        return new Vector2d(
            defaultGPSPosition.x, // Latitude (même pour tous)
            defaultGPSPosition.y + offsetLongitude // Longitude décalée
        );
    }
    
    /// <summary>
    /// Téléporte un bateau spécifique vers des coordonnées GPS
    /// </summary>
    private void TeleportBoatToGPS(UnifiedBoatGPS boatGPS, Vector2d targetGPS)
    {
        if (boatGPS == null) return;
        
        try
        {
            // Méthode 1: Définir d'abord la position initiale, puis téléporter
            LogDebug($"[DirectGPSTeleporter] Téléportation de {boatGPS.gameObject.name} vers GPS: {targetGPS}");
            
            // 1. Définir la position GPS cible comme position initiale
            boatGPS.QueueInitialPosition(targetGPS);
            
            // 2. Forcer la téléportation vers cette position
            Vector3 unityPosition = map.GeoToWorldPosition(targetGPS);
            unityPosition.y = heightOffset;
            
            // 3. Téléportation directe via le BoatMovementController
            var movementController = boatGPS.GetType().GetField("movementController", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (movementController != null)
            {
                var controller = movementController.GetValue(boatGPS);
                var teleportMethod = controller.GetType().GetMethod("TeleportImmediately");
                
                if (teleportMethod != null)
                {
                    teleportMethod.Invoke(controller, new object[] { boatGPS.transform, unityPosition, 0f });
                    LogDebug($"[DirectGPSTeleporter] {boatGPS.gameObject.name} téléporté via BoatMovementController à {unityPosition}");
                }
                else
                {
                    // Fallback: téléportation directe
                    boatGPS.transform.position = unityPosition;
                    LogDebug($"[DirectGPSTeleporter] {boatGPS.gameObject.name} téléporté directement à {unityPosition}");
                }
            }
            else
            {
                // Fallback: téléportation directe
                boatGPS.transform.position = unityPosition;
                LogDebug($"[DirectGPSTeleporter] {boatGPS.gameObject.name} téléporté directement à {unityPosition}");
            }
            
            // 4. Mettre à jour la position GPS interne
            boatGPS.UpdateRealPosition(targetGPS, 0f, Time.time);
            
        }
        catch (System.Exception e)
        {
            LogDebug($"[DirectGPSTeleporter] Erreur lors de la téléportation de {boatGPS.gameObject.name}: {e.Message}");
            
            // Fallback ultime: positionnement direct simple
            Vector3 fallbackPosition = map.GeoToWorldPosition(targetGPS);
            fallbackPosition.y = heightOffset;
            boatGPS.transform.position = fallbackPosition;
            LogDebug($"[DirectGPSTeleporter] {boatGPS.gameObject.name} téléporté en mode fallback à {fallbackPosition}");
        }
    }
    
    /// <summary>
    /// Téléporte tous les bateaux vers des coordonnées GPS connues
    /// Utilise les vraies coordonnées GPS des logs si disponibles
    /// </summary>
    public void TeleportToKnownGPSCoordinates()
    {
        LogDebug("[DirectGPSTeleporter] === TÉLÉPORTATION VERS COORDONNÉES CONNUES ===");
        
        // Coordonnées GPS extraites de vos logs
        Dictionary<string, Vector2d> knownPositions = new Dictionary<string, Vector2d>
        {
            {"Cata01", new Vector2d(49.07237, -1.57763)},
            {"Cata02", new Vector2d(49.07237, -1.57771)},
            {"Cata03", new Vector2d(49.07237, -1.57770)}, // Position interpolée
            {"Secu", new Vector2d(49.07245, -1.57780)}
        };
        
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var boat in allBoats)
        {
            if (boat == null) continue;
            
            string boatName = boat.gameObject.name;
            
            if (knownPositions.TryGetValue(boatName, out Vector2d targetGPS))
            {
                LogDebug($"[DirectGPSTeleporter] Téléportation de {boatName} vers position connue: {targetGPS}");
                TeleportBoatToGPS(boat, targetGPS);
            }
            else
            {
                LogDebug($"[DirectGPSTeleporter] Pas de position connue pour {boatName}, utilisation de la position par défaut");
                TeleportBoatToGPS(boat, defaultGPSPosition);
            }
        }
        
        LogDebug("[DirectGPSTeleporter] === TÉLÉPORTATION TERMINÉE ===");
    }
    
    /// <summary>
    /// Téléporte vers des coordonnées GPS spécifiques (pour tests)
    /// </summary>
    [ContextMenu("Teleport to Known GPS Coordinates")]
    public void TeleportToKnownCoordinatesFromMenu()
    {
        TeleportToKnownGPSCoordinates();
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log(message);
        }
    }
}