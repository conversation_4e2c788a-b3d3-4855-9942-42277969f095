using UnityEngine;

/// <summary>
/// Installation automatique du système GPS simple
/// </summary>
public class SimpleGPSInstaller : MonoBehaviour
{
    [Header("Installation")]
    [SerializeField] private bool installOnStart = true;
    [SerializeField] private bool showLogs = true;
    
    private void Start()
    {
        if (installOnStart)
        {
            Invoke(nameof(InstallSimpleGPSSystem), 1f);
        }
    }
    
    /// <summary>
    /// Installe le système GPS simple complet
    /// </summary>
    [ContextMenu("Installer Système GPS Simple")]
    public void InstallSimpleGPSSystem()
    {
        LogMessage("🚀 === INSTALLATION SYSTÈME GPS SIMPLE ===");
        
        // 1. Créer le gestionnaire GPS
        CreateSimpleGPSManager();
        
        // 2. Configurer tous les bateaux
        ConfigureAllBoats();
        
        // 3. Connecter MeshtasticGPSTracker
        ConnectMeshtasticTracker();
        
        // 4. Nettoyer les anciens systèmes
        CleanupOldSystems();
        
        LogMessage("✅ === INSTALLATION TERMINÉE ===");
        LogMessage("🎯 Système GPS simple installé et prêt !");
        LogMessage("📡 Les bateaux vont maintenant bouger avec les données GPS !");
    }
    
    private void CreateSimpleGPSManager()
    {
        var existingManager = FindObjectOfType<SimpleGPSManager>();
        if (existingManager != null)
        {
            LogMessage("✅ SimpleGPSManager existe déjà");
            return;
        }
        
        GameObject managerObject = new GameObject("SimpleGPSManager");
        managerObject.AddComponent<SimpleGPSManager>();
        
        LogMessage("✅ SimpleGPSManager créé");
    }
    
    private void ConfigureAllBoats()
    {
        LogMessage("🚤 Configuration des bateaux...");
        
        var allObjects = FindObjectsOfType<GameObject>();
        int configuredCount = 0;
        
        foreach (var obj in allObjects)
        {
            if (IsBoat(obj))
            {
                if (ConfigureBoat(obj))
                {
                    configuredCount++;
                }
            }
        }
        
        LogMessage($"✅ {configuredCount} bateaux configurés avec SimpleGPSBoat");
    }
    
    private bool IsBoat(GameObject obj)
    {
        if (obj == null) return false;
        
        string name = obj.name.ToLower();
        return name.Contains("boat") || 
               name.Contains("cata") || 
               name.Contains("!da") ||
               obj.GetComponent<TeamInfo>() != null;
    }
    
    private bool ConfigureBoat(GameObject boat)
    {
        if (boat == null) return false;
        
        // Vérifier s'il a déjà SimpleGPSBoat
        var existingGPS = boat.GetComponent<SimpleGPSBoat>();
        if (existingGPS != null)
        {
            LogMessage($"✅ {boat.name}: SimpleGPSBoat déjà présent");
            return false;
        }
        
        // Ajouter SimpleGPSBoat
        boat.AddComponent<SimpleGPSBoat>();
        
        // Désactiver les anciens systèmes GPS
        var oldUnifiedGPS = boat.GetComponent<UnifiedBoatGPS>();
        if (oldUnifiedGPS != null)
        {
            oldUnifiedGPS.enabled = false;
            LogMessage($"🔇 {boat.name}: UnifiedBoatGPS désactivé");
        }
        
        // Configurer le Rigidbody
        var rb = boat.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = boat.AddComponent<Rigidbody>();
        }
        rb.isKinematic = false; // BoatWaypointMover a besoin de physique
        rb.useGravity = false;
        rb.constraints = RigidbodyConstraints.FreezePositionY | 
                        RigidbodyConstraints.FreezeRotationX | 
                        RigidbodyConstraints.FreezeRotationZ;
        
        // Ajouter BoxCollider si nécessaire
        var collider = boat.GetComponent<BoxCollider>();
        if (collider == null)
        {
            boat.AddComponent<BoxCollider>();
        }
        
        LogMessage($"✅ {boat.name}: SimpleGPSBoat ajouté et configuré");
        return true;
    }
    
    private void ConnectMeshtasticTracker()
    {
        LogMessage("📡 Connexion MeshtasticGPSTracker...");
        
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (tracker == null)
        {
            LogMessage("❌ MeshtasticGPSTracker non trouvé");
            return;
        }
        
        // Ajouter SimpleMeshtasticConnector s'il n'existe pas
        var existingConnector = tracker.GetComponent<SimpleMeshtasticConnector>();
        if (existingConnector != null)
        {
            LogMessage("✅ SimpleMeshtasticConnector existe déjà");
            return;
        }
        
        tracker.gameObject.AddComponent<SimpleMeshtasticConnector>();
        LogMessage("✅ SimpleMeshtasticConnector ajouté");
    }
    
    private void CleanupOldSystems()
    {
        LogMessage("🧹 Nettoyage des anciens systèmes...");
        
        int disabledCount = 0;
        
        // Désactiver les anciens gestionnaires GPS
        var oldManagers = FindObjectsOfType<BoatGPSManager>();
        foreach (var manager in oldManagers)
        {
            if (manager != null && manager.enabled)
            {
                manager.enabled = false;
                disabledCount++;
                LogMessage($"🔇 BoatGPSManager désactivé");
            }
        }
        
        // Désactiver les téléporteurs
        var teleporters = FindObjectsOfType<DirectGPSTeleporter>();
        foreach (var teleporter in teleporters)
        {
            if (teleporter != null && teleporter.enabled)
            {
                teleporter.enabled = false;
                disabledCount++;
                LogMessage($"🔇 DirectGPSTeleporter désactivé");
            }
        }
        
        // Désactiver les initialiseurs
        var initializers = FindObjectsOfType<BoatInitializer>();
        foreach (var initializer in initializers)
        {
            if (initializer != null && initializer.enabled)
            {
                initializer.enabled = false;
                disabledCount++;
                LogMessage($"🔇 BoatInitializer désactivé");
            }
        }
        
        LogMessage($"✅ {disabledCount} anciens composants désactivés");
    }
    
    /// <summary>
    /// Teste le système avec des données GPS simulées
    /// </summary>
    [ContextMenu("Test GPS Simulé")]
    public void TestSimulatedGPS()
    {
        LogMessage("🧪 === TEST GPS SIMULÉ ===");
        
        var manager = FindObjectOfType<SimpleGPSManager>();
        if (manager == null)
        {
            LogMessage("❌ SimpleGPSManager non trouvé - Installez d'abord le système");
            return;
        }
        
        // Positions GPS de test (autour de Toulouse)
        var testPositions = new Mapbox.Utils.Vector2d[]
        {
            new Mapbox.Utils.Vector2d(43.6047, 1.4442),
            new Mapbox.Utils.Vector2d(43.6050, 1.4445),
            new Mapbox.Utils.Vector2d(43.6053, 1.4448)
        };
        
        // Envoyer les positions de test
        for (int i = 0; i < testPositions.Length; i++)
        {
            string testNodeId = $"TestBoat_{i + 1}";
            manager.UpdateBoatGPS(testNodeId, testPositions[i]);
            LogMessage($"🧪 Position test envoyée: {testNodeId} → {testPositions[i]}");
        }
        
        LogMessage("🧪 === FIN DU TEST ===");
        LogMessage("👀 Regardez vos bateaux, ils devraient bouger !");
    }
    
    /// <summary>
    /// Affiche l'état du système
    /// </summary>
    [ContextMenu("Afficher État du Système")]
    public void ShowSystemStatus()
    {
        LogMessage("📊 === ÉTAT DU SYSTÈME GPS SIMPLE ===");
        
        var manager = FindObjectOfType<SimpleGPSManager>();
        var connector = FindObjectOfType<SimpleMeshtasticConnector>();
        var gpsBoats = FindObjectsOfType<SimpleGPSBoat>();
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogMessage($"SimpleGPSManager: {(manager != null ? "✅" : "❌")}");
        LogMessage($"SimpleMeshtasticConnector: {(connector != null ? "✅" : "❌")}");
        LogMessage($"Bateaux avec SimpleGPSBoat: {gpsBoats.Length}");
        LogMessage($"Bateaux avec UnifiedBoatGPS: {oldBoats.Length}");
        
        if (manager != null)
        {
            manager.ShowSystemStatus();
        }
        
        LogMessage("📊 === FIN DE L'ÉTAT ===");
    }
    
    /// <summary>
    /// Remet à zéro tous les bateaux
    /// </summary>
    [ContextMenu("Reset Tous les Bateaux")]
    public void ResetAllBoats()
    {
        LogMessage("🔄 Reset de tous les bateaux...");
        
        var gpsBoats = FindObjectsOfType<SimpleGPSBoat>();
        foreach (var boat in gpsBoats)
        {
            if (boat != null)
            {
                boat.ResetGPS();
            }
        }
        
        LogMessage($"✅ {gpsBoats.Length} bateaux remis à zéro");
    }
    
    private void LogMessage(string message)
    {
        if (showLogs)
        {
            Debug.Log($"[SimpleGPSInstaller] {message}");
        }
    }
}
