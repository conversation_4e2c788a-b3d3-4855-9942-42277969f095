using UnityEngine;

/// <summary>
/// Utilitaire pour corriger la configuration des Rigidbodies des bateaux GPS
/// Assure que tous les bateaux avec UnifiedBoatGPS ont isKinematic = true
/// </summary>
public class GPSRigidbodyFixer : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool fixOnStart = true;
    [SerializeField] private bool showLogs = true;
    
    private void Start()
    {
        if (fixOnStart)
        {
            Invoke(nameof(FixAllGPSBoats), 0.5f);
        }
    }
    
    /// <summary>
    /// Corrige la configuration de tous les bateaux GPS dans la scène
    /// </summary>
    [ContextMenu("Corriger Tous les Bateaux GPS")]
    public void FixAllGPSBoats()
    {
        LogMessage("🔧 === CORRECTION DES RIGIDBODIES GPS ===");
        
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allGPSBoats.Length == 0)
        {
            LogMessage("❌ Aucun bateau avec UnifiedBoatGPS trouvé");
            return;
        }
        
        int fixedCount = 0;
        int alreadyCorrectCount = 0;
        int noRigidbodyCount = 0;
        
        foreach (var gpsBoat in allGPSBoats)
        {
            var result = FixBoatRigidbody(gpsBoat.gameObject);
            
            switch (result)
            {
                case FixResult.Fixed:
                    fixedCount++;
                    break;
                case FixResult.AlreadyCorrect:
                    alreadyCorrectCount++;
                    break;
                case FixResult.NoRigidbody:
                    noRigidbodyCount++;
                    break;
            }
        }
        
        LogMessage($"✅ Correction terminée:");
        LogMessage($"   - Bateaux corrigés: {fixedCount}");
        LogMessage($"   - Déjà corrects: {alreadyCorrectCount}");
        LogMessage($"   - Sans Rigidbody: {noRigidbodyCount}");
        LogMessage($"   - Total traité: {allGPSBoats.Length}");
        LogMessage("🔧 === FIN DE LA CORRECTION ===");
    }
    
    /// <summary>
    /// Corrige la configuration d'un bateau spécifique
    /// </summary>
    private FixResult FixBoatRigidbody(GameObject boat)
    {
        if (boat == null) return FixResult.NoRigidbody;
        
        var rigidbody = boat.GetComponent<Rigidbody>();
        if (rigidbody == null)
        {
            LogMessage($"⚠️ {boat.name}: Pas de Rigidbody");
            return FixResult.NoRigidbody;
        }
        
        bool wasKinematic = rigidbody.isKinematic;
        bool wasDetectingCollisions = rigidbody.detectCollisions;
        
        // Configuration correcte pour GPS
        rigidbody.isKinematic = true;
        rigidbody.detectCollisions = false;
        
        if (!wasKinematic || wasDetectingCollisions)
        {
            LogMessage($"🔧 {boat.name}: Rigidbody corrigé (était: kinematic={wasKinematic}, collisions={wasDetectingCollisions})");
            return FixResult.Fixed;
        }
        else
        {
            LogMessage($"✅ {boat.name}: Rigidbody déjà correct");
            return FixResult.AlreadyCorrect;
        }
    }
    
    /// <summary>
    /// Vérifie l'état de tous les bateaux GPS sans les modifier
    /// </summary>
    [ContextMenu("Vérifier État des Bateaux GPS")]
    public void CheckAllGPSBoats()
    {
        LogMessage("🔍 === VÉRIFICATION DES RIGIDBODIES GPS ===");
        
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allGPSBoats.Length == 0)
        {
            LogMessage("❌ Aucun bateau avec UnifiedBoatGPS trouvé");
            return;
        }
        
        int correctCount = 0;
        int incorrectCount = 0;
        int noRigidbodyCount = 0;
        
        foreach (var gpsBoat in allGPSBoats)
        {
            var status = CheckBoatRigidbody(gpsBoat.gameObject);
            
            switch (status)
            {
                case RigidbodyStatus.Correct:
                    correctCount++;
                    break;
                case RigidbodyStatus.Incorrect:
                    incorrectCount++;
                    break;
                case RigidbodyStatus.NoRigidbody:
                    noRigidbodyCount++;
                    break;
            }
        }
        
        LogMessage($"📊 Résultats de la vérification:");
        LogMessage($"   - Corrects: {correctCount}");
        LogMessage($"   - Incorrects: {incorrectCount}");
        LogMessage($"   - Sans Rigidbody: {noRigidbodyCount}");
        LogMessage($"   - Total: {allGPSBoats.Length}");
        
        if (incorrectCount > 0)
        {
            LogMessage("⚠️ Des bateaux ont une configuration incorrecte ! Utilisez 'Corriger Tous les Bateaux GPS'");
        }
        else
        {
            LogMessage("✅ Tous les bateaux sont correctement configurés !");
        }
        
        LogMessage("🔍 === FIN DE LA VÉRIFICATION ===");
    }
    
    /// <summary>
    /// Vérifie l'état d'un bateau spécifique
    /// </summary>
    private RigidbodyStatus CheckBoatRigidbody(GameObject boat)
    {
        if (boat == null) return RigidbodyStatus.NoRigidbody;
        
        var rigidbody = boat.GetComponent<Rigidbody>();
        if (rigidbody == null)
        {
            LogMessage($"❌ {boat.name}: Pas de Rigidbody");
            return RigidbodyStatus.NoRigidbody;
        }
        
        bool isCorrect = rigidbody.isKinematic && !rigidbody.detectCollisions;
        
        if (isCorrect)
        {
            LogMessage($"✅ {boat.name}: Configuration correcte (kinematic=true, collisions=false)");
            return RigidbodyStatus.Correct;
        }
        else
        {
            LogMessage($"❌ {boat.name}: Configuration incorrecte (kinematic={rigidbody.isKinematic}, collisions={rigidbody.detectCollisions})");
            return RigidbodyStatus.Incorrect;
        }
    }
    
    /// <summary>
    /// Corrige un bateau spécifique (pour utilisation externe)
    /// </summary>
    public void FixSpecificBoat(GameObject boat)
    {
        if (boat == null) return;
        
        var gpsComponent = boat.GetComponent<UnifiedBoatGPS>();
        if (gpsComponent == null)
        {
            LogMessage($"❌ {boat.name}: Pas de composant UnifiedBoatGPS");
            return;
        }
        
        var result = FixBoatRigidbody(boat);
        LogMessage($"Correction de {boat.name}: {result}");
    }
    
    /// <summary>
    /// Surveille en continu les bateaux GPS (pour debug)
    /// </summary>
    [ContextMenu("Démarrer Surveillance Continue")]
    public void StartContinuousMonitoring()
    {
        InvokeRepeating(nameof(MonitorGPSBoats), 1f, 5f);
        LogMessage("🔍 Surveillance continue démarrée (toutes les 5 secondes)");
    }
    
    /// <summary>
    /// Arrête la surveillance continue
    /// </summary>
    [ContextMenu("Arrêter Surveillance Continue")]
    public void StopContinuousMonitoring()
    {
        CancelInvoke(nameof(MonitorGPSBoats));
        LogMessage("🔍 Surveillance continue arrêtée");
    }
    
    /// <summary>
    /// Méthode de surveillance appelée périodiquement
    /// </summary>
    private void MonitorGPSBoats()
    {
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        int incorrectCount = 0;
        
        foreach (var gpsBoat in allGPSBoats)
        {
            var rigidbody = gpsBoat.GetComponent<Rigidbody>();
            if (rigidbody != null && (!rigidbody.isKinematic || rigidbody.detectCollisions))
            {
                incorrectCount++;
                LogMessage($"⚠️ SURVEILLANCE: {gpsBoat.gameObject.name} a une configuration incorrecte !");
                
                // Auto-correction
                rigidbody.isKinematic = true;
                rigidbody.detectCollisions = false;
                LogMessage($"🔧 SURVEILLANCE: {gpsBoat.gameObject.name} auto-corrigé");
            }
        }
        
        if (incorrectCount == 0)
        {
            LogMessage($"✅ SURVEILLANCE: Tous les {allGPSBoats.Length} bateaux GPS sont corrects");
        }
    }
    
    private void LogMessage(string message)
    {
        if (showLogs)
        {
            Debug.Log($"[GPSRigidbodyFixer] {message}");
        }
    }
    
    private enum FixResult
    {
        Fixed,
        AlreadyCorrect,
        NoRigidbody
    }
    
    private enum RigidbodyStatus
    {
        Correct,
        Incorrect,
        NoRigidbody
    }
}
