using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Gestionnaire de test simple pour le système GPS
/// </summary>
public class SimpleGPSTestManager : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private KeyCode testTeleportKey = KeyCode.T;
    [SerializeField] private KeyCode resetTeleportKey = KeyCode.R;
    [SerializeField] private KeyCode showStatusKey = KeyCode.S;
    
    [Header("Test GPS Positions")]
    [SerializeField] private Vector2d testPosition1 = new Vector2d(43.6047, 1.4442); // Toulouse
    [SerializeField] private Vector2d testPosition2 = new Vector2d(43.6000, 1.4400); // Près de Toulouse
    
    private SimpleGPSTeleporter teleporter;
    private MeshtasticGPSTracker gpsTracker;
    private UnifiedBoatGPS[] allBoats;
    
    private void Start()
    {
        // Trouver les composants nécessaires
        teleporter = FindObjectOfType<SimpleGPSTeleporter>();
        gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();
        
        if (teleporter == null)
        {
            Debug.LogError("[SimpleGPSTestManager] SimpleGPSTeleporter non trouvé!");
        }
        
        if (gpsTracker == null)
        {
            Debug.LogError("[SimpleGPSTestManager] MeshtasticGPSTracker non trouvé!");
        }
        
        // Trouver tous les bateaux
        RefreshBoatList();
        
        Debug.Log("[SimpleGPSTestManager] Gestionnaire de test GPS initialisé");
        Debug.Log($"[SimpleGPSTestManager] Touches: {testTeleportKey} = Test téléportation, {resetTeleportKey} = Reset, {showStatusKey} = Statut");
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(testTeleportKey))
        {
            TestTeleportation();
        }
        
        if (Input.GetKeyDown(resetTeleportKey))
        {
            ResetAllTeleportations();
        }
        
        if (Input.GetKeyDown(showStatusKey))
        {
            ShowStatus();
        }
    }
    
    /// <summary>
    /// Test de téléportation manuelle
    /// </summary>
    [ContextMenu("Test Teleportation")]
    public void TestTeleportation()
    {
        RefreshBoatList();
        
        if (teleporter == null)
        {
            Debug.LogError("[SimpleGPSTestManager] Pas de SimpleGPSTeleporter disponible!");
            return;
        }
        
        if (allBoats == null || allBoats.Length == 0)
        {
            Debug.LogError("[SimpleGPSTestManager] Aucun bateau trouvé!");
            return;
        }
        
        Debug.Log("[SimpleGPSTestManager] === TEST DE TÉLÉPORTATION ===");
        
        for (int i = 0; i < allBoats.Length; i++)
        {
            var boat = allBoats[i];
            if (boat == null) continue;
            
            // Utiliser une position différente pour chaque bateau
            Vector2d testPos = (i % 2 == 0) ? testPosition1 : testPosition2;
            string boatId = $"TEST_{boat.gameObject.name}_{i}";
            
            Debug.Log($"[SimpleGPSTestManager] Test téléportation bateau {boat.gameObject.name} vers {testPos}");
            teleporter.TeleportBoatOnFirstGPS(boat.gameObject, testPos, boatId);
        }
        
        Debug.Log("[SimpleGPSTestManager] === TEST TERMINÉ ===");
    }
    
    /// <summary>
    /// Remet à zéro toutes les téléportations
    /// </summary>
    [ContextMenu("Reset All Teleportations")]
    public void ResetAllTeleportations()
    {
        if (teleporter != null)
        {
            teleporter.ResetAllTeleportations();
            Debug.Log("[SimpleGPSTestManager] Toutes les téléportations remises à zéro");
        }
    }
    
    /// <summary>
    /// Affiche le statut du système
    /// </summary>
    [ContextMenu("Show Status")]
    public void ShowStatus()
    {
        RefreshBoatList();
        
        Debug.Log("[SimpleGPSTestManager] === STATUT DU SYSTÈME ===");
        Debug.Log($"[SimpleGPSTestManager] SimpleGPSTeleporter: {(teleporter != null ? "✓" : "✗")}");
        Debug.Log($"[SimpleGPSTestManager] MeshtasticGPSTracker: {(gpsTracker != null ? "✓" : "✗")}");
        Debug.Log($"[SimpleGPSTestManager] Nombre de bateaux: {(allBoats != null ? allBoats.Length : 0)}");
        
        if (allBoats != null)
        {
            for (int i = 0; i < allBoats.Length; i++)
            {
                var boat = allBoats[i];
                if (boat != null)
                {
                    Vector2d currentPos = boat.GetCurrentPosition();
                    Vector3 unityPos = boat.transform.position;
                    Debug.Log($"[SimpleGPSTestManager] Bateau {i + 1}: {boat.gameObject.name}");
                    Debug.Log($"  - Position GPS: {currentPos}");
                    Debug.Log($"  - Position Unity: {unityPos}");
                }
            }
        }
        
        if (teleporter != null)
        {
            teleporter.ShowTeleportationStatus();
        }
        
        Debug.Log("[SimpleGPSTestManager] ================================");
    }
    
    /// <summary>
    /// Rafraîchit la liste des bateaux
    /// </summary>
    private void RefreshBoatList()
    {
        allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        Debug.Log($"[SimpleGPSTestManager] {allBoats.Length} bateaux trouvés");
    }
    
    /// <summary>
    /// Simule la réception de données GPS pour un bateau
    /// </summary>
    public void SimulateGPSData(string boatId, Vector2d gpsPosition)
    {
        if (gpsTracker == null)
        {
            Debug.LogError("[SimpleGPSTestManager] Pas de MeshtasticGPSTracker disponible!");
            return;
        }
        
        Debug.Log($"[SimpleGPSTestManager] Simulation GPS pour {boatId} à {gpsPosition}");
        
        // Créer des données GPS simulées
        var gpsData = new MeshtasticUDPReceiver.GpsData
        {
            nodeId = boatId,
            latitude = gpsPosition.x,
            longitude = gpsPosition.y,
            timestamp = (ulong)(Time.time * 1000)
        };
        
        // Appeler directement la méthode de traitement GPS
        gpsTracker.ProcessGPSData(gpsData);
    }
}
