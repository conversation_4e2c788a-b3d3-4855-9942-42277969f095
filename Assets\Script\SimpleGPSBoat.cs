using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Système GPS simple qui marche vraiment
/// Principe : GPS → Waypoints → BoatWaypointMover
/// </summary>
public class SimpleGPSBoat : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private AbstractMap map;
    [SerializeField] private float heightOffset = 1f;

    [Header("Mouvement")]
    [SerializeField] private float boatSpeed = 5f;
    [SerializeField] private float rotationSpeed = 90f;
    [SerializeField] private float waypointReachDistance = 2f;

    [Header("GPS")]
    [SerializeField] private float minGPSDistance = 1f;
    [SerializeField] private int maxWaypoints = 10;

    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;

    // Variables internes
    private List<Vector3> gpsWaypoints = new List<Vector3>();
    private BoatWaypointMover waypointMover;
    private Vector2d currentGPSPosition;
    private bool isInitialized = false;
    private bool hasFirstPosition = false;

    private void Start()
    {
        InitializeBoat();
    }

    private void InitializeBoat()
    {
        // Trouver la carte
        if (map == null)
        {
            map = FindFirstObjectByType<AbstractMap>();
        }

        if (map == null)
        {
            LogDebug("❌ Carte non trouvée");
            return;
        }

        // Attendre que la carte soit prête
        if (map.CenterLatitudeLongitude == default)
        {
            map.OnInitialized += OnMapReady;
        }
        else
        {
            OnMapReady();
        }

        // Configurer le BoatWaypointMover
        SetupWaypointMover();

        // Configurer le Rigidbody
        SetupRigidbody();

        LogDebug("🚀 SimpleGPSBoat initialisé");
    }

    private void OnMapReady()
    {
        isInitialized = true;
        LogDebug("✅ Carte prête");
    }

    private void SetupWaypointMover()
    {
        waypointMover = GetComponent<BoatWaypointMover>();
        if (waypointMover == null)
        {
            waypointMover = gameObject.AddComponent<BoatWaypointMover>();
        }

        // Configurer les paramètres du BoatWaypointMover
        waypointMover.speed = boatSpeed;
        waypointMover.rotationSpeed = rotationSpeed;
        waypointMover.waypointReachDistance = waypointReachDistance;
        waypointMover.enabled = false; // Désactivé au début

        LogDebug("✅ BoatWaypointMover configuré");
    }

    private void SetupRigidbody()
    {
        var rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = gameObject.AddComponent<Rigidbody>();
        }

        rb.isKinematic = true;
        rb.detectCollisions = false;

        LogDebug("✅ Rigidbody configuré");
    }

    /// <summary>
    /// Met à jour la position GPS - SEULE MÉTHODE PUBLIQUE
    /// </summary>
    public void UpdateGPSPosition(Vector2d newGPSPosition)
    {
        if (!isInitialized)
        {
            LogDebug("⏳ Carte pas encore prête");
            return;
        }

        if (!IsValidGPSPosition(newGPSPosition))
        {
            LogDebug($"❌ Position GPS invalide: {newGPSPosition}");
            return;
        }

        // Vérifier la distance minimale
        if (hasFirstPosition)
        {
            float distance = CalculateGPSDistance(currentGPSPosition, newGPSPosition);
            if (distance < minGPSDistance)
            {
                LogDebug($"📍 Position trop proche ignorée: {distance:F2}m");
                return;
            }
        }

        // Convertir GPS vers position Unity
        Vector3 worldPosition = map.GeoToWorldPosition(newGPSPosition);
        worldPosition.y = heightOffset;

        // Première position = téléportation
        if (!hasFirstPosition)
        {
            transform.position = worldPosition;
            hasFirstPosition = true;
            LogDebug($"🎯 Première position: téléportation à {newGPSPosition}");
        }
        else
        {
            // Ajouter comme waypoint
            AddWaypoint(worldPosition);
        }

        currentGPSPosition = newGPSPosition;
        LogDebug($"📍 Position GPS mise à jour: {newGPSPosition}");
    }

    private void AddWaypoint(Vector3 worldPosition)
    {
        gpsWaypoints.Add(worldPosition);

        // Limiter le nombre de waypoints
        if (gpsWaypoints.Count > maxWaypoints)
        {
            gpsWaypoints.RemoveAt(0);
        }

        // Mettre à jour le BoatWaypointMover
        UpdateWaypointMover();

        LogDebug($"🎯 Waypoint ajouté: {worldPosition} (Total: {gpsWaypoints.Count})");
    }

    private void UpdateWaypointMover()
    {
        if (waypointMover == null || gpsWaypoints.Count == 0) return;

        // BoatWaypointMover fonctionne avec un seul waypoint à la fois
        // On lui donne le dernier waypoint GPS
        Vector3 latestWaypoint = gpsWaypoints[gpsWaypoints.Count - 1];

        // Créer ou mettre à jour le waypoint cible
        Transform waypointTransform = transform.Find("CurrentGPSWaypoint");
        if (waypointTransform == null)
        {
            GameObject waypointObj = new GameObject("CurrentGPSWaypoint");
            waypointObj.transform.SetParent(transform);
            waypointTransform = waypointObj.transform;
        }

        waypointTransform.position = latestWaypoint;

        // Dire au BoatWaypointMover d'aller vers ce waypoint
        waypointMover.SetNextWaypoint(waypointTransform);

        // Activer le mouvement
        if (!waypointMover.enabled)
        {
            waypointMover.enabled = true;
            LogDebug("🚶 Mouvement démarré");
        }

        LogDebug($"🎯 Waypoint mis à jour: {latestWaypoint}");
    }

    private bool IsValidGPSPosition(Vector2d position)
    {
        return position.x >= -90 && position.x <= 90 &&
               position.y >= -180 && position.y <= 180 &&
               position.x != 0 && position.y != 0;
    }

    private float CalculateGPSDistance(Vector2d pos1, Vector2d pos2)
    {
        // Distance simple pour petites distances
        double lat1Rad = pos1.x * Mathf.Deg2Rad;
        double lat2Rad = pos2.x * Mathf.Deg2Rad;
        double deltaLat = (pos2.x - pos1.x) * Mathf.Deg2Rad;
        double deltaLon = (pos2.y - pos1.y) * Mathf.Deg2Rad;

        double a = Mathf.Sin((float)deltaLat / 2) * Mathf.Sin((float)deltaLat / 2) +
                   Mathf.Cos((float)lat1Rad) * Mathf.Cos((float)lat2Rad) *
                   Mathf.Sin((float)deltaLon / 2) * Mathf.Sin((float)deltaLon / 2);

        double c = 2 * Mathf.Atan2(Mathf.Sqrt((float)a), Mathf.Sqrt((float)(1 - a)));

        return (float)(6371000 * c);
    }

    /// <summary>
    /// Remet à zéro le système GPS
    /// </summary>
    [ContextMenu("Reset GPS")]
    public void ResetGPS()
    {
        gpsWaypoints.Clear();
        hasFirstPosition = false;

        if (waypointMover != null)
        {
            waypointMover.enabled = false;
        }

        // Nettoyer le waypoint GPS actuel
        Transform waypointTransform = transform.Find("CurrentGPSWaypoint");
        if (waypointTransform != null)
        {
            DestroyImmediate(waypointTransform.gameObject);
        }

        LogDebug("🔄 GPS remis à zéro");
    }

    /// <summary>
    /// Retourne la position GPS actuelle
    /// </summary>
    public Vector2d GetCurrentGPSPosition()
    {
        return currentGPSPosition;
    }

    /// <summary>
    /// Vérifie si le bateau a une position GPS
    /// </summary>
    public bool HasGPSPosition()
    {
        return hasFirstPosition;
    }

    /// <summary>
    /// Affiche les informations de debug
    /// </summary>
    [ContextMenu("Show GPS Info")]
    public void ShowGPSInfo()
    {
        LogDebug("=== INFORMATIONS GPS ===");
        LogDebug($"Initialisé: {isInitialized}");
        LogDebug($"Première position: {hasFirstPosition}");
        LogDebug($"Position GPS: {currentGPSPosition}");
        LogDebug($"Waypoints: {gpsWaypoints.Count}");
        LogDebug($"BoatWaypointMover actif: {waypointMover != null && waypointMover.enabled}");
        LogDebug("========================");
    }

    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[SimpleGPSBoat-{gameObject.name}] {message}");
        }
    }

    private void OnDestroy()
    {
        if (map != null)
        {
            map.OnInitialized -= OnMapReady;
        }
    }
}
