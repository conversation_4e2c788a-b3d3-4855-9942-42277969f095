using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Connecteur simple entre MeshtasticGPSTracker et SimpleGPSManager
/// </summary>
public class SimpleMeshtasticConnector : MonoBehaviour
{
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    
    private SimpleGPSManager gpsManager;
    private MeshtasticGPSTracker meshtasticTracker;
    
    private void Start()
    {
        // Trouver les composants
        gpsManager = SimpleGPSManager.Instance;
        if (gpsManager == null)
        {
            gpsManager = FindObjectOfType<SimpleGPSManager>();
        }
        
        meshtasticTracker = GetComponent<MeshtasticGPSTracker>();
        
        if (gpsManager == null)
        {
            LogDebug("❌ SimpleGPSManager non trouvé");
            return;
        }
        
        if (meshtasticTracker == null)
        {
            LogDebug("❌ MeshtasticGPSTracker non trouvé");
            return;
        }
        
        LogDebug("🔌 SimpleMeshtasticConnector initialisé");
    }
    
    /// <summary>
    /// Méthode appelée par MeshtasticGPSTracker
    /// </summary>
    public void OnGPSDataReceived(string nodeId, double latitude, double longitude)
    {
        if (gpsManager == null)
        {
            LogDebug("❌ SimpleGPSManager non disponible");
            return;
        }
        
        Vector2d gpsPosition = new Vector2d(latitude, longitude);
        gpsManager.UpdateBoatGPS(nodeId, gpsPosition);
        
        LogDebug($"📡 GPS transmis: {nodeId} → {gpsPosition}");
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[SimpleMeshtasticConnector] {message}");
        }
    }
}
