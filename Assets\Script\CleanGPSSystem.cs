using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Système GPS propre et unifié - Remplace tout le bordel existant
/// Principe simple : Données GPS → Position Unity → Mouvement fluide
/// </summary>
public class CleanGPSSystem : MonoBehaviour
{
    [Header("Configuration Carte")]
    [SerializeField] private AbstractMap map;
    
    [Header("Mouvement")]
    [SerializeField] private float moveSpeed = 8f;
    [SerializeField] private float rotationSpeed = 120f;
    [SerializeField] private float smoothTime = 0.3f;
    
    [Header("GPS")]
    [SerializeField] private float minGPSDistance = 1f; // Distance minimale entre positions GPS
    [SerializeField] private float heightOffset = 1f;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    [SerializeField] private bool showPath = true;
    [SerializeField] private Color pathColor = Color.red;
    
    // Variables internes
    private Vector2d currentGPSPosition;
    private Vector3 targetPosition;
    private bool hasGPSData = false;
    private bool isMoving = false;
    
    // Mouvement fluide
    private Vector3 velocity = Vector3.zero;
    private float currentSpeed = 0f;
    
    // Chemin GPS
    private List<Vector3> gpsPath = new List<Vector3>();
    private LineRenderer pathRenderer;
    
    // État
    private bool isInitialized = false;
    
    private void Start()
    {
        InitializeSystem();
    }
    
    private void InitializeSystem()
    {
        // Trouver la carte si pas assignée
        if (map == null)
        {
            map = FindFirstObjectByType<AbstractMap>();
        }
        
        if (map == null)
        {
            LogDebug("❌ Aucune carte Mapbox trouvée!");
            return;
        }
        
        // Attendre que la carte soit prête
        if (map.CenterLatitudeLongitude == default)
        {
            map.OnInitialized += OnMapReady;
        }
        else
        {
            OnMapReady();
        }
        
        // Configurer le Rigidbody pour le mouvement GPS
        ConfigureRigidbody();
        
        // Configurer le rendu du chemin
        if (showPath)
        {
            SetupPathRenderer();
        }
        
        LogDebug("🚀 CleanGPSSystem initialisé");
    }
    
    private void OnMapReady()
    {
        isInitialized = true;
        LogDebug("✅ Carte prête, GPS opérationnel");
    }
    
    private void ConfigureRigidbody()
    {
        var rb = GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.isKinematic = true;
            rb.detectCollisions = false;
            LogDebug("🔧 Rigidbody configuré pour GPS (kinématique)");
        }
    }
    
    /// <summary>
    /// Met à jour la position GPS du bateau
    /// SEULE MÉTHODE PUBLIQUE NÉCESSAIRE
    /// </summary>
    public void UpdateGPSPosition(Vector2d newGPSPosition)
    {
        if (!isInitialized)
        {
            LogDebug("⏳ GPS pas encore initialisé");
            return;
        }
        
        if (!IsValidGPSPosition(newGPSPosition))
        {
            LogDebug($"❌ Position GPS invalide: {newGPSPosition}");
            return;
        }
        
        // Vérifier la distance minimale
        if (hasGPSData)
        {
            float distance = CalculateGPSDistance(currentGPSPosition, newGPSPosition);
            if (distance < minGPSDistance)
            {
                LogDebug($"📍 Position trop proche ignorée (distance: {distance:F2}m)");
                return;
            }
        }
        
        // Convertir GPS vers position Unity
        Vector3 newWorldPosition = map.GeoToWorldPosition(newGPSPosition);
        newWorldPosition.y = heightOffset;
        
        // Première position GPS = téléportation
        if (!hasGPSData)
        {
            transform.position = newWorldPosition;
            targetPosition = newWorldPosition;
            LogDebug($"🎯 Première position GPS: téléportation à {newGPSPosition}");
        }
        else
        {
            // Positions suivantes = mouvement fluide
            targetPosition = newWorldPosition;
            isMoving = true;
            LogDebug($"🚶 Nouvelle cible GPS: {newGPSPosition}");
        }
        
        // Mettre à jour les données
        currentGPSPosition = newGPSPosition;
        hasGPSData = true;
        
        // Ajouter au chemin
        AddToPath(newWorldPosition);
        
        LogDebug($"📍 Position GPS mise à jour: {newGPSPosition}");
    }
    
    private void Update()
    {
        if (!isMoving || !hasGPSData) return;
        
        MoveTowardsTarget();
    }
    
    private void MoveTowardsTarget()
    {
        Vector3 currentPos = transform.position;
        float distanceToTarget = Vector3.Distance(currentPos, targetPosition);
        
        // Si on est arrivé à destination
        if (distanceToTarget < 0.5f)
        {
            isMoving = false;
            currentSpeed = 0f;
            LogDebug("🏁 Destination atteinte");
            return;
        }
        
        // Mouvement fluide vers la cible
        Vector3 newPosition = Vector3.SmoothDamp(currentPos, targetPosition, ref velocity, smoothTime);
        transform.position = newPosition;
        
        // Rotation fluide vers la direction
        Vector3 direction = (targetPosition - currentPos).normalized;
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
        
        // Calculer la vitesse actuelle
        currentSpeed = velocity.magnitude;
    }
    
    private void AddToPath(Vector3 worldPosition)
    {
        gpsPath.Add(worldPosition);
        
        // Limiter la taille du chemin
        if (gpsPath.Count > 50)
        {
            gpsPath.RemoveAt(0);
        }
        
        UpdatePathRenderer();
    }
    
    private void SetupPathRenderer()
    {
        GameObject pathObject = new GameObject($"GPSPath_{gameObject.name}");
        pathObject.transform.SetParent(transform);
        
        pathRenderer = pathObject.AddComponent<LineRenderer>();
        pathRenderer.material = new Material(Shader.Find("Sprites/Default"));
        pathRenderer.startColor = pathColor;
        pathRenderer.endColor = pathColor;
        pathRenderer.startWidth = 0.3f;
        pathRenderer.endWidth = 0.3f;
        pathRenderer.positionCount = 0;
        pathRenderer.useWorldSpace = true;
    }
    
    private void UpdatePathRenderer()
    {
        if (pathRenderer == null || !showPath) return;
        
        pathRenderer.positionCount = gpsPath.Count;
        for (int i = 0; i < gpsPath.Count; i++)
        {
            pathRenderer.SetPosition(i, gpsPath[i]);
        }
    }
    
    private bool IsValidGPSPosition(Vector2d position)
    {
        return position.x >= -90 && position.x <= 90 && 
               position.y >= -180 && position.y <= 180 &&
               position.x != 0 && position.y != 0;
    }
    
    private float CalculateGPSDistance(Vector2d pos1, Vector2d pos2)
    {
        // Formule de Haversine simplifiée pour petites distances
        double lat1Rad = pos1.x * Mathf.Deg2Rad;
        double lat2Rad = pos2.x * Mathf.Deg2Rad;
        double deltaLat = (pos2.x - pos1.x) * Mathf.Deg2Rad;
        double deltaLon = (pos2.y - pos1.y) * Mathf.Deg2Rad;
        
        double a = Mathf.Sin((float)deltaLat / 2) * Mathf.Sin((float)deltaLat / 2) +
                   Mathf.Cos((float)lat1Rad) * Mathf.Cos((float)lat2Rad) *
                   Mathf.Sin((float)deltaLon / 2) * Mathf.Sin((float)deltaLon / 2);
        
        double c = 2 * Mathf.Atan2(Mathf.Sqrt((float)a), Mathf.Sqrt((float)(1 - a)));
        
        return (float)(6371000 * c); // Rayon de la Terre en mètres
    }
    
    /// <summary>
    /// Remet à zéro le système GPS
    /// </summary>
    [ContextMenu("Reset GPS")]
    public void ResetGPS()
    {
        hasGPSData = false;
        isMoving = false;
        currentSpeed = 0f;
        velocity = Vector3.zero;
        gpsPath.Clear();
        
        if (pathRenderer != null)
        {
            pathRenderer.positionCount = 0;
        }
        
        LogDebug("🔄 GPS remis à zéro");
    }
    
    /// <summary>
    /// Retourne la position GPS actuelle
    /// </summary>
    public Vector2d GetCurrentGPSPosition()
    {
        return currentGPSPosition;
    }
    
    /// <summary>
    /// Retourne la vitesse actuelle en nœuds
    /// </summary>
    public float GetCurrentSpeedKnots()
    {
        return currentSpeed * 1.944f; // Conversion m/s vers nœuds
    }
    
    /// <summary>
    /// Vérifie si le bateau a des données GPS
    /// </summary>
    public bool HasGPSData()
    {
        return hasGPSData;
    }
    
    /// <summary>
    /// Affiche les informations de debug
    /// </summary>
    [ContextMenu("Show GPS Info")]
    public void ShowGPSInfo()
    {
        LogDebug("=== INFORMATIONS GPS ===");
        LogDebug($"Initialisé: {isInitialized}");
        LogDebug($"Données GPS: {hasGPSData}");
        LogDebug($"En mouvement: {isMoving}");
        LogDebug($"Position GPS: {currentGPSPosition}");
        LogDebug($"Vitesse: {currentSpeed:F2} m/s ({GetCurrentSpeedKnots():F1} nœuds)");
        LogDebug($"Points du chemin: {gpsPath.Count}");
        LogDebug("========================");
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[CleanGPSSystem-{gameObject.name}] {message}");
        }
    }
    
    private void OnDestroy()
    {
        if (map != null)
        {
            map.OnInitialized -= OnMapReady;
        }
    }
}
