using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Outil de migration de l'ancien système GPS vers le nouveau système simple
/// </summary>
public class GPSSystemMigrator : MonoBehaviour
{
    [Header("Migration")]
    [SerializeField] private bool autoMigrateOnStart = false;
    [SerializeField] private bool showMigrationLogs = true;

    [Header("Nouveau Système")]
    [SerializeField] private GameObject simpleGPSAdapterPrefab;

    private void Start()
    {
        if (autoMigrateOnStart)
        {
            Invoke(nameof(MigrateToSimpleSystem), 0.5f);
        }
    }

    /// <summary>
    /// Migre tous les bateaux vers le nouveau système simple
    /// </summary>
    [ContextMenu("Migrer vers Système Simple")]
    public void MigrateToSimpleSystem()
    {
        LogMigration("🔄 === DÉBUT DE LA MIGRATION ===");

        // 1. Trouver tous les bateaux avec UnifiedBoatGPS
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();
        LogMigration($"🔍 Trouvé {oldBoats.Length} bateaux avec UnifiedBoatGPS");

        // 2. Remplacer UnifiedBoatGPS par SimpleBoatGPS
        int migratedCount = 0;
        foreach (var oldBoat in oldBoats)
        {
            if (MigrateBoat(oldBoat))
            {
                migratedCount++;
            }
        }

        // 3. Créer l'adaptateur GPS s'il n'existe pas
        CreateGPSAdapterIfNeeded();

        // 4. Modifier MeshtasticGPSTracker pour utiliser le nouveau système
        ModifyMeshtasticGPSTracker();

        LogMigration($"✅ Migration terminée: {migratedCount}/{oldBoats.Length} bateaux migrés");
        LogMigration("🔄 === FIN DE LA MIGRATION ===");
    }

    /// <summary>
    /// Migre un bateau spécifique
    /// </summary>
    private bool MigrateBoat(UnifiedBoatGPS oldBoat)
    {
        if (oldBoat == null) return false;

        GameObject boatObject = oldBoat.gameObject;
        string boatName = boatObject.name;

        try
        {
            // Vérifier si SimpleBoatGPS existe déjà
            var existingSimpleGPS = boatObject.GetComponent<SimpleBoatGPS>();
            if (existingSimpleGPS != null)
            {
                LogMigration($"⚠️ {boatName} a déjà SimpleBoatGPS, migration ignorée");
                return false;
            }

            // Sauvegarder les paramètres importants de l'ancien système
            Vector2d currentGPSPos = oldBoat.GetCurrentPosition();

            // Désactiver l'ancien composant (ne pas le supprimer pour l'instant)
            oldBoat.enabled = false;

            // Ajouter le nouveau composant
            var newBoat = boatObject.AddComponent<SimpleBoatGPS>();

            // Si on a une position GPS valide, l'ajouter
            if (currentGPSPos.x != 0 && currentGPSPos.y != 0)
            {
                // Attendre un frame pour que le nouveau système s'initialise
                StartCoroutine(AddInitialGPSPosition(newBoat, currentGPSPos));
            }

            LogMigration($"✅ {boatName} migré avec succès");
            return true;
        }
        catch (System.Exception e)
        {
            LogMigration($"❌ Erreur lors de la migration de {boatName}: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Ajoute une position GPS initiale après initialisation
    /// </summary>
    private System.Collections.IEnumerator AddInitialGPSPosition(SimpleBoatGPS newBoat, Vector2d gpsPosition)
    {
        yield return new WaitForSeconds(0.1f);

        if (newBoat != null)
        {
            newBoat.AddGPSPosition(gpsPosition);
            LogMigration($"📍 Position GPS initiale ajoutée à {newBoat.gameObject.name}: {gpsPosition}");
        }
    }

    /// <summary>
    /// Crée l'adaptateur GPS s'il n'existe pas
    /// </summary>
    private void CreateGPSAdapterIfNeeded()
    {
        var existingAdapter = FindObjectOfType<SimpleGPSAdapter>();
        if (existingAdapter != null)
        {
            LogMigration("✅ SimpleGPSAdapter existe déjà");
            return;
        }

        // Créer un nouveau GameObject pour l'adaptateur
        GameObject adapterObject = new GameObject("SimpleGPSAdapter");
        adapterObject.AddComponent<SimpleGPSAdapter>();

        LogMigration("✅ SimpleGPSAdapter créé");
    }

    /// <summary>
    /// Modifie MeshtasticGPSTracker pour utiliser le nouveau système
    /// </summary>
    private void ModifyMeshtasticGPSTracker()
    {
        var meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticTracker == null)
        {
            LogMigration("⚠️ MeshtasticGPSTracker non trouvé");
            return;
        }

        // Ajouter le composant de connexion s'il n'existe pas
        var connector = meshtasticTracker.GetComponent<MeshtasticToSimpleGPSConnector>();
        if (connector == null)
        {
            connector = meshtasticTracker.gameObject.AddComponent<MeshtasticToSimpleGPSConnector>();
            LogMigration("✅ Connecteur MeshtasticToSimpleGPS ajouté");
        }
        else
        {
            LogMigration("✅ Connecteur MeshtasticToSimpleGPS existe déjà");
        }
    }

    /// <summary>
    /// Nettoie les anciens composants (à utiliser avec précaution)
    /// </summary>
    [ContextMenu("Nettoyer Anciens Composants")]
    public void CleanupOldComponents()
    {
        LogMigration("🧹 === NETTOYAGE DES ANCIENS COMPOSANTS ===");

        // Supprimer les anciens UnifiedBoatGPS désactivés
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();
        int cleanedCount = 0;

        foreach (var oldBoat in oldBoats)
        {
            if (!oldBoat.enabled)
            {
                DestroyImmediate(oldBoat);
                cleanedCount++;
                LogMigration($"🗑️ UnifiedBoatGPS supprimé de {oldBoat.gameObject.name}");
            }
        }

        // Supprimer les autres composants obsolètes
        var obsoleteComponents = new System.Type[]
        {
            typeof(SimpleGPSTeleporter),
            typeof(GPSMovementOptimizer),
            typeof(TeleportationConflictResolver),
            typeof(ImprovedTeleportationManager),
            typeof(DirectGPSTeleporter),
            typeof(BoatGPSManager),
            typeof(BoatInitializer)
        };

        foreach (var componentType in obsoleteComponents)
        {
            var components = FindObjectsOfType(componentType);
            foreach (var component in components)
            {
                if (component is MonoBehaviour mono)
                {
                    DestroyImmediate(mono);
                    LogMigration($"🗑️ {componentType.Name} supprimé");
                }
            }
        }

        LogMigration($"✅ Nettoyage terminé: {cleanedCount} composants supprimés");
        LogMigration("🧹 === FIN DU NETTOYAGE ===");
    }

    /// <summary>
    /// Teste le nouveau système avec des données GPS simulées
    /// </summary>
    [ContextMenu("Tester Nouveau Système")]
    public void TestNewSystem()
    {
        LogMigration("🧪 === TEST DU NOUVEAU SYSTÈME ===");

        var adapter = FindObjectOfType<SimpleGPSAdapter>();
        if (adapter == null)
        {
            LogMigration("❌ SimpleGPSAdapter non trouvé");
            return;
        }

        // Positions GPS de test (autour de Toulouse)
        var testPositions = new Vector2d[]
        {
            new Vector2d(43.6047, 1.4442),
            new Vector2d(43.6050, 1.4445),
            new Vector2d(43.6053, 1.4448)
        };

        var boatIds = adapter.GetRegisteredBoatIds();
        if (boatIds.Count == 0)
        {
            LogMigration("❌ Aucun bateau enregistré");
            return;
        }

        // Envoyer des positions de test au premier bateau
        string testBoatId = boatIds[0];
        LogMigration($"🧪 Test avec le bateau: {testBoatId}");

        for (int i = 0; i < testPositions.Length; i++)
        {
            adapter.UpdateBoatGPSPosition(testBoatId, testPositions[i]);
            LogMigration($"📍 Position test {i + 1} envoyée: {testPositions[i]}");
        }

        LogMigration("✅ Test terminé - Vérifiez le mouvement du bateau");
        LogMigration("🧪 === FIN DU TEST ===");
    }

    private void LogMigration(string message)
    {
        if (showMigrationLogs)
        {
            Debug.Log($"[GPSSystemMigrator] {message}");
        }
    }
}

/// <summary>
/// Connecteur pour faire le lien entre MeshtasticGPSTracker et SimpleGPSAdapter
/// </summary>
public class MeshtasticToSimpleGPSConnector : MonoBehaviour
{
    private SimpleGPSAdapter gpsAdapter;
    private MeshtasticGPSTracker meshtasticTracker;

    private void Start()
    {
        // Trouver les composants nécessaires
        gpsAdapter = FindObjectOfType<SimpleGPSAdapter>();
        meshtasticTracker = GetComponent<MeshtasticGPSTracker>();

        if (gpsAdapter == null)
        {
            Debug.LogError("[MeshtasticToSimpleGPSConnector] SimpleGPSAdapter non trouvé!");
            return;
        }

        if (meshtasticTracker == null)
        {
            Debug.LogError("[MeshtasticToSimpleGPSConnector] MeshtasticGPSTracker non trouvé!");
            return;
        }

        Debug.Log("[MeshtasticToSimpleGPSConnector] Connecteur initialisé");
    }

    /// <summary>
    /// Méthode appelée par MeshtasticGPSTracker pour transmettre les données GPS
    /// </summary>
    public void OnGPSDataReceived(string nodeId, double latitude, double longitude)
    {
        if (gpsAdapter == null) return;

        var gpsPosition = new Vector2d(latitude, longitude);
        gpsAdapter.UpdateBoatGPSPosition(nodeId, gpsPosition);

        Debug.Log($"[MeshtasticToSimpleGPSConnector] GPS transmis: {nodeId} → {gpsPosition}");
    }
}
