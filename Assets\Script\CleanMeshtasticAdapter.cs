using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Adaptateur propre pour connecter MeshtasticGPSTracker au nouveau système GPS
/// Remplace toute la logique complexe par des appels simples
/// </summary>
public class CleanMeshtasticAdapter : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool showDebugLogs = true;
    [SerializeField] private string officialBoatNodeId = ""; // ID du bateau officiel
    [SerializeField] private Transform officialBoatTransform; // Transform du bateau officiel

    private CleanGPSManager gpsManager;

    private void Start()
    {
        // Trouver le gestionnaire GPS
        gpsManager = CleanGPSManager.Instance;
        if (gpsManager == null)
        {
            gpsManager = FindObjectOfType<CleanGPSManager>();
        }

        if (gpsManager == null)
        {
            LogDebug("❌ CleanGPSManager non trouvé!");
            return;
        }

        LogDebug("🔌 CleanMeshtasticAdapter initialisé");
    }

    /// <summary>
    /// Méthode appelée par MeshtasticGPSTracker pour transmettre les données GPS
    /// REMPLACE TOUTE LA LOGIQUE COMPLEXE
    /// </summary>
    public void OnGPSDataReceived(string nodeId, double latitude, double longitude)
    {
        if (gpsManager == null)
        {
            LogDebug("❌ Gestionnaire GPS non disponible");
            return;
        }

        Vector2d gpsPosition = new Vector2d(latitude, longitude);

        // Traitement spécial pour le bateau de sécurité/officiel
        if (!string.IsNullOrEmpty(officialBoatNodeId) && nodeId == officialBoatNodeId)
        {
            LogDebug($"🚨 Bateau de SÉCURITÉ détecté: {nodeId}");
            HandleOfficialBoat(gpsPosition);
        }
        else
        {
            // Traitement standard pour les bateaux de la flotte (course)
            LogDebug($"🚤 Bateau de FLOTTE détecté: {nodeId}");
            HandleFleetBoat(nodeId, gpsPosition);
        }

        LogDebug($"📡 GPS reçu: {nodeId} → {gpsPosition}");
    }

    /// <summary>
    /// Traite les données GPS du bateau de sécurité/officiel
    /// </summary>
    private void HandleOfficialBoat(Vector2d gpsPosition)
    {
        if (officialBoatTransform != null)
        {
            // Essayer CleanGPSSystem d'abord (nouveau système)
            var cleanGPS = officialBoatTransform.GetComponent<CleanGPSSystem>();
            if (cleanGPS != null)
            {
                cleanGPS.UpdateGPSPosition(gpsPosition);
                LogDebug($"🚨 Bateau de SÉCURITÉ mis à jour (CleanGPS): {gpsPosition}");
                return;
            }

            // Fallback vers UnifiedBoatGPS (ancien système)
            var unifiedGPS = officialBoatTransform.GetComponent<UnifiedBoatGPS>();
            if (unifiedGPS != null)
            {
                unifiedGPS.UpdateRealPosition(gpsPosition, 0f, Time.time);
                LogDebug($"🚨 Bateau de SÉCURITÉ mis à jour (UnifiedGPS): {gpsPosition}");
                return;
            }

            // Aucun système GPS trouvé
            LogDebug($"❌ Bateau de SÉCURITÉ sans système GPS - Ajout automatique de CleanGPSSystem");

            // Ajouter automatiquement CleanGPSSystem
            var newGPS = officialBoatTransform.gameObject.AddComponent<CleanGPSSystem>();

            // Configurer le Rigidbody
            var rb = officialBoatTransform.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = officialBoatTransform.gameObject.AddComponent<Rigidbody>();
            }
            rb.isKinematic = true;
            rb.detectCollisions = false;

            // Mettre à jour la position
            newGPS.UpdateGPSPosition(gpsPosition);
            LogDebug($"✅ CleanGPSSystem ajouté automatiquement au bateau de SÉCURITÉ");
        }
        else
        {
            LogDebug($"❌ Transform du bateau de SÉCURITÉ non assigné dans MeshtasticGPSTracker");
            LogDebug($"💡 Configurez 'Official Boat Transform' dans MeshtasticGPSTracker");
        }
    }

    /// <summary>
    /// Traite les données GPS des bateaux de la flotte
    /// </summary>
    private void HandleFleetBoat(string nodeId, Vector2d gpsPosition)
    {
        gpsManager.UpdateBoatGPS(nodeId, gpsPosition);
        LogDebug($"🚤 Bateau flotte mis à jour: {nodeId} → {gpsPosition}");
    }

    /// <summary>
    /// Configure le bateau officiel
    /// </summary>
    public void SetOfficialBoat(string nodeId, Transform boatTransform)
    {
        officialBoatNodeId = nodeId;
        officialBoatTransform = boatTransform;

        LogDebug($"🏛️ Bateau officiel configuré: {nodeId} → {boatTransform?.name}");
    }

    /// <summary>
    /// Test avec des données GPS simulées
    /// </summary>
    [ContextMenu("Test GPS Simulé")]
    public void TestSimulatedGPS()
    {
        LogDebug("🧪 === TEST GPS SIMULÉ ===");

        // Positions GPS de test (autour de Toulouse)
        var testPositions = new Vector2d[]
        {
            new Vector2d(43.6047, 1.4442),
            new Vector2d(43.6050, 1.4445),
            new Vector2d(43.6053, 1.4448),
            new Vector2d(43.6056, 1.4451)
        };

        // Envoyer les positions de test
        for (int i = 0; i < testPositions.Length; i++)
        {
            string testNodeId = $"TestBoat_{i + 1}";
            OnGPSDataReceived(testNodeId, testPositions[i].x, testPositions[i].y);
            LogDebug($"🧪 Position test {i + 1} envoyée: {testNodeId} → {testPositions[i]}");
        }

        LogDebug("🧪 === FIN DU TEST ===");
    }

    /// <summary>
    /// Affiche l'état de l'adaptateur
    /// </summary>
    [ContextMenu("Afficher État Adaptateur")]
    public void ShowAdapterStatus()
    {
        LogDebug("📊 === ÉTAT DE L'ADAPTATEUR ===");
        LogDebug($"Gestionnaire GPS: {(gpsManager != null ? "✅" : "❌")}");
        LogDebug($"Bateau officiel ID: {(string.IsNullOrEmpty(officialBoatNodeId) ? "Non défini" : officialBoatNodeId)}");
        LogDebug($"Bateau officiel Transform: {(officialBoatTransform != null ? officialBoatTransform.name : "Non assigné")}");

        if (gpsManager != null)
        {
            LogDebug($"Bateaux gérés: {gpsManager.GetManagedBoatsCount()}");

            var boatIds = gpsManager.GetRegisteredBoatIds();
            foreach (string boatId in boatIds)
            {
                LogDebug($"  - {boatId}");
            }
        }

        LogDebug("📊 === FIN DE L'ÉTAT ===");
    }

    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[CleanMeshtasticAdapter] {message}");
        }
    }
}
