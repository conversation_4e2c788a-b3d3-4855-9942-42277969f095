using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Gestionnaire amélioré pour la téléportation des bateaux, 
/// prenant en compte les bateaux en course et ceux hors course
/// </summary>
public class ImprovedTeleportationManager : MonoBehaviour
{
    [Header("Téléportation")]
    [<PERSON><PERSON><PERSON>("Touche pour déclencher la téléportation")]
    [SerializeField] private KeyCode teleportKey = KeyCode.T;
    
    [Toolt<PERSON>("Durée réduite d'ignorance GPS pour les bateaux en course")]
    [SerializeField] private float raceBoatIgnoreDuration = 0.5f;
    
    private RaceController raceController;
    
    private void Start()
    {
        raceController = FindObjectOfType<RaceController>();
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(teleportKey))
        {
            TeleportAllBoatsImproved();
        }
    }
    
    /// <summary>
    /// Téléporte tous les bateaux avec gestion améliorée pour ceux en course
    /// </summary>
    public void TeleportAllBoatsImproved()
    {
        Debug.Log("[ImprovedTeleportationManager] Début de la téléportation améliorée");
        
        // Trouver tous les scripts UnifiedBoatGPS
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allBoats.Length == 0)
        {
            Debug.LogWarning("[ImprovedTeleportationManager] Aucun bateau trouvé");
            return;
        }
        
        // Séparer les bateaux en course des autres
        List<UnifiedBoatGPS> raceBoats = new List<UnifiedBoatGPS>();
        List<UnifiedBoatGPS> freeBoats = new List<UnifiedBoatGPS>();
        
        foreach (var boat in allBoats)
        {
            if (IsBoatInRace(boat.gameObject))
            {
                raceBoats.Add(boat);
            }
            else
            {
                freeBoats.Add(boat);
            }
        }
        
        Debug.Log($"[ImprovedTeleportationManager] Bateaux en course: {raceBoats.Count}, Bateaux libres: {freeBoats.Count}");
        
        // Téléporter les bateaux en course avec gestion spéciale
        TeleportRaceBoats(raceBoats);
        
        // Téléporter les bateaux libres normalement
        TeleportFreeBoats(freeBoats);
    }
    
    /// <summary>
    /// Vérifie si un bateau est actuellement en course
    /// </summary>
    private bool IsBoatInRace(GameObject boat)
    {
        if (raceController == null || !raceController.IsRaceStarted())
            return false;
            
        return raceController.GetBoats().Contains(boat);
    }
    
    /// <summary>
    /// Téléporte les bateaux en course avec gestion spéciale des Rigidbodies
    /// </summary>
    private void TeleportRaceBoats(List<UnifiedBoatGPS> raceBoats)
    {
        foreach (var boatGPS in raceBoats)
        {
            // Sauvegarder l'état du Rigidbody
            Rigidbody rb = boatGPS.GetComponent<Rigidbody>();
            bool wasKinematic = false;
            bool hadCollisions = true;
            
            if (rb != null)
            {
                wasKinematic = rb.isKinematic;
                hadCollisions = rb.detectCollisions;
                
                // Temporairement réactiver la physique pour la téléportation
                rb.isKinematic = false;
                rb.detectCollisions = true;
            }
            
            Debug.Log($"[ImprovedTeleportationManager] Téléportation bateau en course: {boatGPS.gameObject.name}");
            
            // Effectuer la téléportation avec durée d'ignorance GPS réduite
            boatGPS.ManualTeleportToLatestGPS(raceBoatIgnoreDuration);
            
            // Restaurer l'état du Rigidbody après un délai
            if (rb != null)
            {
                StartCoroutine(RestoreRigidbodyState(rb, wasKinematic, hadCollisions));
            }
        }
    }
    
    /// <summary>
    /// Téléporte les bateaux libres normalement
    /// </summary>
    private void TeleportFreeBoats(List<UnifiedBoatGPS> freeBoats)
    {
        foreach (var boatGPS in freeBoats)
        {
            Debug.Log($"[ImprovedTeleportationManager] Téléportation bateau libre: {boatGPS.gameObject.name}");
            boatGPS.ManualTeleportToLatestGPS();
        }
    }
    
    /// <summary>
    /// Restaure l'état du Rigidbody après la téléportation
    /// </summary>
    private System.Collections.IEnumerator RestoreRigidbodyState(Rigidbody rb, bool wasKinematic, bool hadCollisions)
    {
        // Attendre un frame pour que la téléportation soit complète
        yield return new WaitForEndOfFrame();
        
        if (rb != null)
        {
            rb.isKinematic = wasKinematic;
            rb.detectCollisions = hadCollisions;
            Debug.Log($"[ImprovedTeleportationManager] État Rigidbody restauré pour {rb.gameObject.name}");
        }
    }
    
}