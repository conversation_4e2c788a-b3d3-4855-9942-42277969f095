using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Script de diagnostic pour analyser les problèmes de téléportation
/// </summary>
public class TeleportationDiagnostic : MonoBehaviour
{
    [Header("Diagnostic")]
    [SerializeField] private KeyCode diagnosticKey = KeyCode.F1;
    
    private RaceController raceController;
    
    private void Start()
    {
        raceController = FindObjectOfType<RaceController>();
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(diagnosticKey))
        {
            RunDiagnostic();
        }
    }
    
    /// <summary>
    /// Exécute un diagnostic complet des bateaux
    /// </summary>
    public void RunDiagnostic()
    {
        Debug.Log("=== DIAGNOSTIC DE TÉLÉPORTATION ===");
        
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        Debug.Log($"Nombre total de bateaux avec UnifiedBoatGPS: {allBoats.Length}");
        
        foreach (var boat in allBoats)
        {
            AnalyzeBoat(boat);
        }
        
        Debug.Log("=== FIN DU DIAGNOSTIC ===");
    }
    
    /// <summary>
    /// Analyse un bateau spécifique
    /// </summary>
    private void AnalyzeBoat(UnifiedBoatGPS boatGPS)
    {
        GameObject boat = boatGPS.gameObject;
        
        Debug.Log($"\n--- ANALYSE: {boat.name} ---");
        
        // Vérifier si en course
        bool isInRace = IsBoatInRace(boat);
        Debug.Log($"En course: {isInRace}");
        
        // Analyser le Rigidbody
        Rigidbody rb = boat.GetComponent<Rigidbody>();
        if (rb != null)
        {
            Debug.Log($"Rigidbody - Kinematic: {rb.isKinematic}, Collisions: {rb.detectCollisions}");
            Debug.Log($"Rigidbody - Mass: {rb.mass}, Drag: {rb.drag}");
        }
        else
        {
            Debug.Log("Aucun Rigidbody trouvé");
        }
        
        // Analyser la position
        Debug.Log($"Position Unity: {boat.transform.position}");
        Debug.Log($"Position GPS: {boatGPS.GetCurrentPosition()}");
        
        // Vérifier l'état GPS
        Debug.Log($"Ignore GPS Updates: {boatGPS._ignoreGPSUpdatesTemporarily}");
        
        // Vérifier la hiérarchie
        Transform parent = boat.transform.parent;
        string parentHierarchy = "";
        while (parent != null)
        {
            parentHierarchy = parent.name + "/" + parentHierarchy;
            parent = parent.parent;
        }
        Debug.Log($"Hiérarchie: {parentHierarchy}{boat.name}");
        
        // Vérifier les composants
        Component[] components = boat.GetComponents<Component>();
        Debug.Log($"Composants: {string.Join(", ", components.Select(c => c.GetType().Name))}");
    }
    
    /// <summary>
    /// Vérifie si un bateau est en course
    /// </summary>
    private bool IsBoatInRace(GameObject boat)
    {
        if (raceController == null || !raceController.IsRaceStarted())
            return false;
            
        return raceController.GetBoats().Contains(boat);
    }
    
    /// <summary>
    /// Compare les bateaux en course vs libres
    /// </summary>
    [ContextMenu("Comparer Bateaux")]
    public void CompareBoats()
    {
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        var raceBoats = allBoats.Where(b => IsBoatInRace(b.gameObject)).ToList();
        var freeBoats = allBoats.Where(b => !IsBoatInRace(b.gameObject)).ToList();
        
        Debug.Log($"\n=== COMPARAISON ===");
        Debug.Log($"Bateaux en course: {raceBoats.Count}");
        Debug.Log($"Bateaux libres: {freeBoats.Count}");
        
        // Analyser les différences dans les Rigidbodies
        var raceRigidBodies = raceBoats.Select(b => b.GetComponent<Rigidbody>()).Where(rb => rb != null);
        var freeRigidBodies = freeBoats.Select(b => b.GetComponent<Rigidbody>()).Where(rb => rb != null);
        
        Debug.Log($"Bateaux course - Kinematic: {raceRigidBodies.Count(rb => rb.isKinematic)}/{raceRigidBodies.Count()}");
        Debug.Log($"Bateaux libres - Kinematic: {freeRigidBodies.Count(rb => rb.isKinematic)}/{freeRigidBodies.Count()}");
    }
}