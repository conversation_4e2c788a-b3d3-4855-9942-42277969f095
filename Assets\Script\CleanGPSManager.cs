using UnityEngine;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Gestionnaire central propre pour tous les bateaux GPS
/// Remplace tout le bordel de gestionnaires existants
/// </summary>
public class CleanGPSManager : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool autoDiscoverBoats = true;
    [SerializeField] private bool showDebugLogs = true;
    
    // Dictionnaire des bateaux gérés
    private Dictionary<string, CleanGPSSystem> managedBoats = new Dictionary<string, CleanGPSSystem>();
    
    // Singleton
    public static CleanGPSManager Instance { get; private set; }
    
    private void Awake()
    {
        // Singleton
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
        
        LogDebug("🚀 CleanGPSManager initialisé");
    }
    
    private void Start()
    {
        if (autoDiscoverBoats)
        {
            Invoke(nameof(DiscoverAllBoats), 0.5f);
        }
    }
    
    /// <summary>
    /// Découvre automatiquement tous les bateaux avec CleanGPSSystem
    /// </summary>
    [ContextMenu("Découvrir Tous les Bateaux")]
    public void DiscoverAllBoats()
    {
        LogDebug("🔍 === DÉCOUVERTE DES BATEAUX ===");
        
        var allGPSBoats = FindObjectsOfType<CleanGPSSystem>();
        
        if (allGPSBoats.Length == 0)
        {
            LogDebug("❌ Aucun bateau avec CleanGPSSystem trouvé");
            return;
        }
        
        int registeredCount = 0;
        
        foreach (var gpsBoat in allGPSBoats)
        {
            string boatId = GetBoatId(gpsBoat.gameObject);
            
            if (RegisterBoat(boatId, gpsBoat))
            {
                registeredCount++;
            }
        }
        
        LogDebug($"✅ Découverte terminée: {registeredCount}/{allGPSBoats.Length} bateaux enregistrés");
        LogDebug("🔍 === FIN DE LA DÉCOUVERTE ===");
    }
    
    /// <summary>
    /// Enregistre un bateau avec un ID spécifique
    /// </summary>
    public bool RegisterBoat(string boatId, CleanGPSSystem gpsSystem)
    {
        if (string.IsNullOrEmpty(boatId) || gpsSystem == null)
        {
            LogDebug("❌ ID ou système GPS invalide");
            return false;
        }
        
        if (managedBoats.ContainsKey(boatId))
        {
            LogDebug($"⚠️ Bateau {boatId} déjà enregistré");
            return false;
        }
        
        managedBoats[boatId] = gpsSystem;
        LogDebug($"📋 Bateau enregistré: {boatId} → {gpsSystem.gameObject.name}");
        return true;
    }
    
    /// <summary>
    /// Met à jour la position GPS d'un bateau
    /// MÉTHODE PRINCIPALE appelée par les sources de données GPS
    /// </summary>
    public void UpdateBoatGPS(string boatId, Vector2d gpsPosition)
    {
        if (string.IsNullOrEmpty(boatId))
        {
            LogDebug("❌ ID de bateau vide");
            return;
        }
        
        if (managedBoats.TryGetValue(boatId, out CleanGPSSystem gpsSystem))
        {
            gpsSystem.UpdateGPSPosition(gpsPosition);
            LogDebug($"📍 GPS mis à jour pour {boatId}: {gpsPosition}");
        }
        else
        {
            // Essayer de créer automatiquement
            TryAutoRegisterBoat(boatId, gpsPosition);
        }
    }
    
    /// <summary>
    /// Met à jour la position GPS d'un bateau via GameObject
    /// </summary>
    public void UpdateBoatGPS(GameObject boatObject, Vector2d gpsPosition)
    {
        if (boatObject == null) return;
        
        var gpsSystem = boatObject.GetComponent<CleanGPSSystem>();
        if (gpsSystem != null)
        {
            gpsSystem.UpdateGPSPosition(gpsPosition);
            LogDebug($"📍 GPS mis à jour pour {boatObject.name}: {gpsPosition}");
        }
        else
        {
            LogDebug($"❌ {boatObject.name} n'a pas de CleanGPSSystem");
        }
    }
    
    /// <summary>
    /// Essaie d'enregistrer automatiquement un bateau
    /// </summary>
    private void TryAutoRegisterBoat(string boatId, Vector2d gpsPosition)
    {
        LogDebug($"🔍 Tentative d'enregistrement automatique pour {boatId}");
        
        var allGPSBoats = FindObjectsOfType<CleanGPSSystem>();
        
        foreach (var gpsBoat in allGPSBoats)
        {
            // Vérifier si ce bateau n'est pas encore enregistré
            bool isAlreadyRegistered = false;
            foreach (var kvp in managedBoats)
            {
                if (kvp.Value == gpsBoat)
                {
                    isAlreadyRegistered = true;
                    break;
                }
            }
            
            if (!isAlreadyRegistered)
            {
                // Enregistrer ce bateau avec l'ID
                managedBoats[boatId] = gpsBoat;
                gpsBoat.UpdateGPSPosition(gpsPosition);
                LogDebug($"✅ Bateau {gpsBoat.gameObject.name} auto-enregistré pour {boatId}");
                return;
            }
        }
        
        LogDebug($"❌ Aucun bateau disponible pour {boatId}");
    }
    
    /// <summary>
    /// Supprime un bateau de la gestion
    /// </summary>
    public void UnregisterBoat(string boatId)
    {
        if (managedBoats.ContainsKey(boatId))
        {
            managedBoats.Remove(boatId);
            LogDebug($"🗑️ Bateau {boatId} supprimé");
        }
    }
    
    /// <summary>
    /// Remet à zéro tous les bateaux GPS
    /// </summary>
    [ContextMenu("Reset Tous les GPS")]
    public void ResetAllGPS()
    {
        LogDebug("🔄 === RESET DE TOUS LES GPS ===");
        
        foreach (var kvp in managedBoats)
        {
            if (kvp.Value != null)
            {
                kvp.Value.ResetGPS();
                LogDebug($"🔄 GPS reset pour {kvp.Key}");
            }
        }
        
        LogDebug("🔄 === FIN DU RESET ===");
    }
    
    /// <summary>
    /// Affiche les informations de tous les bateaux
    /// </summary>
    [ContextMenu("Afficher Infos Tous les Bateaux")]
    public void ShowAllBoatsInfo()
    {
        LogDebug("📊 === INFORMATIONS DE TOUS LES BATEAUX ===");
        LogDebug($"Nombre de bateaux gérés: {managedBoats.Count}");
        
        foreach (var kvp in managedBoats)
        {
            string boatId = kvp.Key;
            CleanGPSSystem gpsSystem = kvp.Value;
            
            if (gpsSystem != null)
            {
                Vector2d gpsPos = gpsSystem.GetCurrentGPSPosition();
                float speed = gpsSystem.GetCurrentSpeedKnots();
                bool hasData = gpsSystem.HasGPSData();
                
                LogDebug($"🚤 {boatId} ({gpsSystem.gameObject.name}):");
                LogDebug($"   GPS: {gpsPos}");
                LogDebug($"   Vitesse: {speed:F1} nœuds");
                LogDebug($"   Données: {(hasData ? "✅" : "❌")}");
            }
            else
            {
                LogDebug($"❌ {boatId}: Système GPS NULL");
            }
        }
        
        LogDebug("📊 === FIN DES INFORMATIONS ===");
    }
    
    /// <summary>
    /// Retourne la liste des IDs de bateaux enregistrés
    /// </summary>
    public List<string> GetRegisteredBoatIds()
    {
        return new List<string>(managedBoats.Keys);
    }
    
    /// <summary>
    /// Retourne le système GPS pour un ID donné
    /// </summary>
    public CleanGPSSystem GetBoatGPS(string boatId)
    {
        managedBoats.TryGetValue(boatId, out CleanGPSSystem gpsSystem);
        return gpsSystem;
    }
    
    /// <summary>
    /// Vérifie si un bateau est enregistré
    /// </summary>
    public bool IsBoatRegistered(string boatId)
    {
        return managedBoats.ContainsKey(boatId);
    }
    
    /// <summary>
    /// Retourne le nombre de bateaux gérés
    /// </summary>
    public int GetManagedBoatsCount()
    {
        return managedBoats.Count;
    }
    
    /// <summary>
    /// Détermine l'ID d'un bateau basé sur ses composants
    /// </summary>
    private string GetBoatId(GameObject boat)
    {
        // Essayer de récupérer l'ID depuis TeamInfo
        var teamInfo = boat.GetComponent<TeamInfo>();
        if (teamInfo != null)
        {
            string teamName = teamInfo.GetTeamName();
            if (!string.IsNullOrEmpty(teamName))
            {
                return teamName;
            }
        }
        
        // Fallback sur le nom du GameObject
        return boat.name;
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[CleanGPSManager] {message}");
        }
    }
    
    private void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }
    }
}
