using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Classe principale pour la gestion des positions GPS des bateaux
/// </summary>
public class UnifiedBoatGPS : MonoBehaviour
{
    [Header("Map Settings")]
    [SerializeField] private AbstractMap map;
    [SerializeField] private float heightOffset = 1f;

    [Header("Path Settings")]
    [SerializeField] private bool showPaths = true;
    [SerializeField] private Color pathColor = Color.red;
    [SerializeField] private float lineWidth = 0.5f;
    [SerializeField] private int maxPathPoints = 100;

    [Header("Boat Movement")]
    [SerializeField] private float pathFollowDelay = 3f;         // Délai du GPS
    [SerializeField] private float maxSpeed = 10f;               // Vitesse max
    [SerializeField] private float turningSpeed = 30f;          // Vitesse de rotation de base
    [SerializeField] private float inertiaFactor = 0.8f;        // Influence de la vitesse sur le rayon de virage

    [Header("GPS Settings")]
    [SerializeField] private float gpsAccuracy = 2.0f;           // Précision du GPS en mètres (1.5-2m)
    [SerializeField] private float positionSmoothingFactor = 0.3f; // Facteur de lissage pour les petites variations GPS

    [Header("Rotation Settings")]
    [SerializeField] private float minHeadingSmoothTime = 0.8f;  // Temps minimum de lissage du cap (bateau lent)
    [SerializeField] private float maxHeadingSmoothTime = 0.2f;  // Temps maximum de lissage du cap (bateau rapide)

    [Header("Marine Effects")]
    [SerializeField] private float bobHeight = 0.02f;
    [SerializeField] private float bobSpeed = 1f;
    [SerializeField] private float rollAmount = 5f;

    [Header("Debug")]
    [SerializeField] private bool showSpeedLogs = true;
    [SerializeField] private bool showPositionLogs = false;
    [SerializeField] private float maxValidTeleportDistance = 100f; // Distance max en mètres pour une téléportation sans transition
    [SerializeField] private float teleportTransitionDuration = 1.5f; // Durée de la transition en secondes

    // Composants
    private GPSPositionConverter gpsConverter;
    private BoatMovementController movementController;
    private MarineEffectsController effectsController;

    // Variables internes
    private Vector2d? queuedInitialPosition = null;
    private Vector2d currentGPSPosition;
    private Vector2d lastValidGPSPosition;
    private LineRenderer pathRenderer;
    private readonly List<Vector3> pathPoints = new();
    private static Transform pathsContainer; // Conteneur unique pour tous les chemins
    private bool _isReadyForTeleport = false; // Indique si la carte est initialisée et prête pour la téléportation
    private bool _teleportQueued = false; // Indique si une téléportation manuelle est en attente
    public bool _ignoreGPSUpdatesTemporarily = false; // Nouveau: Ignorer les mises à jour GPS après téléportation manuelle
    [SerializeField] private float _gpsUpdateIgnoreDuration = 1.0f; // Durée pendant laquelle ignorer les mises à jour GPS

    // Événements
    public event System.Action OnMapReady;

    private void Start()
    {
        InitializeComponents();
        if (showPaths) SetupPathRenderer();
    }

    private void InitializeComponents()
    {
        // Initialiser la carte si nécessaire
        if (!map)
        {
            map = FindFirstObjectByType<AbstractMap>();
            Debug.Log($"[UnifiedBoatGPS] Recherche de carte Mapbox : {(map != null ? "trouvée" : "non trouvée")}");
        }

        if (map == null)
        {
            Debug.LogError("[UnifiedBoatGPS] Pas de carte Mapbox trouvée!");
            return;
        }

        // Initialiser les composants
        gpsConverter = new GPSPositionConverter(map, heightOffset);
        movementController = new BoatMovementController(pathFollowDelay, maxSpeed, turningSpeed, inertiaFactor);
        effectsController = new MarineEffectsController(bobHeight, bobSpeed, rollAmount, heightOffset);

        // Configurer les paramètres
        movementController.MaxValidTeleportDistance = maxValidTeleportDistance;
        movementController.TeleportTransitionDuration = teleportTransitionDuration;
        movementController.GPSAccuracy = gpsAccuracy;
        movementController.PositionSmoothingFactor = positionSmoothingFactor;
        movementController.MinHeadingSmoothTime = minHeadingSmoothTime;
        movementController.MaxHeadingSmoothTime = maxHeadingSmoothTime;

        // S'abonner à l'événement d'initialisation de la carte
        map.OnInitialized += OnMapInitialized;

        // Si la carte est déjà initialisée, déclencher manuellement
        if (map.CenterLatitudeLongitude != default)
        {
            OnMapInitialized();
        }
    }

    private void OnMapInitialized()
    {
        OnMapReady?.Invoke();
        _isReadyForTeleport = true; // La carte est maintenant prête pour la téléportation

        // Traiter la téléportation manuelle en attente et la position initiale
        ProcessQueuedTeleportsForThisBoat();
    }

    /// <summary>
    /// Traite les téléportations et positions initiales en file d'attente pour cette instance de bateau.
    /// </summary>
    private void ProcessQueuedTeleportsForThisBoat()
    {
        if (_teleportQueued && _isReadyForTeleport)
        {
            ManualTeleportToLatestGPS();
            if (!_teleportQueued)
            {
                // _teleportQueued a été remis à false par ManualTeleportToLatestGPS
            }
            else
            {
                _teleportQueued = false;
            }
        }

        if (queuedInitialPosition.HasValue && _isReadyForTeleport)
        {
            SetInitialPosition(queuedInitialPosition.Value);
            queuedInitialPosition = null;
        }
    }

    /// <summary>
    /// Met en file d'attente une position initiale à définir lorsque la carte sera initialisée
    /// </summary>
    public void QueueInitialPosition(Vector2d initialGPSPosition)
    {
        queuedInitialPosition = initialGPSPosition;
        Debug.Log($"[UnifiedBoatGPS] Position initiale ({initialGPSPosition}) mise en file d'attente pour {gameObject.name}.");

        // Si la carte est déjà initialisée, définir la position immédiatement
        if (gpsConverter != null && gpsConverter.IsMapInitialized)
        {
            SetInitialPosition(initialGPSPosition);
            queuedInitialPosition = null;
        }
    }

    /// <summary>
    /// Définit la position initiale du bateau
    /// </summary>
    public void SetInitialPosition(Vector2d initialGPSPosition)
    {
        if (gpsConverter == null || !gpsConverter.IsMapInitialized || !_isReadyForTeleport)
        {
            QueueInitialPosition(initialGPSPosition);
            return;
        }

        if (!GPSPositionConverter.IsValidGPSCoordinate(initialGPSPosition))
        {
            return;
        }

        currentGPSPosition = initialGPSPosition;
        lastValidGPSPosition = initialGPSPosition;

        Vector3 initialUnityPosition = gpsConverter.GPSToUnityPosition(initialGPSPosition);
        movementController.TeleportImmediately(transform, initialUnityPosition, 0f);
    }

    /// <summary>
    /// Met à jour la position réelle du bateau à partir des coordonnées GPS
    /// </summary>
    public void UpdateRealPosition(Vector2d newPosition, float newHeading, float timestamp)
    {
        if (_ignoreGPSUpdatesTemporarily)
        {
            return;
        }

        if (gpsConverter == null || !gpsConverter.IsMapInitialized || map == null || map.CenterLatitudeLongitude == default)
        {
            return;
        }

        if (!GPSPositionConverter.IsValidGPSCoordinate(newPosition))
        {
            return;
        }

        Vector3 newUnityPosition = gpsConverter.GPSToUnityPosition(newPosition);
        float speed = CalculateApproximateSpeed(newPosition);
        movementController.AddNewPosition(newUnityPosition, newHeading, speed, timestamp);

        if (showPaths) AddPathPoint(newUnityPosition);

        currentGPSPosition = newPosition;
        lastValidGPSPosition = newPosition;
    }

    /// <summary>
    /// Calcule la vitesse approximative basée sur la distance entre la position actuelle et la nouvelle position
    /// </summary>
    private float CalculateApproximateSpeed(Vector2d newPosition)
    {
        // Si c'est la première position, retourner 0
        if (currentGPSPosition == default) return 0f;

        // Calculer la distance en mètres entre les deux positions GPS
        float distance = GPSPositionConverter.CalculateDistance(currentGPSPosition, newPosition);

        // Si la distance est inférieure à la précision du GPS, considérer que le bateau est immobile
        if (distance < gpsAccuracy) return 0f;

        // Calculer le temps écoulé depuis la dernière mise à jour (en secondes)
        float timeElapsed = Time.deltaTime > 0 ? Time.deltaTime : 0.1f; // Éviter la division par zéro

        // Calculer la vitesse en m/s
        float speedMS = distance / timeElapsed;

        // Limiter la vitesse maximale
        return Mathf.Min(speedMS, maxSpeed);
    }

    private void Update()
    {
        if (gpsConverter == null || !gpsConverter.IsMapInitialized) return;

        // Mettre à jour la cible et le mouvement
        movementController.UpdateTarget(Time.time);
        movementController.UpdateMovement(transform, Time.deltaTime);

        // Appliquer les effets marins
        effectsController.ApplyEffects(transform, movementController.CurrentSpeed, maxSpeed);
    }

    /// <summary>
    /// Retourne la vitesse actuelle en nœuds
    /// </summary>
    public float GetCurrentSpeed()
    {
        return movementController.CurrentSpeed * 1.944f; // Conversion en nœuds
    }

    /// <summary>
    /// Retourne la position GPS actuelle
    /// </summary>
    public Vector2d GetCurrentPosition()
    {
        return currentGPSPosition;
    }

    /// <summary>
    /// Déclenche manuellement une téléportation vers la dernière position GPS valide
    /// </summary>
    public void ManualTeleportToLatestGPS()
    {
        ManualTeleportToLatestGPS(_gpsUpdateIgnoreDuration * 10f);
    }

    /// <summary>
    /// Déclenche manuellement une téléportation avec une durée d'ignorance GPS personnalisée
    /// </summary>
    /// <param name="customIgnoreDuration">Durée personnalisée d'ignorance des mises à jour GPS</param>
    public void ManualTeleportToLatestGPS(float customIgnoreDuration)
    {
        if (!_isReadyForTeleport)
        {
            _teleportQueued = true;
            return;
        }

        Vector2d teleportTargetGPS = lastValidGPSPosition;

        if (teleportTargetGPS == default)
        {
            if (queuedInitialPosition.HasValue)
            {
                teleportTargetGPS = queuedInitialPosition.Value;
            }
            else
            {
                return;
            }
        }

        Vector3 targetPosition = gpsConverter.GPSToUnityPosition(teleportTargetGPS);

        movementController.TeleportImmediately(transform, targetPosition, transform.eulerAngles.y);
        currentGPSPosition = teleportTargetGPS;
        lastValidGPSPosition = teleportTargetGPS;
        _ignoreGPSUpdatesTemporarily = true;
        CancelInvoke(nameof(ResetGPSUpdateIgnoreFlag));
        Invoke(nameof(ResetGPSUpdateIgnoreFlag), customIgnoreDuration);
        _teleportQueued = false;
    }

    private void SetupPathRenderer()
    {
        if (!showPaths) return;

        // Créer ou obtenir le conteneur de chemins
        if (pathsContainer == null)
        {
            GameObject container = GameObject.Find("BoatPaths");
            if (container == null)
            {
                container = new GameObject("BoatPaths");
                // Placer le conteneur à la racine de la scène
                container.transform.SetParent(null);
            }
            pathsContainer = container.transform;
        }

        // Créer l'objet de chemin avec un nom unique
        GameObject pathObj = new($"BoatPath_{gameObject.name}");
        // Placer le chemin dans le conteneur dédié
        pathObj.transform.SetParent(pathsContainer);
        pathRenderer = pathObj.AddComponent<LineRenderer>();

        pathRenderer.startWidth = lineWidth;
        pathRenderer.endWidth = lineWidth;
        pathRenderer.material = new Material(Shader.Find("Sprites/Default"));
        pathRenderer.startColor = pathColor;
        pathRenderer.endColor = pathColor;
        pathRenderer.positionCount = 0;
        pathRenderer.useWorldSpace = true;
    }

    private void AddPathPoint(Vector3 newPoint)
    {
        if (!showPaths || pathRenderer == null) return;

        pathPoints.Add(newPoint);
        if (pathPoints.Count > maxPathPoints)
        {
            pathPoints.RemoveAt(0);
        }

        pathRenderer.positionCount = pathPoints.Count;
        pathRenderer.SetPositions(pathPoints.ToArray());
    }

    private void OnDestroy()
    {
        if (map != null)
        {
            map.OnInitialized -= OnMapInitialized;
        }

        // Nettoyer le chemin si nécessaire
        if (pathRenderer != null)
        {
            Destroy(pathRenderer.gameObject);
        }

        // Nettoyer le conteneur de chemins s'il est vide
        if (pathsContainer != null && pathsContainer.childCount == 0)
        {
            Destroy(pathsContainer.gameObject);
            pathsContainer = null;
        }
    }

    private void ResetGPSUpdateIgnoreFlag()
    {
        _ignoreGPSUpdatesTemporarily = false;
    }
}
