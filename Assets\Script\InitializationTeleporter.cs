using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Gère la téléportation d'initialisation des bateaux au démarrage de l'application
/// pour aligner les positions Unity avec les positions GPS réelles
/// </summary>
public class InitializationTeleporter : MonoBehaviour
{
    [Header("Configuration")]
    [Tooltip("Touche pour déclencher la téléportation d'initialisation")]
    [SerializeField] private KeyCode teleportKey = KeyCode.T;
    
    [Tooltip("Délai avant de commencer à suivre le GPS après téléportation (secondes)")]
    [SerializeField] private float postTeleportDelay = 2f;
    
    [Tooltip("Téléporter automatiquement au démarrage")]
    [SerializeField] private bool autoTeleportOnStart = false;
    
    [Tooltip("Délai avant la téléportation automatique (secondes)")]
    [SerializeField] private float autoTeleportDelay = 3f;
    
    private bool hasInitialized = false;
    
    private void Start()
    {
        if (autoTeleportOnStart)
        {
            StartCoroutine(AutoTeleportAfterDelay());
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(teleportKey))
        {
            PerformInitializationTeleport();
        }
    }
    
    /// <summary>
    /// Téléportation automatique après un délai
    /// </summary>
    private IEnumerator AutoTeleportAfterDelay()
    {
        Debug.Log($"[InitializationTeleporter] Téléportation automatique dans {autoTeleportDelay} secondes...");
        yield return new WaitForSeconds(autoTeleportDelay);
        PerformInitializationTeleport();
    }
    
    /// <summary>
    /// Effectue la téléportation d'initialisation de tous les bateaux
    /// </summary>
    public void PerformInitializationTeleport()
    {
        Debug.Log("[InitializationTeleporter] === TÉLÉPORTATION D'INITIALISATION ===");
        
        // Trouver tous les bateaux avec UnifiedBoatGPS
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allBoats.Length == 0)
        {
            Debug.LogWarning("[InitializationTeleporter] Aucun bateau avec UnifiedBoatGPS trouvé!");
            return;
        }
        
        Debug.Log($"[InitializationTeleporter] {allBoats.Length} bateaux trouvés");
        
        // Catégoriser les bateaux
        List<UnifiedBoatGPS> fleetBoats = new List<UnifiedBoatGPS>();
        UnifiedBoatGPS officialBoat = null;
        
        foreach (var boat in allBoats)
        {
            // Vérifier si c'est le bateau officiel (plusieurs méthodes possibles)
            bool isOfficialBoat = false;
            
            // Méthode 1: Par le nom
            if (boat.gameObject.name.ToLower().Contains("official") || 
                boat.gameObject.name.ToLower().Contains("zodiac"))
            {
                isOfficialBoat = true;
            }
            
            // Méthode 2: Par le parent (si dans un dossier spécifique)
            if (!isOfficialBoat && boat.transform.parent != null)
            {
                if (boat.transform.parent.name.ToLower() != "fleet")
                {
                    // Peut-être le bateau officiel s'il n'est pas dans fleet
                    isOfficialBoat = true;
                }
            }
            
            if (isOfficialBoat)
            {
                officialBoat = boat;
                Debug.Log($"[InitializationTeleporter] Bateau officiel identifié: {boat.gameObject.name}");
            }
            else
            {
                fleetBoats.Add(boat);
                Debug.Log($"[InitializationTeleporter] Bateau de course: {boat.gameObject.name}");
            }
        }
        
        // Téléporter tous les bateaux avec le délai approprié
        foreach (var boat in allBoats)
        {
            // Utiliser un délai court pour l'initialisation
            boat.ManualTeleportToLatestGPS(postTeleportDelay);
            
            // Log détaillé
            var currentPos = boat.transform.position;
            var gpsPos = boat.GetCurrentPosition();
            Debug.Log($"[InitializationTeleporter] {boat.gameObject.name} téléporté");
            Debug.Log($"  - Position Unity avant: {currentPos}");
            Debug.Log($"  - Position GPS cible: {gpsPos}");
        }
        
        hasInitialized = true;
        
        // Afficher un résumé
        StartCoroutine(ShowTeleportSummary(allBoats, 0.5f));
    }
    
    /// <summary>
    /// Affiche un résumé après la téléportation
    /// </summary>
    private IEnumerator ShowTeleportSummary(UnifiedBoatGPS[] boats, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        Debug.Log("[InitializationTeleporter] === RÉSUMÉ POST-TÉLÉPORTATION ===");
        foreach (var boat in boats)
        {
            if (boat != null)
            {
                Debug.Log($"[InitializationTeleporter] {boat.gameObject.name} - Position finale: {boat.transform.position}");
            }
        }
        Debug.Log("[InitializationTeleporter] === FIN DE L'INITIALISATION ===");
    }
    
    /// <summary>
    /// Vérifie si l'initialisation a été effectuée
    /// </summary>
    public bool HasInitialized()
    {
        return hasInitialized;
    }
    
    /// <summary>
    /// Réinitialise le statut (utile pour les tests)
    /// </summary>
    [ContextMenu("Reset Initialization Status")]
    public void ResetInitializationStatus()
    {
        hasInitialized = false;
        Debug.Log("[InitializationTeleporter] Statut d'initialisation réinitialisé");
    }
}