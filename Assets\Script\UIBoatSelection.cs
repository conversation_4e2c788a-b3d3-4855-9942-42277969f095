using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

public class UIBoatSelection : MonoBehaviour
{
    [Header("Références")]
    [SerializeField] private Transform fleetContainer;
    [SerializeField] private Transform selectionContainer;
    [SerializeField] private GameObject boatEntryPrefab;
    [SerializeField] private Button confirmButton;
    [SerializeField] private RaceController raceController;

    // Gardez la liste privée pour la gestion interne
    private List<GameObject> _selectedBoats = new List<GameObject>();

    private void Start()
    {
        if (fleetContainer == null)
            fleetContainer = GameObject.Find("Fleet").transform;

        // Assurez-vous que la liste est vide avant de peupler
        _selectedBoats.Clear();

        PopulateBoatList();

        // Mettez à jour l'état du bouton après le peuplement initial
        confirmButton.interactable = _selectedBoats.Count > 0;
        confirmButton.onClick.AddListener(ConfirmSelection);
    }

    private void PopulateBoatList()
    {
        foreach (Transform child in selectionContainer)
            Destroy(child.gameObject);

        var boats = fleetContainer.GetComponentsInChildren<Transform>(true)
            .Where(t => t != fleetContainer && t.parent == fleetContainer);

        foreach (var boatTransform in boats) // Renommé pour clarté
        {
            GameObject boatGameObject = boatTransform.gameObject; // Obtenir le GameObject

            // NOUVEAU: Vérifier si c'est le bateau officiel et l'exclure de la sélection
            if (IsOfficialBoat(boatGameObject))
            {
                Debug.Log($"[UIBoatSelection] Le bateau officiel '{boatGameObject.name}' est exclu de la sélection de course.");
                continue; // Passer au bateau suivant
            }

            var entry = Instantiate(boatEntryPrefab, selectionContainer);
            var entryUI = entry.GetComponent<UIBoatEntry>();

            if (entryUI != null)
            {
                // Initialisez l'entrée comme d'habitude
                entryUI.Initialize(boatGameObject, this);

                // --- NOUVEAU : Sélection par défaut (seulement pour les bateaux de course) ---

                // 1. Ajoutez le bateau à la liste des sélectionnés
                _selectedBoats.Add(boatGameObject);

                // 2. Mettez à jour l'état visuel (Toggle/Checkbox) de l'entrée
                //    Suppose que votre prefab boatEntryPrefab a un composant Toggle
                //    directement sur l'objet racine ou un enfant facile d'accès.
                Toggle entryToggle = entry.GetComponentInChildren<Toggle>(); // Ou GetComponent<Toggle>() si sur la racine
                if (entryToggle != null)
                {
                    // Important: Mettez à jour la valeur sans déclencher l'événement
                    // OnValueChanged qui appellerait ToggleBoatSelection à nouveau.
                    entryToggle.SetIsOnWithoutNotify(true);
                }
                else
                {
                    Debug.LogWarning($"Le prefab {boatEntryPrefab.name} n'a pas de composant Toggle accessible pour la sélection par défaut.");
                    // Si vous n'utilisez pas de Toggle, adaptez cette partie
                    // pour modifier l'apparence visuelle de la sélection.
                }
                // -----------------------------------------
            }
            else
            {
                 Debug.LogError($"Le prefab {boatEntryPrefab.name} n'a pas de composant UIBoatEntry.");
            }
        }
         // Note : L'état du bouton confirmButton est maintenant mis à jour dans Start,
         // après l'appel de PopulateBoatList.
    }

    /// <summary>
    /// Vérifie si un bateau est le bateau officiel
    /// </summary>
    private bool IsOfficialBoat(GameObject boat)
    {
        // Vérifier par nom (si le bateau officiel a un nom spécifique)
        if (boat.name.ToLower().Contains("official") || boat.name.ToLower().Contains("officiel"))
        {
            return true;
        }

        // Vérifier via RaceController si disponible
        if (raceController != null)
        {
            // Accéder au Transform du bateau officiel depuis RaceController
            var officialBoatTransform = GetOfficialBoatTransform();
            if (officialBoatTransform != null && boat.transform == officialBoatTransform)
            {
                return true;
            }
        }

        // Vérifier via MeshtasticGPSTracker si disponible
        var meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticTracker != null)
        {
            var officialBoatNodeId = GetOfficialBoatNodeId(meshtasticTracker);
            if (!string.IsNullOrEmpty(officialBoatNodeId))
            {
                // Vérifier si ce bateau a le même ID Meshtastic que le bateau officiel
                var teamInfo = boat.GetComponent<TeamInfo>();
                if (teamInfo != null && teamInfo.GetTeamName() == officialBoatNodeId)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Récupère le Transform du bateau officiel depuis RaceController
    /// </summary>
    private Transform GetOfficialBoatTransform()
    {
        if (raceController != null)
        {
            // Utiliser la réflexion pour accéder au champ privé officialBoat
            var field = typeof(RaceController).GetField("officialBoat",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                return field.GetValue(raceController) as Transform;
            }
        }
        return null;
    }

    /// <summary>
    /// Récupère l'ID du bateau officiel depuis MeshtasticGPSTracker
    /// </summary>
    private string GetOfficialBoatNodeId(MeshtasticGPSTracker tracker)
    {
        if (tracker != null)
        {
            // Utiliser la réflexion pour accéder au champ privé officialBoatNodeId
            var field = typeof(MeshtasticGPSTracker).GetField("officialBoatNodeId",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                return field.GetValue(tracker) as string;
            }
        }
        return null;
    }

    public void ToggleBoatSelection(GameObject boat, bool isSelected)
    {
        if (isSelected)
        {
            // Eviter les doublons si jamais appelé de manière inattendue
            if (!_selectedBoats.Contains(boat))
                _selectedBoats.Add(boat);
        }
        else
        {
            _selectedBoats.Remove(boat);
        }

        // Mettre à jour l'interactivité du bouton à chaque changement
        confirmButton.interactable = _selectedBoats.Count > 0;
    }

    private void ConfirmSelection()
    {
        // Créez une nouvelle liste pour éviter les modifications pendant l'itération si nécessaire
        raceController.AddBoatsToRace(new List<GameObject>(_selectedBoats));
        gameObject.SetActive(false);
    }

    public void UpdateBoatTeamName(GameObject boat, string newName)
    {
        // Pas besoin de chercher le RaceController à chaque fois si déjà référencé
        raceController?.UpdateTeamName(boat, newName);
    }
}