using UnityEngine;
using Mapbox.Utils;
using Mapbox.Unity.Map;
using System.Collections.Generic;

/// <summary>
/// Système simple de téléportation GPS : 1 réception → 1 téléportation → fini
/// </summary>
public class SimpleGPSTeleporter : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private AbstractMap map;
    [SerializeField] private float heightOffset = 1f;
    [SerializeField] private bool enableDebugLogs = true;
    
    // Dictionnaire pour tracker les bateaux déjà téléportés
    private Dictionary<string, bool> teleportedBoats = new Dictionary<string, bool>();
    
    private void Start()
    {
        // Trouver la carte automatiquement si pas assignée
        if (map == null)
        {
            map = FindFirstObjectByType<AbstractMap>();
        }
        
        if (map == null)
        {
            Debug.LogError("[SimpleGPSTeleporter] Aucune carte Mapbox trouvée!");
            return;
        }
        
        LogDebug("[SimpleGPSTeleporter] Système de téléportation GPS simple initialisé");
    }
    
    /// <summary>
    /// Téléporte un bateau à sa première position GPS reçue
    /// </summary>
    /// <param name="boatGameObject">Le GameObject du bateau</param>
    /// <param name="gpsPosition">La position GPS</param>
    /// <param name="boatId">L'identifiant unique du bateau (MAC address ou NodeId)</param>
    public void TeleportBoatOnFirstGPS(GameObject boatGameObject, Vector2d gpsPosition, string boatId)
    {
        // Vérifier si ce bateau a déjà été téléporté
        if (teleportedBoats.ContainsKey(boatId) && teleportedBoats[boatId])
        {
            LogDebug($"[SimpleGPSTeleporter] Bateau {boatId} déjà téléporté, ignoré");
            return;
        }
        
        // Vérifier la validité des coordonnées GPS
        if (!IsValidGPSCoordinate(gpsPosition))
        {
            Debug.LogError($"[SimpleGPSTeleporter] Coordonnées GPS invalides pour {boatId}: {gpsPosition}");
            return;
        }
        
        // Vérifier que la carte est prête
        if (map == null)
        {
            Debug.LogError($"[SimpleGPSTeleporter] Carte non disponible pour téléporter {boatId}");
            return;
        }
        
        // Convertir GPS vers position Unity
        Vector3 unityPosition = map.GeoToWorldPosition(gpsPosition);
        unityPosition.y = heightOffset;
        
        // Téléportation directe
        boatGameObject.transform.position = unityPosition;
        
        // Désactiver temporairement le Rigidbody pour éviter les conflits
        var rigidbody = boatGameObject.GetComponent<Rigidbody>();
        if (rigidbody != null)
        {
            rigidbody.velocity = Vector3.zero;
            rigidbody.angularVelocity = Vector3.zero;
        }
        
        // Marquer ce bateau comme téléporté
        teleportedBoats[boatId] = true;
        
        LogDebug($"[SimpleGPSTeleporter] ✓ Bateau {boatId} téléporté à GPS: {gpsPosition} → Unity: {unityPosition}");
        
        // Mettre à jour le système GPS du bateau
        var unifiedGPS = boatGameObject.GetComponent<UnifiedBoatGPS>();
        if (unifiedGPS != null)
        {
            // Forcer la mise à jour de la position GPS interne
            unifiedGPS.ForceSetCurrentPosition(gpsPosition);
            LogDebug($"[SimpleGPSTeleporter] Position GPS interne mise à jour pour {boatId}");
        }
    }
    
    /// <summary>
    /// Vérifie si des coordonnées GPS sont valides
    /// </summary>
    private bool IsValidGPSCoordinate(Vector2d gpsPosition)
    {
        return gpsPosition.x >= -90 && gpsPosition.x <= 90 && 
               gpsPosition.y >= -180 && gpsPosition.y <= 180 &&
               gpsPosition.x != 0 && gpsPosition.y != 0;
    }
    
    /// <summary>
    /// Vérifie si un bateau a déjà été téléporté
    /// </summary>
    public bool IsBoatTeleported(string boatId)
    {
        return teleportedBoats.ContainsKey(boatId) && teleportedBoats[boatId];
    }
    
    /// <summary>
    /// Remet à zéro l'état de téléportation d'un bateau (pour debug)
    /// </summary>
    public void ResetBoatTeleportation(string boatId)
    {
        if (teleportedBoats.ContainsKey(boatId))
        {
            teleportedBoats[boatId] = false;
            LogDebug($"[SimpleGPSTeleporter] État de téléportation remis à zéro pour {boatId}");
        }
    }
    
    /// <summary>
    /// Remet à zéro tous les états de téléportation (pour debug)
    /// </summary>
    [ContextMenu("Reset All Teleportations")]
    public void ResetAllTeleportations()
    {
        teleportedBoats.Clear();
        LogDebug("[SimpleGPSTeleporter] Tous les états de téléportation remis à zéro");
    }
    
    /// <summary>
    /// Affiche le statut de tous les bateaux
    /// </summary>
    [ContextMenu("Show Teleportation Status")]
    public void ShowTeleportationStatus()
    {
        LogDebug("[SimpleGPSTeleporter] === STATUT DES TÉLÉPORTATIONS ===");
        LogDebug($"[SimpleGPSTeleporter] Nombre de bateaux trackés: {teleportedBoats.Count}");
        
        foreach (var kvp in teleportedBoats)
        {
            string status = kvp.Value ? "✓ Téléporté" : "✗ Non téléporté";
            LogDebug($"[SimpleGPSTeleporter] {kvp.Key}: {status}");
        }
        
        LogDebug("[SimpleGPSTeleporter] =====================================");
    }
    
    private void LogDebug(string message)
    {
        if (enableDebugLogs)
        {
            Debug.Log(message);
        }
    }
    
    private void Update()
    {
        // Raccourci clavier pour debug
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetAllTeleportations();
        }
        
        if (Input.GetKeyDown(KeyCode.S))
        {
            ShowTeleportationStatus();
        }
    }
}
