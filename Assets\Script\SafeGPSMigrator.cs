using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Version sûre du migrateur GPS qui évite les erreurs de référence
/// </summary>
public class SafeGPSMigrator : MonoBehaviour
{
    [Header("Migration Sûre")]
    [SerializeField] private bool autoMigrateOnStart = true;
    [SerializeField] private bool showMigrationLogs = true;
    [SerializeField] private bool disableOldComponentsOnly = true; // Ne pas supprimer, juste désactiver
    
    private void Start()
    {
        if (autoMigrateOnStart)
        {
            Invoke(nameof(SafeMigration), 1f);
        }
    }
    
    /// <summary>
    /// Migration sûre vers le nouveau système
    /// </summary>
    [ContextMenu("Migration Sûre")]
    public void SafeMigration()
    {
        LogMigration("🔄 === MIGRATION SÛRE VERS SYSTÈME GPS PROPRE ===");
        
        // 1. Créer le gestionnaire central
        CreateCleanGPSManager();
        
        // 2. <PERSON>réer l'adaptateur Meshtastic
        SetupMeshtasticAdapter();
        
        // 3. Migrer tous les bateaux (sans supprimer les anciens composants)
        MigrateAllBoatsSafely();
        
        // 4. Désactiver les anciens composants (optionnel)
        if (disableOldComponentsOnly)
        {
            DisableOldComponents();
        }
        
        LogMigration("✅ === MIGRATION SÛRE TERMINÉE ===");
        LogMigration("🎯 Système GPS propre installé et opérationnel!");
    }
    
    /// <summary>
    /// Crée le gestionnaire GPS central
    /// </summary>
    private void CreateCleanGPSManager()
    {
        var existingManager = FindObjectOfType<CleanGPSManager>();
        if (existingManager != null)
        {
            LogMigration("✅ CleanGPSManager existe déjà");
            return;
        }
        
        GameObject managerObject = new GameObject("CleanGPSManager");
        managerObject.AddComponent<CleanGPSManager>();
        
        LogMigration("✅ CleanGPSManager créé");
    }
    
    /// <summary>
    /// Configure l'adaptateur Meshtastic
    /// </summary>
    private void SetupMeshtasticAdapter()
    {
        var meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticTracker == null)
        {
            LogMigration("⚠️ MeshtasticGPSTracker non trouvé");
            return;
        }
        
        var existingAdapter = meshtasticTracker.GetComponent<CleanMeshtasticAdapter>();
        if (existingAdapter != null)
        {
            LogMigration("✅ CleanMeshtasticAdapter existe déjà");
            return;
        }
        
        meshtasticTracker.gameObject.AddComponent<CleanMeshtasticAdapter>();
        LogMigration("✅ CleanMeshtasticAdapter ajouté");
    }
    
    /// <summary>
    /// Migre tous les bateaux de manière sûre
    /// </summary>
    private void MigrateAllBoatsSafely()
    {
        LogMigration("🚤 === MIGRATION SÛRE DES BATEAUX ===");
        
        // Trouver tous les GameObjects qui pourraient être des bateaux
        var allObjects = FindObjectsOfType<GameObject>();
        var boatsToMigrate = new List<GameObject>();
        
        // Identifier les bateaux
        foreach (var obj in allObjects)
        {
            if (IsBoat(obj))
            {
                boatsToMigrate.Add(obj);
            }
        }
        
        LogMigration($"🔍 Trouvé {boatsToMigrate.Count} bateaux à migrer");
        
        int migratedCount = 0;
        foreach (var boat in boatsToMigrate)
        {
            if (MigrateBoatSafely(boat))
            {
                migratedCount++;
            }
        }
        
        LogMigration($"✅ Migration des bateaux terminée: {migratedCount}/{boatsToMigrate.Count}");
    }
    
    /// <summary>
    /// Vérifie si un GameObject est un bateau
    /// </summary>
    private bool IsBoat(GameObject obj)
    {
        if (obj == null) return false;
        
        // Critères pour identifier un bateau
        string name = obj.name.ToLower();
        
        return name.Contains("boat") || 
               name.Contains("cata") || 
               name.Contains("!da") ||
               obj.GetComponent<TeamInfo>() != null ||
               obj.GetComponent<UnifiedBoatGPS>() != null ||
               obj.GetComponent<CleanGPSSystem>() != null;
    }
    
    /// <summary>
    /// Migre un bateau de manière sûre
    /// </summary>
    private bool MigrateBoatSafely(GameObject boat)
    {
        if (boat == null) return false;
        
        string boatName = boat.name;
        
        try
        {
            // Vérifier s'il a déjà CleanGPSSystem
            var existingCleanGPS = boat.GetComponent<CleanGPSSystem>();
            if (existingCleanGPS != null)
            {
                LogMigration($"✅ {boatName}: CleanGPSSystem déjà présent");
                return false;
            }
            
            // Vérifier s'il a UnifiedBoatGPS
            var oldGPS = boat.GetComponent<UnifiedBoatGPS>();
            Mapbox.Utils.Vector2d currentGPSPos = default;
            
            if (oldGPS != null)
            {
                // Sauvegarder la position GPS actuelle
                try
                {
                    currentGPSPos = oldGPS.GetCurrentPosition();
                }
                catch
                {
                    // Ignorer les erreurs de récupération de position
                }
                
                // Désactiver l'ancien composant (ne pas le supprimer)
                oldGPS.enabled = false;
                LogMigration($"🔄 {boatName}: UnifiedBoatGPS désactivé");
            }
            
            // Ajouter le nouveau composant
            var newGPS = boat.AddComponent<CleanGPSSystem>();
            
            // Configurer le Rigidbody
            var rb = boat.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = boat.AddComponent<Rigidbody>();
            }
            rb.isKinematic = true;
            rb.detectCollisions = false;
            
            // Transférer la position GPS si valide
            if (currentGPSPos.x != 0 && currentGPSPos.y != 0)
            {
                StartCoroutine(TransferGPSPositionSafely(newGPS, currentGPSPos, boatName));
            }
            
            LogMigration($"✅ {boatName}: Migré vers CleanGPSSystem");
            return true;
        }
        catch (System.Exception e)
        {
            LogMigration($"❌ Erreur migration {boatName}: {e.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Transfère la position GPS de manière sûre
    /// </summary>
    private System.Collections.IEnumerator TransferGPSPositionSafely(CleanGPSSystem newGPS, Mapbox.Utils.Vector2d gpsPosition, string boatName)
    {
        yield return new WaitForSeconds(0.2f);
        
        try
        {
            if (newGPS != null)
            {
                newGPS.UpdateGPSPosition(gpsPosition);
                LogMigration($"📍 {boatName}: Position GPS transférée: {gpsPosition}");
            }
        }
        catch (System.Exception e)
        {
            LogMigration($"⚠️ {boatName}: Erreur transfert GPS: {e.Message}");
        }
    }
    
    /// <summary>
    /// Désactive les anciens composants (sans les supprimer)
    /// </summary>
    private void DisableOldComponents()
    {
        LogMigration("🔇 === DÉSACTIVATION DES ANCIENS COMPOSANTS ===");
        
        int disabledCount = 0;
        
        // Désactiver les composants obsolètes
        var obsoleteTypes = new System.Type[]
        {
            typeof(BoatGPSManager),
            typeof(GPSRigidbodyFixer),
            typeof(TeleportationConflictResolver),
            typeof(ImprovedTeleportationManager),
            typeof(DirectGPSTeleporter),
            typeof(BoatInitializer),
            typeof(InitializationTeleporter),
            typeof(TeleportationDiagnostic)
        };
        
        foreach (var componentType in obsoleteTypes)
        {
            try
            {
                var components = FindObjectsOfType(componentType);
                foreach (var component in components)
                {
                    if (component != null && component is MonoBehaviour mono && mono.enabled)
                    {
                        mono.enabled = false;
                        disabledCount++;
                        LogMigration($"🔇 {componentType.Name} désactivé");
                    }
                }
            }
            catch (System.Exception e)
            {
                LogMigration($"⚠️ Erreur désactivation {componentType.Name}: {e.Message}");
            }
        }
        
        LogMigration($"✅ Désactivation terminée: {disabledCount} composants désactivés");
    }
    
    /// <summary>
    /// Affiche l'état du système
    /// </summary>
    [ContextMenu("Afficher État du Système")]
    public void ShowSystemStatus()
    {
        LogMigration("📊 === ÉTAT DU SYSTÈME GPS ===");
        
        var manager = FindObjectOfType<CleanGPSManager>();
        var adapter = FindObjectOfType<CleanMeshtasticAdapter>();
        var cleanBoats = FindObjectsOfType<CleanGPSSystem>();
        var oldBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogMigration($"CleanGPSManager: {(manager != null ? "✅" : "❌")}");
        LogMigration($"CleanMeshtasticAdapter: {(adapter != null ? "✅" : "❌")}");
        LogMigration($"Bateaux avec CleanGPSSystem: {cleanBoats.Length}");
        LogMigration($"Bateaux avec UnifiedBoatGPS: {oldBoats.Length}");
        
        if (manager != null)
        {
            LogMigration($"Bateaux gérés: {manager.GetManagedBoatsCount()}");
        }
        
        // Compter les bateaux actifs/inactifs
        int activeOldBoats = 0;
        int inactiveOldBoats = 0;
        
        foreach (var oldBoat in oldBoats)
        {
            if (oldBoat != null)
            {
                if (oldBoat.enabled)
                    activeOldBoats++;
                else
                    inactiveOldBoats++;
            }
        }
        
        LogMigration($"UnifiedBoatGPS actifs: {activeOldBoats}");
        LogMigration($"UnifiedBoatGPS désactivés: {inactiveOldBoats}");
        
        LogMigration("📊 === FIN DE L'ÉTAT ===");
    }
    
    private void LogMigration(string message)
    {
        if (showMigrationLogs)
        {
            Debug.Log($"[SafeGPSMigrator] {message}");
        }
    }
}
