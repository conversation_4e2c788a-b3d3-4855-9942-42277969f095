using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Système GPS simple et efficace pour les bateaux
/// Principe : GPS → Waypoints → Mouvement fluide
/// </summary>
public class SimpleBoatGPS : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private AbstractMap map;
    [SerializeField] private float heightOffset = 1f;

    [Header("Mouvement")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float rotationSpeed = 90f;
    [SerializeField] private float waypointReachDistance = 2f;

    [Header("Chemin GPS")]
    [SerializeField] private int maxWaypoints = 10;
    [SerializeField] private float minDistanceBetweenWaypoints = 1f;

    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    [SerializeField] private bool showPath = true;
    [SerializeField] private Color pathColor = Color.red;

    // Variables internes
    private List<Vector3> gpsWaypoints = new List<Vector3>();
    private int currentWaypointIndex = 0;
    private bool isMoving = false;
    private Vector2d currentGPSPosition;
    private LineRenderer pathRenderer;

    // État du bateau
    private bool isInitialized = false;
    private float currentSpeed = 0f;

    private void Start()
    {
        InitializeSystem();
    }

    private void InitializeSystem()
    {
        // Trouver la carte si pas assignée
        if (map == null)
        {
            map = FindFirstObjectByType<AbstractMap>();
        }

        if (map == null)
        {
            LogDebug("❌ Aucune carte Mapbox trouvée!");
            return;
        }

        // Attendre que la carte soit initialisée
        if (map.CenterLatitudeLongitude == default)
        {
            map.OnInitialized += OnMapReady;
        }
        else
        {
            OnMapReady();
        }

        // Configurer le rendu du chemin
        if (showPath)
        {
            SetupPathRenderer();
        }

        LogDebug("🚀 SimpleBoatGPS initialisé");
    }

    private void OnMapReady()
    {
        isInitialized = true;
        LogDebug("✅ Carte prête, GPS opérationnel");
    }

    /// <summary>
    /// Ajoute une nouvelle position GPS au chemin
    /// </summary>
    public void AddGPSPosition(Vector2d gpsPosition)
    {
        if (!isInitialized)
        {
            LogDebug("⏳ GPS pas encore initialisé, position ignorée");
            return;
        }

        if (!IsValidGPSPosition(gpsPosition))
        {
            LogDebug($"❌ Position GPS invalide: {gpsPosition}");
            return;
        }

        // Convertir GPS vers position Unity
        Vector3 worldPosition = map.GeoToWorldPosition(gpsPosition);
        worldPosition.y = heightOffset;

        // Vérifier la distance minimale
        if (gpsWaypoints.Count > 0)
        {
            float distance = Vector3.Distance(worldPosition, gpsWaypoints[gpsWaypoints.Count - 1]);
            if (distance < minDistanceBetweenWaypoints)
            {
                LogDebug($"📍 Position trop proche, ignorée (distance: {distance:F2}m)");
                return;
            }
        }

        // Ajouter le waypoint
        gpsWaypoints.Add(worldPosition);
        currentGPSPosition = gpsPosition;

        // Limiter le nombre de waypoints
        if (gpsWaypoints.Count > maxWaypoints)
        {
            gpsWaypoints.RemoveAt(0);
            if (currentWaypointIndex > 0)
            {
                currentWaypointIndex--;
            }
        }

        // Démarrer le mouvement si c'est le premier waypoint
        if (gpsWaypoints.Count == 1)
        {
            // Téléporter au premier point
            transform.position = worldPosition;
            LogDebug($"🎯 Première position GPS: téléportation à {gpsPosition}");
        }
        else if (gpsWaypoints.Count == 2)
        {
            // Démarrer le mouvement au deuxième point
            isMoving = true;
            currentWaypointIndex = 1;
            LogDebug("🚶 Mouvement démarré");
        }

        // Mettre à jour le rendu du chemin
        UpdatePathRenderer();

        LogDebug($"📍 Nouveau waypoint ajouté: {gpsPosition} → {worldPosition} (Total: {gpsWaypoints.Count})");
    }

    private void Update()
    {
        if (!isMoving || gpsWaypoints.Count < 2) return;

        MoveTowardsCurrentWaypoint();
    }

    private void MoveTowardsCurrentWaypoint()
    {
        if (currentWaypointIndex >= gpsWaypoints.Count) return;

        Vector3 targetPosition = gpsWaypoints[currentWaypointIndex];
        Vector3 currentPosition = transform.position;

        // Calculer la direction et la distance
        Vector3 direction = (targetPosition - currentPosition).normalized;
        float distance = Vector3.Distance(currentPosition, targetPosition);

        // Vérifier si on a atteint le waypoint
        if (distance <= waypointReachDistance)
        {
            currentWaypointIndex++;

            if (currentWaypointIndex >= gpsWaypoints.Count)
            {
                // Fin du chemin, ralentir
                currentSpeed = Mathf.Lerp(currentSpeed, 0f, Time.deltaTime * 2f);
                LogDebug("🏁 Fin du chemin atteinte");
            }
            else
            {
                LogDebug($"✅ Waypoint {currentWaypointIndex - 1} atteint");
            }
            return;
        }

        // Mouvement fluide
        currentSpeed = Mathf.Lerp(currentSpeed, moveSpeed, Time.deltaTime * 2f);

        // Déplacement
        transform.position = Vector3.MoveTowards(currentPosition, targetPosition, currentSpeed * Time.deltaTime);

        // Rotation fluide vers la cible
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
    }

    private void SetupPathRenderer()
    {
        GameObject pathObject = new GameObject($"Path_{gameObject.name}");
        pathObject.transform.SetParent(transform);

        pathRenderer = pathObject.AddComponent<LineRenderer>();
        pathRenderer.material = new Material(Shader.Find("Sprites/Default"));
        pathRenderer.startColor = pathColor;
        pathRenderer.endColor = pathColor;
        pathRenderer.startWidth = 0.5f;
        pathRenderer.endWidth = 0.5f;
        pathRenderer.positionCount = 0;
        pathRenderer.useWorldSpace = true;
    }

    private void UpdatePathRenderer()
    {
        if (pathRenderer == null || !showPath) return;

        pathRenderer.positionCount = gpsWaypoints.Count;
        for (int i = 0; i < gpsWaypoints.Count; i++)
        {
            pathRenderer.SetPosition(i, gpsWaypoints[i]);
        }
    }

    private bool IsValidGPSPosition(Vector2d position)
    {
        return position.x >= -90 && position.x <= 90 &&
               position.y >= -180 && position.y <= 180 &&
               position.x != 0 && position.y != 0;
    }

    /// <summary>
    /// Remet à zéro le chemin GPS
    /// </summary>
    [ContextMenu("Reset GPS Path")]
    public void ResetGPSPath()
    {
        gpsWaypoints.Clear();
        currentWaypointIndex = 0;
        isMoving = false;
        currentSpeed = 0f;

        if (pathRenderer != null)
        {
            pathRenderer.positionCount = 0;
        }

        LogDebug("🔄 Chemin GPS remis à zéro");
    }

    /// <summary>
    /// Affiche les informations de debug
    /// </summary>
    [ContextMenu("Show GPS Info")]
    public void ShowGPSInfo()
    {
        LogDebug("=== INFORMATIONS GPS ===");
        LogDebug($"Initialisé: {isInitialized}");
        LogDebug($"En mouvement: {isMoving}");
        LogDebug($"Waypoints: {gpsWaypoints.Count}");
        LogDebug($"Waypoint actuel: {currentWaypointIndex}");
        LogDebug($"Vitesse actuelle: {currentSpeed:F2} m/s");
        LogDebug($"Position GPS: {currentGPSPosition}");
        LogDebug("========================");
    }

    /// <summary>
    /// Retourne la position GPS actuelle
    /// </summary>
    public Vector2d GetCurrentGPSPosition()
    {
        return currentGPSPosition;
    }

    /// <summary>
    /// Retourne la vitesse actuelle en nœuds
    /// </summary>
    public float GetCurrentSpeedKnots()
    {
        return currentSpeed * 1.944f; // Conversion m/s vers nœuds
    }

    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[SimpleBoatGPS-{gameObject.name}] {message}");
        }
    }

    private void OnDestroy()
    {
        if (map != null)
        {
            map.OnInitialized -= OnMapReady;
        }
    }
}
