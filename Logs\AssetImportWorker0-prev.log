Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'fr' Physical Memory: 13234 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/PL/Projets Unity/RegattaVisionV5
-logFile
Logs/AssetImportWorker0.log
-srvPort
54948
Successfully changed project path to: C:/PL/Projets Unity/RegattaVisionV5
C:/PL/Projets Unity/RegattaVisionV5
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20472]  Target information:

Player connection [20472]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 476123652 [EditorId] 476123652 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20472]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 476123652 [EditorId] 476123652 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20472] Host joined multi-casting on [***********:54997]...
Player connection [20472] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 455.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/PL/Projets Unity/RegattaVisionV5/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     6617 MB
    Driver:   31.0.21921.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56124
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.007334 seconds.
- Loaded All Assemblies, in  0.487 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 312 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.685 seconds
Domain Reload Profiling: 1171ms
	BeginReloadAssembly (137ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (237ms)
		LoadAssemblies (135ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (232ms)
				TypeCache.ScanAssembly (216ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (627ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (420ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (139ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.562 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.101 seconds
Domain Reload Profiling: 3662ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (2302ms)
		LoadAssemblies (2111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (294ms)
			TypeCache.Refresh (242ms)
				TypeCache.ScanAssembly (219ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1102ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (856ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (155ms)
			ProcessInitializeOnLoadAttributes (582ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 11.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6827 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (2.8 MB). Loaded Objects now: 7301.
Memory consumption went from 289.2 MB to 286.4 MB.
Total: 7.573500 ms (FindLiveObjects: 1.460100 ms CreateObjectMapping: 0.696400 ms MarkObjects: 4.355800 ms  DeleteObjects: 1.058700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.588 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.270 seconds
Domain Reload Profiling: 2855ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (359ms)
		LoadAssemblies (377ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (65ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2271ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (797ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (520ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 15.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6616 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7305.
Memory consumption went from 249.0 MB to 246.2 MB.
Total: 6.254700 ms (FindLiveObjects: 0.803500 ms CreateObjectMapping: 0.670000 ms MarkObjects: 3.869300 ms  DeleteObjects: 0.910200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: f50b11b8df0f8924c82ca0038be14f6d -> 9c149f224c77e926b74d80ec9b069bef
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Import Request.
  Time since last request: 254778.538430 seconds.
  path: Assets/Script/SimpleGPSTestManager.cs
  artifactKey: Guid(23e516c6a66a36e40b8d496f4ccbf58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SimpleGPSTestManager.cs using Guid(23e516c6a66a36e40b8d496f4ccbf58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '5e040bdfba099e25b5a3f1c816b0b3d3') in 0.002260 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.071 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  3.686 seconds
Domain Reload Profiling: 4755ms
	BeginReloadAssembly (370ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (163ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (610ms)
		LoadAssemblies (633ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (93ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (50ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (3687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (923ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (600ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 13.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7310.
Memory consumption went from 249.0 MB to 246.2 MB.
Total: 5.920200 ms (FindLiveObjects: 0.511100 ms CreateObjectMapping: 0.464000 ms MarkObjects: 4.234900 ms  DeleteObjects: 0.709400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 9de864c7dc2126dab5ce2227abbd9523 -> 9c149f224c77e926b74d80ec9b069bef
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Import Request.
  Time since last request: 6781.537224 seconds.
  path: Assets/Script/GPSSystemMigrator.cs
  artifactKey: Guid(461fd610894adbb4da68db5de933f898) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/GPSSystemMigrator.cs using Guid(461fd610894adbb4da68db5de933f898) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '5ce9ed6edba65a54f2d8bbfa218f9161') in 0.002297 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2973.231384 seconds.
  path: Assets/Script/GPSSystemMigrator.cs
  artifactKey: Guid(461fd610894adbb4da68db5de933f898) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/GPSSystemMigrator.cs using Guid(461fd610894adbb4da68db5de933f898) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'b5a7c3ad86df5cd49356071870e6d275') in 0.000817 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.678 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.519 seconds
Domain Reload Profiling: 3194ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (424ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (92ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (43ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (2520ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (764ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (155ms)
			ProcessInitializeOnLoadAttributes (508ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 13.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7317.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.448500 ms (FindLiveObjects: 0.484500 ms CreateObjectMapping: 0.266600 ms MarkObjects: 3.644300 ms  DeleteObjects: 1.052100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b3d66e8c416a68a9918245a0ef4674a3 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Import Request.
  Time since last request: 550.897571 seconds.
  path: Assets/Script/SimpleBoatGPS.cs
  artifactKey: Guid(e8985155db0c28543b1a07d10e0f0115) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SimpleBoatGPS.cs using Guid(e8985155db0c28543b1a07d10e0f0115) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '09a039045bb983aba7c5d07f5f0afd9b') in 0.002092 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.821572 seconds.
  path: Assets/Script/SimpleGPSAdapter.cs
  artifactKey: Guid(be0729b64e1710b4d985b806902cbbad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SimpleGPSAdapter.cs using Guid(be0729b64e1710b4d985b806902cbbad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '91e29408f12f96ed229181fe2a7e2c1e') in 0.000487 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.605173 seconds.
  path: Assets/Script/SimpleGPSTeleporter.cs
  artifactKey: Guid(d1d9f9ff2c18e944b909b1aba96c45ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SimpleGPSTeleporter.cs using Guid(d1d9f9ff2c18e944b909b1aba96c45ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '7e91362f04b8f73098353d5779f63eb9') in 0.000620 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.939481 seconds.
  path: Assets/Script/SimpleWindController.cs
  artifactKey: Guid(017679a08aa463a468b9e3ce1e07e159) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SimpleWindController.cs using Guid(017679a08aa463a468b9e3ce1e07e159) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '4c3368682ba3d589954a092026cc0ae3') in 0.000672 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 27.566760 seconds.
  path: Assets/Script/BoatGPSManager.cs
  artifactKey: Guid(202dedf036aa2a44eb87986e14306ecd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/BoatGPSManager.cs using Guid(202dedf036aa2a44eb87986e14306ecd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'aed92520888efa83fbc8f3c9acf8087d') in 0.000874 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.624 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.592 seconds
Domain Reload Profiling: 2214ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (384ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (75ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (39ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1593ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (733ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (494ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 12.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6614 Unused Serialized files (Serialized files now loaded: 0)
Unloading 46 unused Assets / (2.8 MB). Loaded Objects now: 7315.
Memory consumption went from 249.0 MB to 246.2 MB.
Total: 4.277700 ms (FindLiveObjects: 0.445200 ms CreateObjectMapping: 0.424900 ms MarkObjects: 2.934600 ms  DeleteObjects: 0.471800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.592 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.427 seconds
Domain Reload Profiling: 2017ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (373ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (71ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (34ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1428ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (664ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (448ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 18.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6614 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7319.
Memory consumption went from 249.0 MB to 246.2 MB.
Total: 6.241800 ms (FindLiveObjects: 0.671300 ms CreateObjectMapping: 0.689900 ms MarkObjects: 3.859000 ms  DeleteObjects: 1.020300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.627 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.658 seconds
Domain Reload Profiling: 2283ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (406ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (77ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (40ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1659ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (799ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (525ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 25.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7324.
Memory consumption went from 249.0 MB to 246.2 MB.
Total: 147.585400 ms (FindLiveObjects: 0.619000 ms CreateObjectMapping: 1.086800 ms MarkObjects: 144.744800 ms  DeleteObjects: 1.133400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b23c866ab73e4760d7b4c53bdf9de1ec -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.602 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.544 seconds
Domain Reload Profiling: 2144ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (382ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (74ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (10ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1544ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (721ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 19.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6619 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7332.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 4.835200 ms (FindLiveObjects: 0.531100 ms CreateObjectMapping: 0.473200 ms MarkObjects: 3.236300 ms  DeleteObjects: 0.593500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 4fe53ec9e0e548264f6c6c3972d6bded -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.607 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.488 seconds
Domain Reload Profiling: 2093ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (372ms)
		LoadAssemblies (380ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (73ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (34ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1488ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (644ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (405ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 10.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7337.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 4.860700 ms (FindLiveObjects: 0.467200 ms CreateObjectMapping: 0.427100 ms MarkObjects: 3.475100 ms  DeleteObjects: 0.490400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ab9a6222d1ce84f1a2d8587a3566429c -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.581 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.587 seconds
Domain Reload Profiling: 2165ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (366ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (75ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (41ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1587ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (769ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (170ms)
			ProcessInitializeOnLoadAttributes (491ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 14.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7341.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.110600 ms (FindLiveObjects: 0.552000 ms CreateObjectMapping: 0.570100 ms MarkObjects: 3.127500 ms  DeleteObjects: 0.859800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ab9a6222d1ce84f1a2d8587a3566429c -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.692 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.714 seconds
Domain Reload Profiling: 2402ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (415ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (90ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (16ms)
			ScanForSourceGeneratedMonoScriptInfo (44ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1715ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (795ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (532ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 17.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6621 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7346.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 19.697300 ms (FindLiveObjects: 1.063000 ms CreateObjectMapping: 0.657200 ms MarkObjects: 17.067700 ms  DeleteObjects: 0.907700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 897de58ee0840a89a46c1020e98fd2ee -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.592 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.607 seconds
Domain Reload Profiling: 2197ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (70ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (36ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (780ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (525ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 14.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6621 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7350.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.241200 ms (FindLiveObjects: 0.480000 ms CreateObjectMapping: 0.289600 ms MarkObjects: 3.636400 ms  DeleteObjects: 0.834500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 897de58ee0840a89a46c1020e98fd2ee -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.569 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.474 seconds
Domain Reload Profiling: 2042ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (352ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (65ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (34ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1475ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (410ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 48.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6622 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7355.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 4.700600 ms (FindLiveObjects: 0.563900 ms CreateObjectMapping: 0.460500 ms MarkObjects: 3.194600 ms  DeleteObjects: 0.481000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SafeGPSMigrator.cs: 60622bcc57500a6c673f0eca393b107b -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: e076f2830a3c92e44c53eb27bf125bb1 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Import Request.
  Time since last request: 2924.456250 seconds.
  path: Assets/Script/SafeGPSMigrator.cs
  artifactKey: Guid(066936d7be9dd76459c6913b65d194f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SafeGPSMigrator.cs using Guid(066936d7be9dd76459c6913b65d194f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'aa5467d6a3fde2024c4f88a33cd6fd57') in 0.001644 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.660 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.682 seconds
Domain Reload Profiling: 2339ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (422ms)
		LoadAssemblies (439ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (63ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (32ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1683ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (789ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (563ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 16.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6619 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (2.8 MB). Loaded Objects now: 7356.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.929000 ms (FindLiveObjects: 0.648500 ms CreateObjectMapping: 0.778700 ms MarkObjects: 3.933900 ms  DeleteObjects: 0.567100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:scripting/monoscript/fileName/SimpleMeshtasticConnector.cs: beb89772f37fac8e882f8d18c1617f82 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSManager.cs: ffdaadd34f6ba5d8a8d1a9df198e37dc -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:scripting/monoscript/fileName/SimpleGPSInstaller.cs: 8743db190339431f0719601b77e0c00b -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SafeGPSMigrator.cs: 60622bcc57500a6c673f0eca393b107b -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0e497c0666d67855d918b19a3e459501 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleGPSBoat.cs: a8f861f6cae4d6aa97d91c062fd9c70e -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.672 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in 31.564 seconds
Domain Reload Profiling: 35236ms
	BeginReloadAssembly (362ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (105ms)
	RebuildCommonClasses (169ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (3090ms)
		LoadAssemblies (3072ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (121ms)
				TypeCache.ScanAssembly (103ms)
			ScanForSourceGeneratedMonoScriptInfo (52ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (31565ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (916ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (603ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 16.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7361.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 7.699000 ms (FindLiveObjects: 0.498300 ms CreateObjectMapping: 0.451400 ms MarkObjects: 5.913400 ms  DeleteObjects: 0.834800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:scripting/monoscript/fileName/SimpleMeshtasticConnector.cs: beb89772f37fac8e882f8d18c1617f82 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSManager.cs: ffdaadd34f6ba5d8a8d1a9df198e37dc -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:scripting/monoscript/fileName/SimpleGPSInstaller.cs: 8743db190339431f0719601b77e0c00b -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SafeGPSMigrator.cs: 60622bcc57500a6c673f0eca393b107b -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0e497c0666d67855d918b19a3e459501 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleGPSBoat.cs: a8f861f6cae4d6aa97d91c062fd9c70e -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
