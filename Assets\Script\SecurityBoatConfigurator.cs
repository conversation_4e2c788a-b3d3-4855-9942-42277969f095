using UnityEngine;

/// <summary>
/// Configurateur automatique pour le bateau de sécurité
/// Différencie clairement bateau de sécurité et bateaux de flotte
/// </summary>
public class SecurityBoatConfigurator : MonoBehaviour
{
    [Header("Configuration Bateau de Sécurité")]
    [SerializeField] private string securityBoatNodeId = "!da5394d0";
    [SerializeField] private bool autoConfigureOnStart = true;
    [SerializeField] private bool showDebugLogs = true;
    
    [Header("Recherche Automatique")]
    [SerializeField] private bool searchByNodeId = true;
    [SerializeField] private bool searchByName = true;
    [SerializeField] private string[] possibleNames = { "security", "secu", "officiel", "official", "!da5394d0" };
    
    private void Start()
    {
        if (autoConfigureOnStart)
        {
            Invoke(nameof(ConfigureSecurityBoat), 1f);
        }
    }
    
    /// <summary>
    /// Configure automatiquement le bateau de sécurité
    /// </summary>
    [ContextMenu("Configurer Bateau de Sécurité")]
    public void ConfigureSecurityBoat()
    {
        LogDebug("🚨 === CONFIGURATION BATEAU DE SÉCURITÉ ===");
        
        // 1. Trouver le bateau de sécurité
        GameObject securityBoat = FindSecurityBoat();
        
        if (securityBoat == null)
        {
            LogDebug("❌ Bateau de sécurité non trouvé");
            LogDebug("💡 Créez un GameObject pour le bateau de sécurité ou vérifiez son nom");
            return;
        }
        
        LogDebug($"✅ Bateau de sécurité trouvé: {securityBoat.name}");
        
        // 2. Configurer le système GPS
        ConfigureGPSSystem(securityBoat);
        
        // 3. Configurer le MeshtasticGPSTracker
        ConfigureMeshtasticTracker(securityBoat);
        
        // 4. Configurer l'adaptateur
        ConfigureAdapter();
        
        LogDebug("🚨 === CONFIGURATION TERMINÉE ===");
        LogDebug("✅ Bateau de sécurité configuré et opérationnel!");
    }
    
    /// <summary>
    /// Trouve le bateau de sécurité dans la scène
    /// </summary>
    private GameObject FindSecurityBoat()
    {
        LogDebug("🔍 Recherche du bateau de sécurité...");
        
        var allObjects = FindObjectsOfType<GameObject>();
        
        foreach (var obj in allObjects)
        {
            // Recherche par nom
            if (searchByName && IsSecurityBoatByName(obj))
            {
                LogDebug($"🔍 Trouvé par nom: {obj.name}");
                return obj;
            }
            
            // Recherche par ID de nœud (si configuré dans TeamInfo ou autre)
            if (searchByNodeId && IsSecurityBoatByNodeId(obj))
            {
                LogDebug($"🔍 Trouvé par NodeId: {obj.name}");
                return obj;
            }
        }
        
        LogDebug("❌ Bateau de sécurité non trouvé automatiquement");
        return null;
    }
    
    /// <summary>
    /// Vérifie si un objet est le bateau de sécurité par son nom
    /// </summary>
    private bool IsSecurityBoatByName(GameObject obj)
    {
        if (obj == null) return false;
        
        string name = obj.name.ToLower();
        
        foreach (string possibleName in possibleNames)
        {
            if (name.Contains(possibleName.ToLower()))
            {
                return true;
            }
        }
        
        return false;
    }
    
    /// <summary>
    /// Vérifie si un objet est le bateau de sécurité par son NodeId
    /// </summary>
    private bool IsSecurityBoatByNodeId(GameObject obj)
    {
        if (obj == null) return false;
        
        // Vérifier dans TeamInfo
        var teamInfo = obj.GetComponent<TeamInfo>();
        if (teamInfo != null)
        {
            string teamName = teamInfo.GetTeamName();
            if (!string.IsNullOrEmpty(teamName) && teamName.Contains(securityBoatNodeId))
            {
                return true;
            }
        }
        
        // Vérifier dans le nom de l'objet
        return obj.name.Contains(securityBoatNodeId);
    }
    
    /// <summary>
    /// Configure le système GPS sur le bateau de sécurité
    /// </summary>
    private void ConfigureGPSSystem(GameObject securityBoat)
    {
        LogDebug("🔧 Configuration du système GPS...");
        
        // Vérifier s'il a déjà un système GPS
        var cleanGPS = securityBoat.GetComponent<CleanGPSSystem>();
        var unifiedGPS = securityBoat.GetComponent<UnifiedBoatGPS>();
        
        if (cleanGPS != null)
        {
            LogDebug("✅ CleanGPSSystem déjà présent");
        }
        else if (unifiedGPS != null)
        {
            LogDebug("⚠️ UnifiedBoatGPS présent - Migration vers CleanGPSSystem recommandée");
        }
        else
        {
            // Ajouter CleanGPSSystem
            cleanGPS = securityBoat.AddComponent<CleanGPSSystem>();
            LogDebug("✅ CleanGPSSystem ajouté");
        }
        
        // Configurer le Rigidbody
        var rb = securityBoat.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = securityBoat.AddComponent<Rigidbody>();
            LogDebug("✅ Rigidbody ajouté");
        }
        
        rb.isKinematic = true;
        rb.detectCollisions = false;
        LogDebug("✅ Rigidbody configuré (kinématique)");
    }
    
    /// <summary>
    /// Configure le MeshtasticGPSTracker pour reconnaître le bateau de sécurité
    /// </summary>
    private void ConfigureMeshtasticTracker(GameObject securityBoat)
    {
        LogDebug("🔧 Configuration du MeshtasticGPSTracker...");
        
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (tracker == null)
        {
            LogDebug("❌ MeshtasticGPSTracker non trouvé");
            return;
        }
        
        // Utiliser la réflexion pour configurer les champs privés
        var trackerType = typeof(MeshtasticGPSTracker);
        
        // Configurer officialBoatNodeId
        var nodeIdField = trackerType.GetField("officialBoatNodeId", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (nodeIdField != null)
        {
            nodeIdField.SetValue(tracker, securityBoatNodeId);
            LogDebug($"✅ Official Boat Node ID configuré: {securityBoatNodeId}");
        }
        
        // Configurer officialBoatTransform
        var transformField = trackerType.GetField("officialBoatTransform", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (transformField != null)
        {
            transformField.SetValue(tracker, securityBoat.transform);
            LogDebug($"✅ Official Boat Transform configuré: {securityBoat.name}");
        }
    }
    
    /// <summary>
    /// Configure l'adaptateur Meshtastic
    /// </summary>
    private void ConfigureAdapter()
    {
        LogDebug("🔧 Configuration de l'adaptateur...");
        
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (tracker == null) return;
        
        var adapter = tracker.GetComponent<CleanMeshtasticAdapter>();
        if (adapter == null)
        {
            adapter = tracker.gameObject.AddComponent<CleanMeshtasticAdapter>();
            LogDebug("✅ CleanMeshtasticAdapter ajouté");
        }
        else
        {
            LogDebug("✅ CleanMeshtasticAdapter déjà présent");
        }
    }
    
    /// <summary>
    /// Affiche l'état de la configuration
    /// </summary>
    [ContextMenu("Afficher État Configuration")]
    public void ShowConfigurationStatus()
    {
        LogDebug("📊 === ÉTAT DE LA CONFIGURATION ===");
        
        // Bateau de sécurité
        GameObject securityBoat = FindSecurityBoat();
        LogDebug($"Bateau de sécurité: {(securityBoat != null ? securityBoat.name : "NON TROUVÉ")}");
        
        if (securityBoat != null)
        {
            var cleanGPS = securityBoat.GetComponent<CleanGPSSystem>();
            var unifiedGPS = securityBoat.GetComponent<UnifiedBoatGPS>();
            var rb = securityBoat.GetComponent<Rigidbody>();
            
            LogDebug($"  - CleanGPSSystem: {(cleanGPS != null ? "✅" : "❌")}");
            LogDebug($"  - UnifiedBoatGPS: {(unifiedGPS != null ? "✅" : "❌")}");
            LogDebug($"  - Rigidbody: {(rb != null ? (rb.isKinematic ? "✅ (kinématique)" : "⚠️ (non kinématique)") : "❌")}");
        }
        
        // MeshtasticGPSTracker
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        LogDebug($"MeshtasticGPSTracker: {(tracker != null ? "✅" : "❌")}");
        
        if (tracker != null)
        {
            var adapter = tracker.GetComponent<CleanMeshtasticAdapter>();
            LogDebug($"  - CleanMeshtasticAdapter: {(adapter != null ? "✅" : "❌")}");
        }
        
        // Gestionnaire GPS
        var manager = FindObjectOfType<CleanGPSManager>();
        LogDebug($"CleanGPSManager: {(manager != null ? "✅" : "❌")}");
        
        LogDebug("📊 === FIN DE L'ÉTAT ===");
    }
    
    /// <summary>
    /// Crée manuellement un bateau de sécurité
    /// </summary>
    [ContextMenu("Créer Bateau de Sécurité")]
    public void CreateSecurityBoat()
    {
        LogDebug("🚨 Création d'un bateau de sécurité...");
        
        GameObject securityBoat = new GameObject($"SecurityBoat_{securityBoatNodeId}");
        
        // Ajouter les composants nécessaires
        securityBoat.AddComponent<CleanGPSSystem>();
        
        var rb = securityBoat.AddComponent<Rigidbody>();
        rb.isKinematic = true;
        rb.detectCollisions = false;
        
        // Ajouter TeamInfo pour identification
        var teamInfo = securityBoat.AddComponent<TeamInfo>();
        // Configurer le TeamInfo si possible
        
        LogDebug($"✅ Bateau de sécurité créé: {securityBoat.name}");
        
        // Configurer automatiquement
        ConfigureSecurityBoat();
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[SecurityBoatConfigurator] {message}");
        }
    }
}
