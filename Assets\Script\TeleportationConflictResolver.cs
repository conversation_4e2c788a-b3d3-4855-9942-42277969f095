using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Résout les conflits entre systèmes de positionnement pendant la téléportation
/// </summary>
public class TeleportationConflictResolver : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private KeyCode teleportKey = KeyCode.T;
    [SerializeField] private float teleportationLockDuration = 2f;
    
    private MeshtasticGPSTracker meshtasticTracker;
    private Dictionary<UnifiedBoatGPS, bool> temporaryIgnoreStates = new Dictionary<UnifiedBoatGPS, bool>();
    private bool isTeleportationInProgress = false;
    
    private void Start()
    {
        meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(teleportKey))
        {
            StartTeleportationSequence();
        }
    }
    
    /// <summary>
    /// Démarre la séquence de téléportation en résolvant les conflits
    /// </summary>
    private void StartTeleportationSequence()
    {
        if (isTeleportationInProgress)
        {
            Debug.LogWarning("[TeleportationConflictResolver] Téléportation déjà en cours...");
            return;
        }
        
        Debug.Log("[TeleportationConflictResolver] === DÉBUT TÉLÉPORTATION SÉCURISÉE ===");
        
        isTeleportationInProgress = true;
        
        // Étape 1: Préparer tous les bateaux pour la téléportation
        PrepareBoatsForTeleportation();
        
        // Étape 2: Désactiver temporairement les mises à jour automatiques conflictuelles
        DisableConflictingSystems();
        
        // Étape 3: Effectuer la téléportation
        PerformTeleportation();
        
        // Étape 4: Programmer la réactivation des systèmes
        Invoke(nameof(RestoreSystemsAfterTeleportation), teleportationLockDuration);
    }
    
    /// <summary>
    /// Prépare tous les bateaux pour la téléportation
    /// </summary>
    private void PrepareBoatsForTeleportation()
    {
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var boat in allBoats)
        {
            // Sauvegarder l'état actuel d'ignorance GPS
            temporaryIgnoreStates[boat] = boat._ignoreGPSUpdatesTemporarily;
            
            // Arrêter tout invoke en cours
            boat.CancelInvoke("ResetGPSUpdateIgnoreFlag");
            
            Debug.Log($"[TeleportationConflictResolver] Bateau préparé: {boat.gameObject.name}");
        }
    }
    
    /// <summary>
    /// Désactive temporairement les systèmes conflictuels
    /// </summary>
    private void DisableConflictingSystems()
    {
        // Désactiver temporairement le MeshtasticGPSTracker pour éviter les conflits
        if (meshtasticTracker != null)
        {
            meshtasticTracker.enabled = false;
            Debug.Log("[TeleportationConflictResolver] MeshtasticGPSTracker temporairement désactivé");
        }
    }
    
    /// <summary>
    /// Effectue la téléportation de tous les bateaux
    /// </summary>
    private void PerformTeleportation()
    {
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        Debug.Log($"[TeleportationConflictResolver] Téléportation de {allBoats.Length} bateaux...");
        
        foreach (var boat in allBoats)
        {
            // Forcer l'arrêt des mises à jour GPS pendant la téléportation
            boat._ignoreGPSUpdatesTemporarily = false;
            
            // Effectuer la téléportation
            boat.ManualTeleportToLatestGPS(teleportationLockDuration);
            
            Debug.Log($"[TeleportationConflictResolver] Téléportation effectuée pour: {boat.gameObject.name}");
        }
    }
    
    /// <summary>
    /// Restaure les systèmes après la téléportation
    /// </summary>
    private void RestoreSystemsAfterTeleportation()
    {
        Debug.Log("[TeleportationConflictResolver] === RESTAURATION DES SYSTÈMES ===");
        
        // Réactiver le MeshtasticGPSTracker
        if (meshtasticTracker != null)
        {
            meshtasticTracker.enabled = true;
            Debug.Log("[TeleportationConflictResolver] MeshtasticGPSTracker réactivé");
        }
        
        // Restaurer les états d'ignorance GPS sauvegardés
        foreach (var kvp in temporaryIgnoreStates)
        {
            var boat = kvp.Key;
            var originalState = kvp.Value;
            
            if (boat != null)
            {
                // Laisser les bateaux continuer à ignorer temporairement les mises à jour GPS
                // comme prévu par leur système de téléportation
                Debug.Log($"[TeleportationConflictResolver] État restauré pour: {boat.gameObject.name}");
            }
        }
        
        // Nettoyer
        temporaryIgnoreStates.Clear();
        isTeleportationInProgress = false;
        
        Debug.Log("[TeleportationConflictResolver] === TÉLÉPORTATION TERMINÉE ===");
    }
    
    /// <summary>
    /// Arrêt d'urgence en cas de problème
    /// </summary>
    [ContextMenu("Force Restore Systems")]
    public void ForceRestoreSystems()
    {
        if (meshtasticTracker != null)
        {
            meshtasticTracker.enabled = true;
        }
        
        temporaryIgnoreStates.Clear();
        isTeleportationInProgress = false;
        
        Debug.Log("[TeleportationConflictResolver] RESTAURATION FORCÉE des systèmes");
    }
    
    private void OnDisable()
    {
        // S'assurer que les systèmes sont restaurés si le script est désactivé
        ForceRestoreSystems();
    }
}