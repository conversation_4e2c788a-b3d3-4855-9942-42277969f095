using UnityEngine;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Gestionnaire simple qui connecte MeshtasticGPSTracker aux bateaux
/// </summary>
public class SimpleGPSManager : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool showDebugLogs = true;
    
    [<PERSON><PERSON>("Bateau de Sécurité")]
    [SerializeField] private string securityBoatNodeId = "!da5394d0";
    [SerializeField] private GameObject securityBoatObject;
    
    // Dictionnaire des bateaux
    private Dictionary<string, SimpleGPSBoat> boatMap = new Dictionary<string, SimpleGPSBoat>();
    
    // Singleton
    public static SimpleGPSManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }
    
    private void Start()
    {
        if (autoSetupOnStart)
        {
            Invoke(nameof(SetupSystem), 0.5f);
        }
    }
    
    /// <summary>
    /// Configure le système automatiquement
    /// </summary>
    [ContextMenu("Setup System")]
    public void SetupSystem()
    {
        LogDebug("🚀 === SETUP SYSTÈME GPS SIMPLE ===");
        
        // 1. Trouver tous les bateaux et leur ajouter SimpleGPSBoat
        SetupAllBoats();
        
        // 2. Configurer le bateau de sécurité
        SetupSecurityBoat();
        
        // 3. Connecter MeshtasticGPSTracker
        ConnectMeshtasticTracker();
        
        LogDebug("✅ === SETUP TERMINÉ ===");
    }
    
    /// <summary>
    /// Configure tous les bateaux avec SimpleGPSBoat
    /// </summary>
    private void SetupAllBoats()
    {
        LogDebug("🚤 Configuration des bateaux...");
        
        // Trouver tous les bateaux potentiels
        var allObjects = FindObjectsOfType<GameObject>();
        int setupCount = 0;
        
        foreach (var obj in allObjects)
        {
            if (IsBoat(obj))
            {
                if (SetupBoat(obj))
                {
                    setupCount++;
                }
            }
        }
        
        LogDebug($"✅ {setupCount} bateaux configurés");
    }
    
    /// <summary>
    /// Vérifie si un GameObject est un bateau
    /// </summary>
    private bool IsBoat(GameObject obj)
    {
        if (obj == null) return false;
        
        string name = obj.name.ToLower();
        return name.Contains("boat") || 
               name.Contains("cata") || 
               name.Contains("!da") ||
               obj.GetComponent<TeamInfo>() != null;
    }
    
    /// <summary>
    /// Configure un bateau spécifique
    /// </summary>
    private bool SetupBoat(GameObject boat)
    {
        if (boat == null) return false;
        
        // Vérifier s'il a déjà SimpleGPSBoat
        var existingGPS = boat.GetComponent<SimpleGPSBoat>();
        if (existingGPS != null)
        {
            LogDebug($"✅ {boat.name}: SimpleGPSBoat déjà présent");
            return false;
        }
        
        // Ajouter SimpleGPSBoat
        var newGPS = boat.AddComponent<SimpleGPSBoat>();
        
        // Enregistrer le bateau
        string boatId = GetBoatId(boat);
        boatMap[boatId] = newGPS;
        
        LogDebug($"✅ {boat.name}: SimpleGPSBoat ajouté (ID: {boatId})");
        return true;
    }
    
    /// <summary>
    /// Détermine l'ID d'un bateau
    /// </summary>
    private string GetBoatId(GameObject boat)
    {
        // Essayer TeamInfo d'abord
        var teamInfo = boat.GetComponent<TeamInfo>();
        if (teamInfo != null)
        {
            string teamName = teamInfo.GetTeamName();
            if (!string.IsNullOrEmpty(teamName))
            {
                return teamName;
            }
        }
        
        // Fallback sur le nom
        return boat.name;
    }
    
    /// <summary>
    /// Configure le bateau de sécurité
    /// </summary>
    private void SetupSecurityBoat()
    {
        LogDebug("🚨 Configuration du bateau de sécurité...");
        
        // Trouver le bateau de sécurité s'il n'est pas assigné
        if (securityBoatObject == null)
        {
            securityBoatObject = FindSecurityBoat();
        }
        
        if (securityBoatObject == null)
        {
            LogDebug("❌ Bateau de sécurité non trouvé");
            return;
        }
        
        // Configurer le bateau de sécurité
        SetupBoat(securityBoatObject);
        
        LogDebug($"✅ Bateau de sécurité configuré: {securityBoatObject.name}");
    }
    
    /// <summary>
    /// Trouve le bateau de sécurité
    /// </summary>
    private GameObject FindSecurityBoat()
    {
        var allObjects = FindObjectsOfType<GameObject>();
        
        foreach (var obj in allObjects)
        {
            if (obj.name.Contains(securityBoatNodeId) || 
                obj.name.ToLower().Contains("security") ||
                obj.name.ToLower().Contains("secu"))
            {
                return obj;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Connecte MeshtasticGPSTracker au système
    /// </summary>
    private void ConnectMeshtasticTracker()
    {
        LogDebug("📡 Connexion MeshtasticGPSTracker...");
        
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (tracker == null)
        {
            LogDebug("❌ MeshtasticGPSTracker non trouvé");
            return;
        }
        
        // Ajouter notre connecteur s'il n'existe pas
        var connector = tracker.GetComponent<SimpleMeshtasticConnector>();
        if (connector == null)
        {
            connector = tracker.gameObject.AddComponent<SimpleMeshtasticConnector>();
        }
        
        LogDebug("✅ MeshtasticGPSTracker connecté");
    }
    
    /// <summary>
    /// Met à jour la position GPS d'un bateau
    /// APPELÉE PAR SimpleMeshtasticConnector
    /// </summary>
    public void UpdateBoatGPS(string nodeId, Vector2d gpsPosition)
    {
        // Bateau de sécurité
        if (nodeId == securityBoatNodeId && securityBoatObject != null)
        {
            var securityGPS = securityBoatObject.GetComponent<SimpleGPSBoat>();
            if (securityGPS != null)
            {
                securityGPS.UpdateGPSPosition(gpsPosition);
                LogDebug($"🚨 Bateau de sécurité mis à jour: {gpsPosition}");
                return;
            }
        }
        
        // Bateaux de la flotte
        if (boatMap.TryGetValue(nodeId, out SimpleGPSBoat boatGPS))
        {
            boatGPS.UpdateGPSPosition(gpsPosition);
            LogDebug($"🚤 Bateau flotte mis à jour: {nodeId} → {gpsPosition}");
        }
        else
        {
            LogDebug($"⚠️ Bateau inconnu: {nodeId}");
        }
    }
    
    /// <summary>
    /// Affiche l'état du système
    /// </summary>
    [ContextMenu("Show System Status")]
    public void ShowSystemStatus()
    {
        LogDebug("📊 === ÉTAT DU SYSTÈME ===");
        LogDebug($"Bateaux enregistrés: {boatMap.Count}");
        LogDebug($"Bateau de sécurité: {(securityBoatObject != null ? securityBoatObject.name : "Non trouvé")}");
        
        foreach (var kvp in boatMap)
        {
            string boatId = kvp.Key;
            SimpleGPSBoat gpsBoat = kvp.Value;
            
            if (gpsBoat != null)
            {
                bool hasGPS = gpsBoat.HasGPSPosition();
                Vector2d pos = gpsBoat.GetCurrentGPSPosition();
                LogDebug($"  - {boatId}: {(hasGPS ? $"GPS {pos}" : "Pas de GPS")}");
            }
        }
        
        LogDebug("📊 === FIN DE L'ÉTAT ===");
    }
    
    /// <summary>
    /// Remet à zéro tous les bateaux
    /// </summary>
    [ContextMenu("Reset All Boats")]
    public void ResetAllBoats()
    {
        LogDebug("🔄 Reset de tous les bateaux...");
        
        foreach (var kvp in boatMap)
        {
            if (kvp.Value != null)
            {
                kvp.Value.ResetGPS();
            }
        }
        
        LogDebug("✅ Tous les bateaux remis à zéro");
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[SimpleGPSManager] {message}");
        }
    }
}
