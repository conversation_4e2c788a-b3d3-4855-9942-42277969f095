using UnityEngine;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Adaptateur simple pour connecter MeshtasticGPSTracker au nouveau système SimpleBoatGPS
/// </summary>
public class SimpleGPSAdapter : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool enableDebugLogs = true;
    
    // Dictionnaire pour mapper les MAC addresses aux bateaux
    private Dictionary<string, SimpleBoatGPS> boatGPSMap = new Dictionary<string, SimpleBoatGPS>();
    
    private void Start()
    {
        // Découvrir automatiquement tous les bateaux avec SimpleBoatGPS
        DiscoverBoats();
        
        LogDebug("🔌 SimpleGPSAdapter initialisé");
    }
    
    /// <summary>
    /// Découvre automatiquement tous les bateaux dans la scène
    /// </summary>
    private void DiscoverBoats()
    {
        var allBoats = FindObjectsOfType<SimpleBoatGPS>();
        
        LogDebug($"🔍 Découverte de {allBoats.Length} bateaux avec SimpleBoatGPS");
        
        foreach (var boat in allBoats)
        {
            // Utiliser le nom du GameObject comme identifiant
            string boatId = boat.gameObject.name;
            
            // Essayer de trouver un TeamInfo pour obtenir un meilleur ID
            var teamInfo = boat.GetComponent<TeamInfo>();
            if (teamInfo != null)
            {
                string teamName = teamInfo.GetTeamName();
                if (!string.IsNullOrEmpty(teamName))
                {
                    boatId = teamName;
                }
            }
            
            boatGPSMap[boatId] = boat;
            LogDebug($"📋 Bateau enregistré: {boatId} → {boat.gameObject.name}");
        }
    }
    
    /// <summary>
    /// Met à jour la position GPS d'un bateau
    /// Appelée par MeshtasticGPSTracker ou autre source GPS
    /// </summary>
    public void UpdateBoatGPSPosition(string boatId, Vector2d gpsPosition)
    {
        if (string.IsNullOrEmpty(boatId))
        {
            LogDebug("❌ ID de bateau vide");
            return;
        }
        
        if (boatGPSMap.TryGetValue(boatId, out SimpleBoatGPS boatGPS))
        {
            boatGPS.AddGPSPosition(gpsPosition);
            LogDebug($"📍 Position GPS mise à jour pour {boatId}: {gpsPosition}");
        }
        else
        {
            // Essayer de créer automatiquement un bateau
            TryCreateBoatForId(boatId, gpsPosition);
        }
    }
    
    /// <summary>
    /// Met à jour la position GPS d'un bateau via GameObject
    /// </summary>
    public void UpdateBoatGPSPosition(GameObject boatObject, Vector2d gpsPosition)
    {
        if (boatObject == null) return;
        
        var simpleGPS = boatObject.GetComponent<SimpleBoatGPS>();
        if (simpleGPS != null)
        {
            simpleGPS.AddGPSPosition(gpsPosition);
            LogDebug($"📍 Position GPS mise à jour pour {boatObject.name}: {gpsPosition}");
        }
        else
        {
            LogDebug($"❌ {boatObject.name} n'a pas de composant SimpleBoatGPS");
        }
    }
    
    /// <summary>
    /// Essaie de créer automatiquement un bateau pour un ID donné
    /// </summary>
    private void TryCreateBoatForId(string boatId, Vector2d gpsPosition)
    {
        LogDebug($"🔍 Tentative de création automatique pour {boatId}");
        
        // Chercher un bateau existant sans GPS assigné
        var allBoats = FindObjectsOfType<SimpleBoatGPS>();
        
        foreach (var boat in allBoats)
        {
            // Vérifier si ce bateau n'est pas encore mappé
            bool isAlreadyMapped = false;
            foreach (var kvp in boatGPSMap)
            {
                if (kvp.Value == boat)
                {
                    isAlreadyMapped = true;
                    break;
                }
            }
            
            if (!isAlreadyMapped)
            {
                // Assigner ce bateau à l'ID
                boatGPSMap[boatId] = boat;
                boat.AddGPSPosition(gpsPosition);
                LogDebug($"✅ Bateau {boat.gameObject.name} assigné automatiquement à {boatId}");
                return;
            }
        }
        
        LogDebug($"❌ Aucun bateau disponible pour {boatId}");
    }
    
    /// <summary>
    /// Enregistre manuellement un bateau avec un ID spécifique
    /// </summary>
    public void RegisterBoat(string boatId, SimpleBoatGPS boatGPS)
    {
        if (string.IsNullOrEmpty(boatId) || boatGPS == null) return;
        
        boatGPSMap[boatId] = boatGPS;
        LogDebug($"📋 Bateau enregistré manuellement: {boatId} → {boatGPS.gameObject.name}");
    }
    
    /// <summary>
    /// Supprime un bateau de la carte
    /// </summary>
    public void UnregisterBoat(string boatId)
    {
        if (boatGPSMap.ContainsKey(boatId))
        {
            boatGPSMap.Remove(boatId);
            LogDebug($"🗑️ Bateau {boatId} supprimé de la carte");
        }
    }
    
    /// <summary>
    /// Remet à zéro tous les chemins GPS
    /// </summary>
    [ContextMenu("Reset All GPS Paths")]
    public void ResetAllGPSPaths()
    {
        foreach (var kvp in boatGPSMap)
        {
            if (kvp.Value != null)
            {
                kvp.Value.ResetGPSPath();
            }
        }
        
        LogDebug("🔄 Tous les chemins GPS remis à zéro");
    }
    
    /// <summary>
    /// Affiche les informations de tous les bateaux
    /// </summary>
    [ContextMenu("Show All Boats Info")]
    public void ShowAllBoatsInfo()
    {
        LogDebug("=== INFORMATIONS DE TOUS LES BATEAUX ===");
        LogDebug($"Nombre de bateaux enregistrés: {boatGPSMap.Count}");
        
        foreach (var kvp in boatGPSMap)
        {
            string boatId = kvp.Key;
            SimpleBoatGPS boatGPS = kvp.Value;
            
            if (boatGPS != null)
            {
                Vector2d gpsPos = boatGPS.GetCurrentGPSPosition();
                float speed = boatGPS.GetCurrentSpeedKnots();
                LogDebug($"🚤 {boatId} ({boatGPS.gameObject.name}):");
                LogDebug($"   GPS: {gpsPos}");
                LogDebug($"   Vitesse: {speed:F1} nœuds");
            }
            else
            {
                LogDebug($"❌ {boatId}: Bateau NULL");
            }
        }
        
        LogDebug("========================================");
    }
    
    /// <summary>
    /// Retourne la liste des IDs de bateaux enregistrés
    /// </summary>
    public List<string> GetRegisteredBoatIds()
    {
        return new List<string>(boatGPSMap.Keys);
    }
    
    /// <summary>
    /// Retourne le SimpleBoatGPS pour un ID donné
    /// </summary>
    public SimpleBoatGPS GetBoatGPS(string boatId)
    {
        boatGPSMap.TryGetValue(boatId, out SimpleBoatGPS boatGPS);
        return boatGPS;
    }
    
    /// <summary>
    /// Vérifie si un bateau est enregistré
    /// </summary>
    public bool IsBoatRegistered(string boatId)
    {
        return boatGPSMap.ContainsKey(boatId);
    }
    
    private void LogDebug(string message)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[SimpleGPSAdapter] {message}");
        }
    }
}
