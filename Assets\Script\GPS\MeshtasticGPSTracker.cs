using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Mapbox.Utils;

/// <summary>
/// Implémentation du tracker GPS pour les balises Meshtastic via UDP
/// </summary>
public class MeshtasticGPSTracker : AbstractGPSTracker
{
    [Header("Meshtastic Configuration")]
    [SerializeField] private MeshtasticUDPReceiver meshtasticReceiver;
    [SerializeField] private float startDelay = 2f;
    [SerializeField] private GameObject buoyPrefab; // Préfab pour les bouées
    [SerializeField] private string officialBoatNodeId; // ID du nœud Meshtastic pour le bateau officiel
    [SerializeField] private Transform officialBoatTransform; // Transform du bateau officiel

    // Système de téléportation simple
    private SimpleGPSTeleporter simpleTeleporter;

    [Header("Recording Settings")]
    [SerializeField] private string recordingsFolder = "Replays";
    [SerializeField] private float positionThreshold = 0f; // Distance minimale (mètres) pour enregistrement
    [SerializeField] private float timedRecordInterval = 1f; // Intervalle d'enregistrement temporisé

    private bool isTracking = false;
    private List<RaceSnapshot> currentRecording = new List<RaceSnapshot>();
    private string currentRecordingFile;
    private float lastRecordTime = 0f;

    private GameObject startBuoyObject; // Référence à la bouée de départ
    private GameObject endBuoyObject;   // Référence à la bouée d'arrivée

    public event Action<GameObject, GameObject> OnStartEndBuoysPlaced; // Événement pour notifier le RaceController

    protected override void Start()
    {
        base.Start();
        Debug.Log($"[MeshtasticGPSTracker] Démarrage. officialBoatNodeId: {officialBoatNodeId}");

        // Initialiser le système de téléportation simple
        simpleTeleporter = FindObjectOfType<SimpleGPSTeleporter>();
        if (simpleTeleporter == null)
        {
            // Créer le système s'il n'existe pas
            GameObject teleporterObj = new GameObject("SimpleGPSTeleporter");
            simpleTeleporter = teleporterObj.AddComponent<SimpleGPSTeleporter>();
            Debug.Log("[MeshtasticGPSTracker] SimpleGPSTeleporter créé automatiquement");
        }

        // Vérifier si le bateau officiel est correctement configuré dans la liste 'boats'
        Invoke(nameof(StartTracking), startDelay);
    }

    protected override void StartDataFetching()
    {
        Debug.Log("Démarrage du tracking GPS Meshtastic...");

        // Peupler le dictionnaire de bateaux avec les bateaux pré-configurés
        // S'assurer que le dictionnaire est vide avant de le peupler
        if (boatDictionary == null)
        {
            boatDictionary = new Dictionary<string, RegattaBoatData>();
        }
        boatDictionary.Clear();

        Debug.Log($"[MeshtasticGPSTracker] === INITIALISATION DES BATEAUX ===");
        Debug.Log($"[MeshtasticGPSTracker] ID du bateau officiel configuré: '{officialBoatNodeId}'");
        Debug.Log($"[MeshtasticGPSTracker] Nombre de bateaux pré-configurés: {(boats != null ? boats.Count : 0)}");

        if (boats != null)
        {
            for (int i = 0; i < boats.Count; i++)
            {
                var boat = boats[i];
                if (boat != null && !string.IsNullOrEmpty(boat.associatedMacAddress))
                {
                    Debug.Log($"[MeshtasticGPSTracker] Traitement du bateau {i + 1}: '{boat.boatId}' (MAC/ID: {boat.associatedMacAddress})");

                    // Exclure le bateau officiel de la liste des bateaux de course
                    if (boat.associatedMacAddress == officialBoatNodeId)
                    {
                        Debug.Log($"[MeshtasticGPSTracker] ✓ Bateau officiel '{boat.boatId}' (MAC/ID: {boat.associatedMacAddress}) détecté. Il ne sera pas ajouté au dictionnaire des bateaux de course.");
                        continue; // Passer au bateau suivant
                    }

                    if (!boatDictionary.ContainsKey(boat.associatedMacAddress))
                    {
                        boatDictionary.Add(boat.associatedMacAddress, boat);
                        Debug.Log($"[MeshtasticGPSTracker] ✓ Ajout du bateau de course {boat.boatId} avec MAC/ID {boat.associatedMacAddress} au dictionnaire.");
                    }
                    else
                    {
                        Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ Doublon détecté pour le MAC/ID {boat.associatedMacAddress} lors du peuplement initial. Le bateau {boat.boatId} ne sera pas ajouté au dictionnaire.");
                    }
                }
                else if (boat != null)
                {
                    Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ Le bateau pré-configuré {boat.boatId} n'a pas de MAC/ID associé et ne sera pas ajouté au dictionnaire.");
                }
                else
                {
                    Debug.LogError($"[MeshtasticGPSTracker] ✗ Bateau {i + 1} est NULL dans la liste pré-configurée.");
                }
            }
        }
        else
        {
            Debug.LogWarning("[MeshtasticGPSTracker] ⚠ La liste 'boats' est nulle. Aucun bateau pré-configuré à ajouter au dictionnaire.");
        }

        Debug.Log($"[MeshtasticGPSTracker] Nombre final de bateaux de course dans le dictionnaire: {boatDictionary.Count}");
        Debug.Log($"[MeshtasticGPSTracker] =======================================");

        // Vérifier si le récepteur Meshtastic est assigné
        if (meshtasticReceiver == null)
        {
            meshtasticReceiver = FindObjectOfType<MeshtasticUDPReceiver>();
            if (meshtasticReceiver == null)
            {
                Debug.LogError("Aucun récepteur MeshtasticUDPReceiver trouvé dans la scène!");
                return;
            }
        }

        // S'abonner aux événements de réception de données
        meshtasticReceiver.OnGpsDataReceived += OnMeshtasticDataReceived;
        meshtasticReceiver.OnCannedMessageReceived += OnMeshtasticCannedMessageReceived;

        isTracking = true;
        Debug.Log("Tracking GPS Meshtastic démarré avec succès.");
    }

    private void StartTracking()
    {
        StartDataFetching();
    }

    protected override void OnDestroy()
    {
        // Se désabonner des événements pour éviter les fuites de mémoire
        if (meshtasticReceiver != null)
        {
            meshtasticReceiver.OnGpsDataReceived -= OnMeshtasticDataReceived;
            meshtasticReceiver.OnCannedMessageReceived -= OnMeshtasticCannedMessageReceived;
        }

        isTracking = false;
        StopAllCoroutines();

        // Sauvegarder l'enregistrement en cours si nécessaire
        if (currentRecording.Count > 0)
        {
            SaveGPXFile();
        }

        // Appeler la méthode OnDestroy de la classe de base
        base.OnDestroy();
    }

    /// <summary>
    /// Méthode appelée lorsque de nouvelles données GPS sont reçues du MeshtasticUDPReceiver
    /// </summary>
    private void OnMeshtasticDataReceived(MeshtasticUDPReceiver.GpsData gpsData)
    {
        Debug.Log($"[MeshtasticGPSTracker] OnMeshtasticDataReceived: Données GPS reçues pour NodeId: {gpsData.nodeId}, Lat: {gpsData.latitude}, Lon: {gpsData.longitude}");
        if (!isTracking || map == null)
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] OnMeshtasticDataReceived: Non en tracking ou carte non initialisée. isTracking={isTracking}, map={(map != null ? "initialisée" : "null")}.");
            return;
        }

        ProcessGPSData(gpsData);
    }

    /// <summary>
    /// Méthode appelée lorsque de nouveaux messages canned sont reçus du MeshtasticUDPReceiver
    /// </summary>
    private void OnMeshtasticCannedMessageReceived(MeshtasticUDPReceiver.CannedMessage cannedMessage)
    {
        if (!isTracking || map == null) return;

        Debug.Log($"[MeshtasticGPSTracker] Message canned reçu de {cannedMessage.nodeId}: {cannedMessage.message}");

        // Traiter le message canned
        ProcessCannedMessage(cannedMessage);
    }


    /// <summary>
    /// Méthode appelée par MeshtasticUDPReceiver lorsque de nouvelles données GPS sont disponibles
    /// </summary>
    public void ProcessGPSData(MeshtasticUDPReceiver.GpsData gpsData)
    {
        if (!isTracking || map == null) return;

        // NOUVEAU SYSTÈME : Utiliser le BoatGPSManager pour TOUS les bateaux
        if (!ValidateCoordinates(gpsData.latitude, gpsData.longitude))
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] Coordonnées invalides pour '{gpsData.nodeId}': lat={gpsData.latitude}, lon={gpsData.longitude}");
            return;
        }

        Vector2d gpsPosition = new Vector2d(gpsData.latitude, gpsData.longitude);

        // Traitement spécifique pour le bateau officiel
        if (!string.IsNullOrEmpty(officialBoatNodeId) && gpsData.nodeId == officialBoatNodeId)
        {
            if (officialBoatTransform != null && simpleTeleporter != null)
            {
                // NOUVEAU SYSTÈME SIMPLE pour le bateau officiel aussi
                if (!simpleTeleporter.IsBoatTeleported(gpsData.nodeId))
                {
                    Debug.Log($"[MeshtasticGPSTracker] 🚀 PREMIÈRE RÉCEPTION GPS pour BATEAU OFFICIEL {gpsData.nodeId} - TÉLÉPORTATION UNIQUE");
                    simpleTeleporter.TeleportBoatOnFirstGPS(officialBoatTransform.gameObject, gpsPosition, gpsData.nodeId);
                }
                else
                {
                    // Bateau officiel déjà téléporté, mise à jour normale
                    var officialBoatGPS = officialBoatTransform.GetComponent<UnifiedBoatGPS>();
                    if (officialBoatGPS != null)
                    {
                        officialBoatGPS.UpdateRealPosition(gpsPosition, 0f, Time.time);
                        Debug.Log($"[MeshtasticGPSTracker] Bateau officiel (NodeId: {gpsData.nodeId}) mis à jour via UnifiedBoatGPS à GPS: {gpsData.latitude}, {gpsData.longitude}");
                    }
                    else
                    {
                        // Fallback - positionnement direct (déconseillé)
                        Vector3 worldPosition = map.GeoToWorldPosition(gpsPosition);
                        officialBoatTransform.position = worldPosition;
                        Debug.LogWarning($"[MeshtasticGPSTracker] Bateau officiel sans UnifiedBoatGPS - position mise à jour directement à {worldPosition}");
                    }
                }
            }
            else
            {
                Debug.LogWarning($"[MeshtasticGPSTracker] officialBoatTransform n'est pas assigné pour le bateau officiel (NodeId: {gpsData.nodeId})");
            }
            return;
        }

        // Traitement pour les bateaux de course via le système standard
        TrackerData trackerData = new TrackerData
        {
            mac = gpsData.nodeId,
            lat = gpsData.latitude,
            lng = gpsData.longitude,
            alertType = 0,
            lastUpdate = (int)(gpsData.timestamp / 1000)
        };

        bool boatExists = boatDictionary.ContainsKey(trackerData.mac);

        if (boatExists)
        {
            try
            {
                // NOUVEAU SYSTÈME SIMPLE : Téléportation unique à la première réception GPS
                var boat = boatDictionary[trackerData.mac];
                if (boat != null && boat.boatObject != null && simpleTeleporter != null)
                {
                    // Vérifier si ce bateau n'a pas encore été téléporté
                    if (!simpleTeleporter.IsBoatTeleported(trackerData.mac))
                    {
                        Debug.Log($"[MeshtasticGPSTracker] 🚀 PREMIÈRE RÉCEPTION GPS pour {trackerData.mac} - TÉLÉPORTATION UNIQUE");
                        simpleTeleporter.TeleportBoatOnFirstGPS(boat.boatObject, new Vector2d(trackerData.lat, trackerData.lng), trackerData.mac);
                    }
                    else
                    {
                        // Bateau déjà téléporté, mise à jour normale via UnifiedBoatGPS
                        UpdateBoatPosition(trackerData);
                    }
                }
                else
                {
                    // Fallback vers l'ancien système
                    UpdateBoatPosition(trackerData);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[MeshtasticGPSTracker] ProcessGPSData: Erreur lors de la mise à jour de la position pour {trackerData.mac}: {e.Message}\n{e.StackTrace}");
            }
        }
        else
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ Bateau inconnu '{trackerData.mac}' avec coordonnées valides. Vérification si c'est le bateau officiel...");

            // Vérifier si c'est le bateau officiel avant de créer automatiquement
            if (!string.IsNullOrEmpty(officialBoatNodeId) && trackerData.mac == officialBoatNodeId)
            {
                Debug.Log($"[MeshtasticGPSTracker] ✓ Le nœud '{trackerData.mac}' correspond au bateau officiel. Aucune création automatique nécessaire.");
                return; // Ne pas créer de bateau automatiquement pour le bateau officiel
            }

            Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ Tentative de création automatique d'un bateau pour le nœud '{trackerData.mac}'.");
            TryCreateBoatForMeshtasticNode(trackerData.mac, new Vector2d(trackerData.lat, trackerData.lng));
        }
    }

    /// <summary>
    /// Vérifie si un bateau correspond à un nœud Meshtastic donné
    /// </summary>
    private bool DoesBoatMatchNode(GameObject boat, string nodeId)
    {
        // Méthode 1: Vérifier si le bateau a été configuré avec cette MAC address
        if (boatDictionary.ContainsKey(nodeId))
        {
            var boatData = boatDictionary[nodeId];
            return boatData != null && boatData.boatObject == boat;
        }

        // Méthode 2: Vérifier par nom ou autres critères
        // Vous pouvez ajouter d'autres méthodes de correspondance ici

        return false;
    }

    /// <summary>
    /// Traite les messages canned reçus
    /// </summary>
    private void ProcessCannedMessage(MeshtasticUDPReceiver.CannedMessage cannedMessage)
    {
        // Trouver le bateau associé au nodeId
        RegattaBoatData boat = FindBoatByMacAddress(cannedMessage.nodeId);

        if (boat == null)
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] Message canned reçu pour un nodeId non configuré: {cannedMessage.nodeId}. Ignoré.");
            return;
        }

        // Traiter le message en fonction de son contenu
        switch (cannedMessage.message)
        {
            case "B1":
                Debug.Log($"[MeshtasticGPSTracker] Message B1 reçu de {cannedMessage.nodeId} pour le bateau {boat.boatId}. Placement de la bouée de départ.");
                // Placer ou mettre à jour la bouée de départ
                startBuoyObject = PlaceBuoy(boat.currentPosition, "StartBuoy", startBuoyObject);
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 6, "Signal B1"); // Optionnel: conserver l'alerte visuelle
                CheckAndNotifyBuoysPlaced();
                break;
            case "B2":
                Debug.Log($"[MeshtasticGPSTracker] Message B2 reçu de {cannedMessage.nodeId} pour le bateau {boat.boatId}. Placement de la bouée d'arrivée.");
                // Placer ou mettre à jour la bouée d'arrivée
                endBuoyObject = PlaceBuoy(boat.currentPosition, "EndBuoy", endBuoyObject);
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 7, "Signal B2"); // Optionnel: conserver l'alerte visuelle
                CheckAndNotifyBuoysPlaced();
                break;
            case "Start":
                Debug.Log($"[MeshtasticGPSTracker] Message Start reçu de {cannedMessage.nodeId} pour le bateau {boat.boatId}. Déclenchement de la séquence de départ.");
                 if (StartSequenceTimerInstance != null)
                {
                    if (!StartSequenceTimerInstance.enabled)
                    {
                        StartSequenceTimerInstance.enabled = true; // Activer le script s'il est désactivé
                        Debug.Log("[MeshtasticGPSTracker] StartSequenceTimer activé.");
                    }
                    Debug.Log("[MeshtasticGPSTracker] Message Start détecté ! Démarrage du timer.");
                    StartSequenceTimerInstance.StartSequence(StartSequenceTimerInstance.totalTime / 60f);
                }
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 8, "Départ Course"); // Optionnel: conserver l'alerte visuelle
                break;
            case "FIN":
                Debug.Log($"[MeshtasticGPSTracker] Signal FIN reçu pour le bateau {boat.boatId}");
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 9, "Arrivée Course");
                break;
            default:
                Debug.LogWarning($"[MeshtasticGPSTracker] Message canned inconnu reçu de {cannedMessage.nodeId}: {cannedMessage.message}.");
                break;
        }
    }


    protected override void OnBoatPositionUpdated(string macAddress, Vector2d position, float heading, int alertStatus)
    {
        // Enregistrer l'état du bateau pour l'export GPX
        RecordBoatState(macAddress, position, heading, alertStatus);
    }

    private void RecordBoatState(string macAddress, Vector2d position, float heading, int alertStatus)
    {
        // Filtrage des positions trop proches
        var lastPos = currentRecording.Count > 0 ?
            currentRecording[^1].boatStates.Find(b => b.macAddress == macAddress)?.position
            : null;

        if (lastPos != null && Vector2d.Distance(lastPos.Value, position) < positionThreshold)
            return;

        var newState = new BoatState
        {
            macAddress = macAddress,
            position = position,
            heading = heading,
            alertStatus = alertStatus,
            timestamp = Time.time
        };

        // Vérifier si on doit créer un nouveau snapshot ou ajouter à l'existant
        bool createNewSnapshot = false;

        // Si c'est le premier enregistrement ou si le temps écoulé depuis le dernier enregistrement est suffisant
        if (currentRecording.Count == 0 || (Time.time - lastRecordTime) >= timedRecordInterval)
        {
            createNewSnapshot = true;
            lastRecordTime = Time.time;
        }

        if (createNewSnapshot)
        {
            var snapshot = new RaceSnapshot
            {
                timestamp = Time.time,
                boatStates = new List<BoatState> { newState }
            };
            currentRecording.Add(snapshot);
        }
        else
        {
            // Ajouter au dernier snapshot
            var lastSnapshot = currentRecording[currentRecording.Count - 1];

            // Vérifier si ce bateau existe déjà dans le snapshot
            int existingIndex = lastSnapshot.boatStates.FindIndex(b => b.macAddress == macAddress);
            if (existingIndex >= 0)
            {
                // Remplacer l'état existant
                lastSnapshot.boatStates[existingIndex] = newState;
            }
            else
            {
                // Ajouter un nouvel état
                lastSnapshot.boatStates.Add(newState);
            }
        }
    }

    /// <summary>
    /// Démarre un nouvel enregistrement GPX
    /// </summary>
    public void StartRecording()
    {
        if (currentRecording.Count > 0)
        {
            Debug.LogWarning("Un enregistrement est déjà en cours. Arrêtez-le d'abord.");
            return;
        }

        currentRecording = new List<RaceSnapshot>();
        currentRecordingFile = $"race_{DateTime.Now:yyyyMMdd_HHmmss}";
        Debug.Log($"Démarrage de l'enregistrement: {currentRecordingFile}");
    }

    /// <summary>
    /// Arrête l'enregistrement en cours et sauvegarde le fichier GPX
    /// </summary>
    public void StopRecording()
    {
        if (currentRecording.Count == 0)
        {
            Debug.LogWarning("Aucun enregistrement en cours.");
            return;
        }

        SaveGPXFile();
        currentRecording.Clear();
        Debug.Log("Enregistrement arrêté et sauvegardé.");
    }

    /// <summary>
    /// Sauvegarde l'enregistrement actuel au format GPX
    /// </summary>
    private void SaveGPXFile()
    {
        if (currentRecording.Count == 0) return;

        try
        {
            // Créer le dossier s'il n'existe pas
            string folderPath = Path.Combine(Application.persistentDataPath, recordingsFolder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            string filePath = Path.Combine(folderPath, $"{currentRecordingFile}.gpx");

            // Générer le contenu GPX
            string gpxContent = GenerateGPXContent();

            // Écrire dans le fichier
            File.WriteAllText(filePath, gpxContent);

            Debug.Log($"Fichier GPX sauvegardé: {filePath}");
        }
        catch (Exception e)
        {
            Debug.LogError($"Erreur lors de la sauvegarde du fichier GPX: {e.Message}");
        }
    }

    /// <summary>
    /// Essaie de créer automatiquement un bateau pour un identifiant Meshtastic
    /// </summary>
    private void TryCreateBoatForMeshtasticNode(string nodeId, Vector2d position)
    {
        Debug.Log($"[MeshtasticGPSTracker] === CRÉATION AUTOMATIQUE DE BATEAU ===");
        Debug.Log($"[MeshtasticGPSTracker] NodeId: '{nodeId}', Position: {position}");

        try
        {
            // Vérifier si un préfab de bateau est disponible
            GameObject boatPrefab = null;

            // Essayer de trouver un préfab de bateau dans les ressources
            boatPrefab = Resources.Load<GameObject>("Prefabs/Boat");
            Debug.Log($"[MeshtasticGPSTracker] Préfab depuis Resources: {(boatPrefab != null ? "trouvé" : "non trouvé")}");

            // Si aucun préfab n'est trouvé, essayer de cloner un bateau existant
            if (boatPrefab == null && boats.Count > 0)
            {
                Debug.Log($"[MeshtasticGPSTracker] Recherche d'un bateau existant à cloner parmi {boats.Count} bateaux...");
                foreach (var boat in boats)
                {
                    if (boat != null && boat.boatObject != null)
                    {
                        boatPrefab = boat.boatObject;
                        Debug.Log($"[MeshtasticGPSTracker] ✓ Utilisation du bateau existant comme modèle: {boat.boatId}");
                        break;
                    }
                }
            }

            if (boatPrefab != null)
            {
                // Créer un nouveau GameObject pour le bateau
                GameObject newBoatObject = GameObject.Instantiate(boatPrefab);
                newBoatObject.name = $"Boat_{nodeId}";

                // Créer une nouvelle instance de RegattaBoatData
                RegattaBoatData newBoat = new RegattaBoatData($"Boat_{nodeId}", newBoatObject, position);
                newBoat.associatedMacAddress = nodeId; // Utiliser l'identifiant Meshtastic comme MAC

                // Ajouter le bateau à la liste et au dictionnaire
                boats.Add(newBoat);
                boatDictionary[nodeId] = newBoat;

                // Initialiser le bateau
                var gpsSystem = newBoatObject.GetComponent<UnifiedBoatGPS>();
                if (gpsSystem != null)
                {
                    // S'assurer que le bateau a une référence à la carte
                    var mapField = typeof(UnifiedBoatGPS).GetField("map", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Public);
                    if (mapField != null && map != null)
                    {
                        mapField.SetValue(gpsSystem, map);
                        Debug.Log($"[MeshtasticGPSTracker] Référence à la carte définie pour le bateau {nodeId}");
                    }

                    // Forcer l'initialisation des composants
                    var initMethod = typeof(UnifiedBoatGPS).GetMethod("InitializeComponents", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                    if (initMethod != null)
                    {
                        initMethod.Invoke(gpsSystem, null);
                        Debug.Log($"[MeshtasticGPSTracker] Composants initialisés pour le bateau {nodeId}");
                    }

                    // Attendre que la carte soit initialisée avant de définir la position
                    gpsSystem.QueueInitialPosition(position);
                    newBoat.isInitialized = true;
                    newBoat.currentPosition = position;

                    Debug.Log($"[MeshtasticGPSTracker] Position initiale définie pour le bateau {nodeId}");
                }

                Debug.Log($"[MeshtasticGPSTracker] ✓ Bateau créé automatiquement pour {nodeId} à la position {position}");
                Debug.Log($"[MeshtasticGPSTracker] ✓ Total bateaux après création: {boats.Count}");
                Debug.Log($"[MeshtasticGPSTracker] ================================================");
            }
            else
            {
                Debug.LogError($"[MeshtasticGPSTracker] ✗ Impossible de créer un bateau pour {nodeId}: aucun préfab ou modèle disponible");
                Debug.Log($"[MeshtasticGPSTracker] ================================================");
            }
        }
        catch (Exception e)
        {
                    Debug.LogError($"[MeshtasticGPSTracker] Erreur lors de la création du bateau pour {nodeId}: {e.Message}");
        }
    }

    /// <summary>
    /// Place une bouée sur la carte à une position donnée.
    /// </summary>
    /// <param name="position">La position GPS où placer la bouée.</param>
    /// <param name="buoyType">Le type de bouée (par exemple, "StartBuoy", "EndBuoy").</param>
    /// <param name="existingBuoy">Référence à une bouée existante à mettre à jour, ou null si nouvelle.</param>
    /// <returns>La référence de la bouée créée ou mise à jour.</returns>
    private GameObject PlaceBuoy(Vector2d position, string buoyType, GameObject existingBuoy)
    {
        if (buoyPrefab == null)
        {
            Debug.LogError("[MeshtasticGPSTracker] Impossible de placer la bouée: le préfab de bouée n'est pas assigné.");
            return existingBuoy; // Retourne l'existante si elle ne peut pas être mise à jour
        }

        Vector3 worldPosition = Vector3.zero;

        if (map != null)
        {
            worldPosition = map.GeoToWorldPosition(position);
        }
        else
        {
            Debug.LogWarning("[MeshtasticGPSTracker] Référence de carte non trouvée pour convertir la position de la bouée.");
            return existingBuoy;
        }

        GameObject buoyObject;
        if (existingBuoy != null)
        {
            // Mettre à jour la position de la bouée existante
            buoyObject = existingBuoy;
            buoyObject.transform.position = worldPosition;
            Debug.Log($"[MeshtasticGPSTracker] Bouée '{buoyType}' mise à jour à la position monde {worldPosition} (GPS: {position.x}, {position.y})");
        }
        else
        {
            // Instancier une nouvelle bouée
            buoyObject = Instantiate(buoyPrefab, worldPosition, Quaternion.identity);
            buoyObject.name = $"{buoyType}"; // Nom fixe pour faciliter la référence
            Debug.Log($"[MeshtasticGPSTracker] Nouvelle bouée '{buoyType}' placée à la position monde {worldPosition} (GPS: {position.x}, {position.y})");
        }

        return buoyObject;
    }

    /// <summary>
    /// Vérifie si les deux bouées (départ et arrivée) sont placées et notifie le RaceController.
    /// </summary>
    private void CheckAndNotifyBuoysPlaced()
    {
        if (startBuoyObject != null && endBuoyObject != null)
        {
            Debug.Log("[MeshtasticGPSTracker] Les bouées de départ et d'arrivée sont toutes deux placées. Notification du RaceController.");
            OnStartEndBuoysPlaced?.Invoke(startBuoyObject, endBuoyObject);
        }
    }


    private string GenerateGPXContent()
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();

        // En-tête GPX
        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        sb.AppendLine("<gpx version=\"1.1\" creator=\"MeshtasticGPSTracker\" xmlns=\"http://www.topografix.com/GPX/1/1\">");

        // Regrouper les données par bateau
        Dictionary<string, List<BoatState>> boatTracks = new Dictionary<string, List<BoatState>>();

        foreach (var snapshot in currentRecording)
        {
            foreach (var state in snapshot.boatStates)
            {
                if (!boatTracks.ContainsKey(state.macAddress))
                {
                    boatTracks[state.macAddress] = new List<BoatState>();
                }
                boatTracks[state.macAddress].Add(state);
            }
        }

        // Créer une piste pour chaque bateau
        foreach (var kvp in boatTracks)
        {
            string macAddress = kvp.Key;
            List<BoatState> states = kvp.Value;

            // Trier par timestamp
            states.Sort((a, b) => a.timestamp.CompareTo(b.timestamp));

            // Créer la piste
            sb.AppendLine($"  <trk>");
            sb.AppendLine($"    <name>Boat {macAddress}</name>");
            sb.AppendLine($"    <trkseg>");

            // Ajouter chaque point
            foreach (var state in states)
            {
                // Convertir le timestamp en format ISO 8601
                DateTime dateTime = DateTime.Now.AddSeconds(-Time.time + state.timestamp);
                string timeStr = dateTime.ToString("yyyy-MM-ddTHH:mm:ssZ");

                sb.AppendLine($"      <trkpt lat=\"{state.position.x}\" lon=\"{state.position.y}\">");
                sb.AppendLine($"        <time>{timeStr}</time>");
                sb.AppendLine($"        <course>{state.heading}</course>");
                sb.AppendLine($"      </trkpt>");
            }
        }

        sb.AppendLine($"    </trkseg>");
        sb.AppendLine($"  </trk>");
        sb.AppendLine("</gpx>");

        return sb.ToString();
    }
}
