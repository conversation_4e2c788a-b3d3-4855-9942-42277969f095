using UnityEngine;

/// <summary>
/// Optimise automatiquement tous les bateaux pour un mouvement GPS fluide et réactif
/// </summary>
public class GPSMovementOptimizer : MonoBehaviour
{
    [Header("Optimisation GPS")]
    [SerializeField] private bool optimizeOnStart = true;
    [SerializeField] private bool showOptimizationLogs = true;
    
    [Header("Paramètres Optimaux")]
    [SerializeField] private float optimalPathFollowDelay = 0.3f;
    [SerializeField] private float optimalMaxSpeed = 20f;
    [SerializeField] private float optimalTurningSpeed = 80f;
    [SerializeField] private float optimalInertiaFactor = 3.0f;
    [SerializeField] private float optimalGpsAccuracy = 0.3f;
    [SerializeField] private float optimalPositionSmoothingFactor = 0.05f;
    
    private void Start()
    {
        if (optimizeOnStart)
        {
            // Attendre un peu que tous les bateaux soient initialisés
            Invoke(nameof(OptimizeAllBoats), 1f);
        }
    }
    
    /// <summary>
    /// Optimise tous les bateaux pour un mouvement GPS fluide
    /// </summary>
    [ContextMenu("Optimiser Tous les Bateaux")]
    public void OptimizeAllBoats()
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allBoats.Length == 0)
        {
            LogOptimization("Aucun bateau avec UnifiedBoatGPS trouvé");
            return;
        }
        
        LogOptimization($"=== OPTIMISATION GPS DE {allBoats.Length} BATEAUX ===");
        
        foreach (var boat in allBoats)
        {
            OptimizeBoat(boat);
        }
        
        LogOptimization("=== OPTIMISATION TERMINÉE ===");
    }
    
    /// <summary>
    /// Optimise un bateau spécifique
    /// </summary>
    private void OptimizeBoat(UnifiedBoatGPS boat)
    {
        if (boat == null) return;
        
        // Utiliser la réflexion pour modifier les paramètres privés
        var boatType = typeof(UnifiedBoatGPS);
        
        // Modifier pathFollowDelay
        var pathFollowDelayField = boatType.GetField("pathFollowDelay", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (pathFollowDelayField != null)
        {
            pathFollowDelayField.SetValue(boat, optimalPathFollowDelay);
        }
        
        // Modifier maxSpeed
        var maxSpeedField = boatType.GetField("maxSpeed", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (maxSpeedField != null)
        {
            maxSpeedField.SetValue(boat, optimalMaxSpeed);
        }
        
        // Modifier turningSpeed
        var turningSpeedField = boatType.GetField("turningSpeed", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (turningSpeedField != null)
        {
            turningSpeedField.SetValue(boat, optimalTurningSpeed);
        }
        
        // Modifier inertiaFactor
        var inertiaFactorField = boatType.GetField("inertiaFactor", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (inertiaFactorField != null)
        {
            inertiaFactorField.SetValue(boat, optimalInertiaFactor);
        }
        
        // Modifier gpsAccuracy
        var gpsAccuracyField = boatType.GetField("gpsAccuracy", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (gpsAccuracyField != null)
        {
            gpsAccuracyField.SetValue(boat, optimalGpsAccuracy);
        }
        
        // Modifier positionSmoothingFactor
        var positionSmoothingFactorField = boatType.GetField("positionSmoothingFactor", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (positionSmoothingFactorField != null)
        {
            positionSmoothingFactorField.SetValue(boat, optimalPositionSmoothingFactor);
        }
        
        // Appeler la méthode d'optimisation du bateau
        boat.OptimizeForRealTimeGPS();
        
        LogOptimization($"✓ Bateau {boat.gameObject.name} optimisé");
    }
    
    /// <summary>
    /// Restaure les paramètres par défaut pour tous les bateaux
    /// </summary>
    [ContextMenu("Restaurer Paramètres Par Défaut")]
    public void RestoreDefaultSettings()
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogOptimization($"=== RESTAURATION DES PARAMÈTRES PAR DÉFAUT ===");
        
        foreach (var boat in allBoats)
        {
            RestoreBoatDefaults(boat);
        }
        
        LogOptimization("=== RESTAURATION TERMINÉE ===");
    }
    
    /// <summary>
    /// Restaure les paramètres par défaut d'un bateau
    /// </summary>
    private void RestoreBoatDefaults(UnifiedBoatGPS boat)
    {
        if (boat == null) return;
        
        // Paramètres par défaut conservateurs
        var boatType = typeof(UnifiedBoatGPS);
        
        var pathFollowDelayField = boatType.GetField("pathFollowDelay", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (pathFollowDelayField != null)
        {
            pathFollowDelayField.SetValue(boat, 3f);
        }
        
        var maxSpeedField = boatType.GetField("maxSpeed", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (maxSpeedField != null)
        {
            maxSpeedField.SetValue(boat, 10f);
        }
        
        var turningSpeedField = boatType.GetField("turningSpeed", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (turningSpeedField != null)
        {
            turningSpeedField.SetValue(boat, 30f);
        }
        
        var inertiaFactorField = boatType.GetField("inertiaFactor", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (inertiaFactorField != null)
        {
            inertiaFactorField.SetValue(boat, 0.8f);
        }
        
        var gpsAccuracyField = boatType.GetField("gpsAccuracy", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (gpsAccuracyField != null)
        {
            gpsAccuracyField.SetValue(boat, 2.0f);
        }
        
        var positionSmoothingFactorField = boatType.GetField("positionSmoothingFactor", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (positionSmoothingFactorField != null)
        {
            positionSmoothingFactorField.SetValue(boat, 0.3f);
        }
        
        LogOptimization($"✓ Paramètres par défaut restaurés pour {boat.gameObject.name}");
    }
    
    /// <summary>
    /// Affiche les paramètres actuels de tous les bateaux
    /// </summary>
    [ContextMenu("Afficher Paramètres Actuels")]
    public void ShowCurrentSettings()
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogOptimization($"=== PARAMÈTRES ACTUELS DE {allBoats.Length} BATEAUX ===");
        
        foreach (var boat in allBoats)
        {
            ShowBoatSettings(boat);
        }
        
        LogOptimization("=== FIN DES PARAMÈTRES ===");
    }
    
    /// <summary>
    /// Affiche les paramètres d'un bateau spécifique
    /// </summary>
    private void ShowBoatSettings(UnifiedBoatGPS boat)
    {
        if (boat == null) return;
        
        var boatType = typeof(UnifiedBoatGPS);
        
        var pathFollowDelay = GetFieldValue<float>(boat, "pathFollowDelay");
        var maxSpeed = GetFieldValue<float>(boat, "maxSpeed");
        var turningSpeed = GetFieldValue<float>(boat, "turningSpeed");
        var inertiaFactor = GetFieldValue<float>(boat, "inertiaFactor");
        var gpsAccuracy = GetFieldValue<float>(boat, "gpsAccuracy");
        
        LogOptimization($"Bateau {boat.gameObject.name}:");
        LogOptimization($"  - Délai GPS: {pathFollowDelay}s");
        LogOptimization($"  - Vitesse max: {maxSpeed}m/s");
        LogOptimization($"  - Vitesse rotation: {turningSpeed}°/s");
        LogOptimization($"  - Inertie: {inertiaFactor}");
        LogOptimization($"  - Précision GPS: {gpsAccuracy}m");
    }
    
    /// <summary>
    /// Récupère la valeur d'un champ privé
    /// </summary>
    private T GetFieldValue<T>(UnifiedBoatGPS boat, string fieldName)
    {
        var field = typeof(UnifiedBoatGPS).GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            return (T)field.GetValue(boat);
        }
        
        return default(T);
    }
    
    private void LogOptimization(string message)
    {
        if (showOptimizationLogs)
        {
            Debug.Log($"[GPSMovementOptimizer] {message}");
        }
    }
}
