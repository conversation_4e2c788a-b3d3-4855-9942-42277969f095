using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Mapbox.Utils;

/// <summary>
/// Gestionnaire central pour le suivi GPS permanent des bateaux
/// Indépendant du système de course - fonctionne dès le lancement de l'application
/// </summary>
public class BoatGPSManager : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool enableGPSTrackingOnStart = true;
    [SerializeField] private Transform fleetContainer;
    [SerializeField] private KeyCode initializationTeleportKey = KeyCode.T;
    [SerializeField] private float postTeleportIgnoreDuration = 2f;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    
    private Dictionary<GameObject, UnifiedBoatGPS> managedBoats = new Dictionary<GameObject, UnifiedBoatGPS>();
    private bool hasInitialized = false;
    
    /// <summary>
    /// Événement déclenché quand un bateau reçoit de nouvelles données GPS
    /// </summary>
    public System.Action<GameObject, Vector2d> OnBoatGPSUpdated;
    
    private void Start()
    {
        if (fleetContainer == null)
        {
            // Chercher automatiquement le conteneur Fleet
            GameObject fleet = GameObject.Find("Fleet");
            if (fleet != null)
            {
                fleetContainer = fleet.transform;
            }
        }
        
        if (enableGPSTrackingOnStart)
        {
            InitializeAllBoats();
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(initializationTeleportKey))
        {
            PerformInitializationTeleport();
        }
    }
    
    /// <summary>
    /// Initialise tous les bateaux pour le suivi GPS
    /// </summary>
    public void InitializeAllBoats()
    {
        LogDebug("[BoatGPSManager] === INITIALISATION DU SUIVI GPS ===");
        
        // Trouver tous les bateaux avec UnifiedBoatGPS
        UnifiedBoatGPS[] allGPSComponents = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var gpsComponent in allGPSComponents)
        {
            RegisterBoat(gpsComponent.gameObject);
        }
        
        // Également chercher dans le conteneur Fleet si défini
        if (fleetContainer != null)
        {
            foreach (Transform child in fleetContainer)
            {
                var gps = child.GetComponent<UnifiedBoatGPS>();
                if (gps != null && !managedBoats.ContainsKey(child.gameObject))
                {
                    RegisterBoat(child.gameObject);
                }
            }
        }
        
        LogDebug($"[BoatGPSManager] {managedBoats.Count} bateaux initialisés pour le suivi GPS");
        hasInitialized = true;
    }
    
    /// <summary>
    /// Enregistre un bateau pour le suivi GPS
    /// </summary>
    public void RegisterBoat(GameObject boat)
    {
        var gpsComponent = boat.GetComponent<UnifiedBoatGPS>();
        if (gpsComponent == null)
        {
            LogDebug($"[BoatGPSManager] ERREUR: {boat.name} n'a pas de composant UnifiedBoatGPS");
            return;
        }
        
        if (managedBoats.ContainsKey(boat))
        {
            LogDebug($"[BoatGPSManager] {boat.name} déjà enregistré");
            return;
        }
        
        managedBoats[boat] = gpsComponent;
        
        // Configurer le Rigidbody pour un mouvement fluide (pas de physique)
        var rigidbody = boat.GetComponent<Rigidbody>();
        if (rigidbody != null)
        {
            rigidbody.isKinematic = true;
            rigidbody.detectCollisions = false;
            LogDebug($"[BoatGPSManager] Rigidbody configuré pour {boat.name}");
        }
        
        LogDebug($"[BoatGPSManager] Bateau enregistré: {boat.name}");
    }
    
    /// <summary>
    /// Désenregistre un bateau du suivi GPS
    /// </summary>
    public void UnregisterBoat(GameObject boat)
    {
        if (managedBoats.Remove(boat))
        {
            LogDebug($"[BoatGPSManager] Bateau désenregistré: {boat.name}");
        }
    }
    
    /// <summary>
    /// Met à jour la position d'un bateau via GPS
    /// Appelée par MeshtasticGPSTracker ou autre source de données GPS
    /// </summary>
    public void UpdateBoatGPSPosition(GameObject boat, Vector2d gpsPosition, float heading = 0f)
    {
        if (!managedBoats.TryGetValue(boat, out UnifiedBoatGPS gpsComponent))
        {
            // Si le bateau n'est pas encore enregistré, l'enregistrer automatiquement
            RegisterBoat(boat);
            gpsComponent = boat.GetComponent<UnifiedBoatGPS>();
        }
        
        if (gpsComponent != null)
        {
            gpsComponent.UpdateRealPosition(gpsPosition, heading, Time.time);
            
            // Déclencher l'événement pour informer les autres systèmes
            OnBoatGPSUpdated?.Invoke(boat, gpsPosition);
            
            LogDebug($"[BoatGPSManager] Position GPS mise à jour pour {boat.name}: {gpsPosition}");
        }
    }
    
    /// <summary>
    /// Effectue la téléportation d'initialisation de tous les bateaux
    /// </summary>
    public void PerformInitializationTeleport()
    {
        LogDebug("[BoatGPSManager] === TÉLÉPORTATION D'INITIALISATION ===");
        
        foreach (var kvp in managedBoats)
        {
            var boat = kvp.Key;
            var gpsComponent = kvp.Value;
            
            if (boat != null && gpsComponent != null)
            {
                gpsComponent.ManualTeleportToLatestGPS(postTeleportIgnoreDuration);
                LogDebug($"[BoatGPSManager] Téléportation: {boat.name}");
            }
        }
        
        LogDebug($"[BoatGPSManager] {managedBoats.Count} bateaux téléportés");
    }
    
    /// <summary>
    /// Définit la position initiale d'un bateau
    /// </summary>
    public void SetBoatInitialPosition(GameObject boat, Vector2d initialGPSPosition)
    {
        if (managedBoats.TryGetValue(boat, out UnifiedBoatGPS gpsComponent))
        {
            gpsComponent.QueueInitialPosition(initialGPSPosition);
            LogDebug($"[BoatGPSManager] Position initiale définie pour {boat.name}: {initialGPSPosition}");
        }
    }
    
    /// <summary>
    /// Obtient la liste de tous les bateaux suivis
    /// </summary>
    public List<GameObject> GetTrackedBoats()
    {
        return managedBoats.Keys.Where(boat => boat != null).ToList();
    }
    
    /// <summary>
    /// Vérifie si un bateau est suivi
    /// </summary>
    public bool IsBoatTracked(GameObject boat)
    {
        return managedBoats.ContainsKey(boat);
    }
    
    /// <summary>
    /// Obtient le composant UnifiedBoatGPS d'un bateau
    /// </summary>
    public UnifiedBoatGPS GetBoatGPS(GameObject boat)
    {
        managedBoats.TryGetValue(boat, out UnifiedBoatGPS gpsComponent);
        return gpsComponent;
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log(message);
        }
    }
    
    /// <summary>
    /// Méthode appelée par le RaceController quand une course démarre
    /// Ne change rien au suivi GPS - juste pour information
    /// </summary>
    public void OnRaceStarted(List<GameObject> raceBoats)
    {
        LogDebug($"[BoatGPSManager] Course démarrée avec {raceBoats.Count} bateaux");
        // Le suivi GPS continue normalement
    }
    
    /// <summary>
    /// Méthode appelée par le RaceController quand une course s'arrête
    /// Ne change rien au suivi GPS - juste pour information
    /// </summary>
    public void OnRaceStopped()
    {
        LogDebug("[BoatGPSManager] Course arrêtée - suivi GPS continue");
        // Le suivi GPS continue normalement
    }
}