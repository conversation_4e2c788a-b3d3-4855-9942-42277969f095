using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Diagnostic spécialisé pour le bateau de sécurité
/// </summary>
public class SecurityBoatDiagnostic : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private string securityNodeId = "!da5394d0";
    [SerializeField] private bool showLogs = true;
    
    /// <summary>
    /// Diagnostic complet du bateau de sécurité
    /// </summary>
    [ContextMenu("Diagnostic Bateau de Sécurité")]
    public void DiagnoseSecurityBoat()
    {
        LogMessage("🚨 === DIAGNOSTIC BATEAU DE SÉCURITÉ ===");
        
        // 1. Vérifier SimpleGPSManager
        var manager = FindObjectOfType<SimpleGPSManager>();
        LogMessage($"SimpleGPSManager: {(manager != null ? "✅ Trouvé" : "❌ Non trouvé")}");
        
        // 2. Vérifier MeshtasticGPSTracker
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        LogMessage($"MeshtasticGPSTracker: {(tracker != null ? "✅ Trouvé" : "❌ Non trouvé")}");
        
        // 3. Vérifier SimpleMeshtasticConnector
        var connector = FindObjectOfType<SimpleMeshtasticConnector>();
        LogMessage($"SimpleMeshtasticConnector: {(connector != null ? "✅ Trouvé" : "❌ Non trouvé")}");
        
        // 4. Chercher le bateau de sécurité
        GameObject securityBoat = FindSecurityBoat();
        LogMessage($"Bateau de sécurité: {(securityBoat != null ? $"✅ Trouvé: {securityBoat.name}" : "❌ Non trouvé")}");
        
        if (securityBoat != null)
        {
            DiagnoseSecurityBoatComponents(securityBoat);
        }
        
        // 5. Vérifier la configuration du tracker
        if (tracker != null)
        {
            DiagnoseTrackerConfiguration(tracker);
        }
        
        LogMessage("🚨 === FIN DU DIAGNOSTIC ===");
    }
    
    /// <summary>
    /// Diagnostic des composants du bateau de sécurité
    /// </summary>
    private void DiagnoseSecurityBoatComponents(GameObject securityBoat)
    {
        LogMessage($"🔍 Analyse des composants de {securityBoat.name}:");
        
        // SimpleGPSBoat
        var simpleGPS = securityBoat.GetComponent<SimpleGPSBoat>();
        LogMessage($"  - SimpleGPSBoat: {(simpleGPS != null ? "✅" : "❌")}");
        
        if (simpleGPS != null)
        {
            bool hasGPS = simpleGPS.HasGPSPosition();
            Vector2d gpsPos = simpleGPS.GetCurrentGPSPosition();
            LogMessage($"    • Position GPS: {(hasGPS ? $"✅ {gpsPos}" : "❌ Aucune")}");
        }
        
        // BoatWaypointMover
        var waypointMover = securityBoat.GetComponent<BoatWaypointMover>();
        LogMessage($"  - BoatWaypointMover: {(waypointMover != null ? (waypointMover.enabled ? "✅ Actif" : "⚠️ Inactif") : "❌ Absent")}");
        
        // Rigidbody
        var rb = securityBoat.GetComponent<Rigidbody>();
        LogMessage($"  - Rigidbody: {(rb != null ? "✅" : "❌")}");
        if (rb != null)
        {
            LogMessage($"    • Kinematic: {(rb.isKinematic ? "❌ Oui (problème!)" : "✅ Non")}");
            LogMessage($"    • Gravity: {(rb.useGravity ? "⚠️ Oui" : "✅ Non")}");
            LogMessage($"    • Mass: {rb.mass}");
        }
        
        // BoxCollider
        var collider = securityBoat.GetComponent<BoxCollider>();
        LogMessage($"  - BoxCollider: {(collider != null ? "✅" : "❌")}");
        
        // Anciens composants
        var oldGPS = securityBoat.GetComponent<UnifiedBoatGPS>();
        LogMessage($"  - UnifiedBoatGPS (ancien): {(oldGPS != null ? (oldGPS.enabled ? "⚠️ Actif (conflit!)" : "✅ Inactif") : "✅ Absent")}");
    }
    
    /// <summary>
    /// Diagnostic de la configuration du tracker
    /// </summary>
    private void DiagnoseTrackerConfiguration(MeshtasticGPSTracker tracker)
    {
        LogMessage("📡 Configuration MeshtasticGPSTracker:");
        
        // Utiliser la réflexion pour accéder aux champs privés
        var trackerType = typeof(MeshtasticGPSTracker);
        
        // officialBoatNodeId
        var nodeIdField = trackerType.GetField("officialBoatNodeId", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (nodeIdField != null)
        {
            string nodeId = nodeIdField.GetValue(tracker) as string;
            LogMessage($"  - Official Boat Node ID: {(nodeId == securityNodeId ? $"✅ {nodeId}" : $"❌ {nodeId} (devrait être {securityNodeId})")}");
        }
        
        // officialBoatTransform
        var transformField = trackerType.GetField("officialBoatTransform", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (transformField != null)
        {
            Transform boatTransform = transformField.GetValue(tracker) as Transform;
            LogMessage($"  - Official Boat Transform: {(boatTransform != null ? $"✅ {boatTransform.name}" : "❌ Non assigné")}");
        }
    }
    
    /// <summary>
    /// Trouve le bateau de sécurité
    /// </summary>
    private GameObject FindSecurityBoat()
    {
        var allObjects = FindObjectsOfType<GameObject>();
        
        foreach (var obj in allObjects)
        {
            if (obj.name.Contains(securityNodeId) || 
                obj.name.ToLower().Contains("security") ||
                obj.name.ToLower().Contains("secu") ||
                obj.name.ToLower().Contains("officiel"))
            {
                return obj;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Corrige automatiquement le bateau de sécurité
    /// </summary>
    [ContextMenu("Corriger Bateau de Sécurité")]
    public void FixSecurityBoat()
    {
        LogMessage("🔧 === CORRECTION BATEAU DE SÉCURITÉ ===");
        
        GameObject securityBoat = FindSecurityBoat();
        if (securityBoat == null)
        {
            LogMessage("❌ Bateau de sécurité non trouvé - Impossible de corriger");
            return;
        }
        
        LogMessage($"🎯 Correction de {securityBoat.name}");
        
        // 1. Ajouter SimpleGPSBoat si nécessaire
        var simpleGPS = securityBoat.GetComponent<SimpleGPSBoat>();
        if (simpleGPS == null)
        {
            simpleGPS = securityBoat.AddComponent<SimpleGPSBoat>();
            LogMessage("✅ SimpleGPSBoat ajouté");
        }
        
        // 2. Configurer BoatWaypointMover
        var waypointMover = securityBoat.GetComponent<BoatWaypointMover>();
        if (waypointMover == null)
        {
            waypointMover = securityBoat.AddComponent<BoatWaypointMover>();
            LogMessage("✅ BoatWaypointMover ajouté");
        }
        waypointMover.enabled = true;
        LogMessage("✅ BoatWaypointMover activé");
        
        // 3. Configurer Rigidbody
        var rb = securityBoat.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = securityBoat.AddComponent<Rigidbody>();
            LogMessage("✅ Rigidbody ajouté");
        }
        
        rb.isKinematic = false;
        rb.useGravity = false;
        rb.constraints = RigidbodyConstraints.FreezePositionY | 
                        RigidbodyConstraints.FreezeRotationX | 
                        RigidbodyConstraints.FreezeRotationZ;
        rb.mass = 2000f;
        rb.drag = 2f;
        rb.angularDrag = 5f;
        LogMessage("✅ Rigidbody configuré");
        
        // 4. Ajouter BoxCollider
        var collider = securityBoat.GetComponent<BoxCollider>();
        if (collider == null)
        {
            collider = securityBoat.AddComponent<BoxCollider>();
            collider.size = new Vector3(5f, 2f, 10f);
            LogMessage("✅ BoxCollider ajouté");
        }
        
        // 5. Désactiver UnifiedBoatGPS
        var oldGPS = securityBoat.GetComponent<UnifiedBoatGPS>();
        if (oldGPS != null)
        {
            oldGPS.enabled = false;
            LogMessage("✅ UnifiedBoatGPS désactivé");
        }
        
        // 6. Configurer le tracker
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (tracker != null)
        {
            ConfigureTracker(tracker, securityBoat);
        }
        
        LogMessage("🔧 === CORRECTION TERMINÉE ===");
        LogMessage("🎯 Le bateau de sécurité devrait maintenant fonctionner !");
    }
    
    /// <summary>
    /// Configure le tracker pour le bateau de sécurité
    /// </summary>
    private void ConfigureTracker(MeshtasticGPSTracker tracker, GameObject securityBoat)
    {
        var trackerType = typeof(MeshtasticGPSTracker);
        
        // Configurer officialBoatNodeId
        var nodeIdField = trackerType.GetField("officialBoatNodeId", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (nodeIdField != null)
        {
            nodeIdField.SetValue(tracker, securityNodeId);
            LogMessage($"✅ Official Boat Node ID configuré: {securityNodeId}");
        }
        
        // Configurer officialBoatTransform
        var transformField = trackerType.GetField("officialBoatTransform", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (transformField != null)
        {
            transformField.SetValue(tracker, securityBoat.transform);
            LogMessage($"✅ Official Boat Transform configuré: {securityBoat.name}");
        }
    }
    
    /// <summary>
    /// Test avec position GPS simulée
    /// </summary>
    [ContextMenu("Test GPS Sécurité")]
    public void TestSecurityBoatGPS()
    {
        LogMessage("🧪 === TEST GPS BATEAU DE SÉCURITÉ ===");
        
        var manager = FindObjectOfType<SimpleGPSManager>();
        if (manager == null)
        {
            LogMessage("❌ SimpleGPSManager non trouvé");
            return;
        }
        
        // Position GPS de test
        Vector2d testPosition = new Vector2d(43.6047, 1.4442);
        
        manager.UpdateBoatGPS(securityNodeId, testPosition);
        LogMessage($"🧪 Position test envoyée: {securityNodeId} → {testPosition}");
        LogMessage("👀 Regardez le bateau de sécurité, il devrait bouger !");
        
        LogMessage("🧪 === FIN DU TEST ===");
    }
    
    private void LogMessage(string message)
    {
        if (showLogs)
        {
            Debug.Log($"[SecurityBoatDiagnostic] {message}");
        }
    }
}
