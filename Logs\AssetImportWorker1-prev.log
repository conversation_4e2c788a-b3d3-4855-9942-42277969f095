Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'fr' Physical Memory: 13234 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/PL/Projets Unity/RegattaVisionV5
-logFile
Logs/AssetImportWorker1.log
-srvPort
54948
Successfully changed project path to: C:/PL/Projets Unity/RegattaVisionV5
C:/PL/Projets Unity/RegattaVisionV5
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21552]  Target information:

Player connection [21552]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1861486420 [EditorId] 1861486420 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21552]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1861486420 [EditorId] 1861486420 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21552] Host joined multi-casting on [***********:54997]...
Player connection [21552] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 455.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/PL/Projets Unity/RegattaVisionV5/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     6617 MB
    Driver:   31.0.21921.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56872
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.006701 seconds.
- Loaded All Assemblies, in  0.489 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 310 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.683 seconds
Domain Reload Profiling: 1171ms
	BeginReloadAssembly (137ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (239ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (236ms)
			TypeCache.Refresh (234ms)
				TypeCache.ScanAssembly (218ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (683ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (626ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (419ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (138ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.561 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.145 seconds
Domain Reload Profiling: 3705ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (2302ms)
		LoadAssemblies (2108ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (295ms)
			TypeCache.Refresh (241ms)
				TypeCache.ScanAssembly (217ms)
			ScanForSourceGeneratedMonoScriptInfo (37ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1146ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (869ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (587ms)
			ProcessInitializeOnLoadMethodAttributes (80ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 14.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6827 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (2.8 MB). Loaded Objects now: 7301.
Memory consumption went from 289.4 MB to 286.5 MB.
Total: 8.240600 ms (FindLiveObjects: 0.571700 ms CreateObjectMapping: 0.507700 ms MarkObjects: 6.402000 ms  DeleteObjects: 0.757800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Import Request.
  Time since last request: 253243.563067 seconds.
  path: Assets/Script/SimpleGPSTestManager.cs
  artifactKey: Guid(23e516c6a66a36e40b8d496f4ccbf58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SimpleGPSTestManager.cs using Guid(23e516c6a66a36e40b8d496f4ccbf58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '4285e2c9cd16d4e2112ce5a7b2747b03') in 0.002845 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.590 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.266 seconds
Domain Reload Profiling: 2853ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (362ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (65ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (36ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2266ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (793ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 12.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6616 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7305.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.759900 ms (FindLiveObjects: 0.685400 ms CreateObjectMapping: 0.365700 ms MarkObjects: 4.192700 ms  DeleteObjects: 0.515000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: f50b11b8df0f8924c82ca0038be14f6d -> 9c149f224c77e926b74d80ec9b069bef
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.205 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  3.508 seconds
Domain Reload Profiling: 4711ms
	BeginReloadAssembly (396ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (170ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (724ms)
		LoadAssemblies (703ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (127ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (12ms)
			ScanForSourceGeneratedMonoScriptInfo (64ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (3509ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (923ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (198ms)
			ProcessInitializeOnLoadAttributes (596ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 10.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7310.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.657800 ms (FindLiveObjects: 0.495300 ms CreateObjectMapping: 0.515600 ms MarkObjects: 3.940600 ms  DeleteObjects: 0.705300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 9de864c7dc2126dab5ce2227abbd9523 -> 9c149f224c77e926b74d80ec9b069bef
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.716 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.439 seconds
Domain Reload Profiling: 3153ms
	BeginReloadAssembly (165ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (461ms)
		LoadAssemblies (471ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (76ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (44ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2440ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (762ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (155ms)
			ProcessInitializeOnLoadAttributes (506ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 15.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7317.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 6.353200 ms (FindLiveObjects: 0.516300 ms CreateObjectMapping: 0.486900 ms MarkObjects: 4.748700 ms  DeleteObjects: 0.600100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b3d66e8c416a68a9918245a0ef4674a3 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Import Request.
  Time since last request: 11332.646453 seconds.
  path: Assets/Script/GPSSystemMigrator.cs
  artifactKey: Guid(461fd610894adbb4da68db5de933f898) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/GPSSystemMigrator.cs using Guid(461fd610894adbb4da68db5de933f898) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '68e0db7182bfda5bbd02b9ec080f2b36') in 0.004582 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.625 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.597 seconds
Domain Reload Profiling: 2219ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (385ms)
		LoadAssemblies (394ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (76ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (42ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1598ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (738ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (493ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 11.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6614 Unused Serialized files (Serialized files now loaded: 0)
Unloading 46 unused Assets / (2.8 MB). Loaded Objects now: 7315.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 4.551500 ms (FindLiveObjects: 0.474100 ms CreateObjectMapping: 0.422000 ms MarkObjects: 3.119800 ms  DeleteObjects: 0.533900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.596 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.424 seconds
Domain Reload Profiling: 2018ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (375ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (69ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (37ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1424ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (660ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (436ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 14.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6614 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7319.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 5.319500 ms (FindLiveObjects: 0.699900 ms CreateObjectMapping: 0.489800 ms MarkObjects: 3.221000 ms  DeleteObjects: 0.908100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.642 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.647 seconds
Domain Reload Profiling: 2286ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (380ms)
		LoadAssemblies (419ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (77ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (42ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1648ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (803ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (532ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 18.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7324.
Memory consumption went from 249.1 MB to 246.3 MB.
Total: 7.048800 ms (FindLiveObjects: 1.274700 ms CreateObjectMapping: 0.770600 ms MarkObjects: 4.103700 ms  DeleteObjects: 0.897600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: b23c866ab73e4760d7b4c53bdf9de1ec -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.603 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.567 seconds
Domain Reload Profiling: 2168ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (372ms)
		LoadAssemblies (380ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (72ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (34ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1568ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (734ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (488ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 16.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6619 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7332.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 5.433200 ms (FindLiveObjects: 0.677700 ms CreateObjectMapping: 0.613600 ms MarkObjects: 3.485000 ms  DeleteObjects: 0.656000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 4fe53ec9e0e548264f6c6c3972d6bded -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.605 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.497 seconds
Domain Reload Profiling: 2100ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (382ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (71ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1498ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (642ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (404ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 12.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7337.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 4.406600 ms (FindLiveObjects: 0.476400 ms CreateObjectMapping: 0.453200 ms MarkObjects: 3.001100 ms  DeleteObjects: 0.475200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ab9a6222d1ce84f1a2d8587a3566429c -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.577 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.587 seconds
Domain Reload Profiling: 2162ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (71ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (38ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1588ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (784ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (508ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 18.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7341.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 6.138300 ms (FindLiveObjects: 0.552800 ms CreateObjectMapping: 0.574600 ms MarkObjects: 4.489800 ms  DeleteObjects: 0.520100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ab9a6222d1ce84f1a2d8587a3566429c -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.680 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.672 seconds
Domain Reload Profiling: 2350ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (451ms)
		LoadAssemblies (437ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (90ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (12ms)
			ScanForSourceGeneratedMonoScriptInfo (45ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1673ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (790ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 19.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6621 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7346.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 18.071800 ms (FindLiveObjects: 1.083800 ms CreateObjectMapping: 0.694300 ms MarkObjects: 15.389300 ms  DeleteObjects: 0.902900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 897de58ee0840a89a46c1020e98fd2ee -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.587 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.647 seconds
Domain Reload Profiling: 2232ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (359ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (64ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1648ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (799ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (535ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 14.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6621 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7350.
Memory consumption went from 249.2 MB to 246.5 MB.
Total: 4.457200 ms (FindLiveObjects: 0.538100 ms CreateObjectMapping: 0.287500 ms MarkObjects: 2.989900 ms  DeleteObjects: 0.640800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 897de58ee0840a89a46c1020e98fd2ee -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.579 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.457 seconds
Domain Reload Profiling: 2034ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (360ms)
		LoadAssemblies (372ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (63ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1457ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (628ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (405ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 17.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6622 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7355.
Memory consumption went from 249.3 MB to 246.5 MB.
Total: 10.313300 ms (FindLiveObjects: 1.385700 ms CreateObjectMapping: 0.954600 ms MarkObjects: 7.129500 ms  DeleteObjects: 0.842100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SafeGPSMigrator.cs: 60622bcc57500a6c673f0eca393b107b -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: e076f2830a3c92e44c53eb27bf125bb1 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.663 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.693 seconds
Domain Reload Profiling: 2354ms
	BeginReloadAssembly (165ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (422ms)
		LoadAssemblies (439ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (67ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (37ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1694ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (797ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (568ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 16.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6619 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (2.8 MB). Loaded Objects now: 7356.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 6.579800 ms (FindLiveObjects: 0.495500 ms CreateObjectMapping: 0.539600 ms MarkObjects: 4.409600 ms  DeleteObjects: 1.134000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:scripting/monoscript/fileName/SimpleMeshtasticConnector.cs: beb89772f37fac8e882f8d18c1617f82 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSManager.cs: ffdaadd34f6ba5d8a8d1a9df198e37dc -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:scripting/monoscript/fileName/SimpleGPSInstaller.cs: 8743db190339431f0719601b77e0c00b -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SafeGPSMigrator.cs: 60622bcc57500a6c673f0eca393b107b -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0e497c0666d67855d918b19a3e459501 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleGPSBoat.cs: a8f861f6cae4d6aa97d91c062fd9c70e -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.645 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in 31.575 seconds
Domain Reload Profiling: 35219ms
	BeginReloadAssembly (342ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (113ms)
	RebuildCommonClasses (163ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (3089ms)
		LoadAssemblies (3056ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (189ms)
			TypeCache.Refresh (121ms)
				TypeCache.ScanAssembly (104ms)
			ScanForSourceGeneratedMonoScriptInfo (51ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (31576ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (926ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (194ms)
			ProcessInitializeOnLoadAttributes (608ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 14.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6620 Unused Serialized files (Serialized files now loaded: 0)
Unloading 40 unused Assets / (2.8 MB). Loaded Objects now: 7361.
Memory consumption went from 249.2 MB to 246.4 MB.
Total: 7.088500 ms (FindLiveObjects: 0.563600 ms CreateObjectMapping: 0.562800 ms MarkObjects: 5.423600 ms  DeleteObjects: 0.537600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/SimpleGPSTeleporter.cs: 79f1536b2d90ac73b16ff02c947780cb -> 
  custom:scripting/monoscript/fileName/SimpleMeshtasticConnector.cs: beb89772f37fac8e882f8d18c1617f82 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/CleanGPSSystem.cs: 6054229cec595918ab507be7be1ea307 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/SimpleGPSManager.cs: ffdaadd34f6ba5d8a8d1a9df198e37dc -> 
  custom:scripting/monoscript/fileName/SimpleGPSTestManager.cs: 463df260e1f518c268d8d9382d6f6626 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/GPSSystemMigrator.cs: 6333c19830358f972d2e2ef9f1084d74 -> 
  custom:scripting/monoscript/fileName/SimpleGPSInstaller.cs: 8743db190339431f0719601b77e0c00b -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SafeGPSMigrator.cs: 60622bcc57500a6c673f0eca393b107b -> 
  custom:scripting/monoscript/fileName/SecurityBoatConfigurator.cs: dd6e5b0d723960c251245894c9deaa12 -> 
  custom:scripting/monoscript/fileName/QuickGPSFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSManager.cs: de92530ae31198490704a84228e315ce -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0e497c0666d67855d918b19a3e459501 -> 9c149f224c77e926b74d80ec9b069bef
  custom:scripting/monoscript/fileName/SimpleGPSBoat.cs: a8f861f6cae4d6aa97d91c062fd9c70e -> 
  custom:scripting/monoscript/fileName/SimpleBoatGPS.cs: 5277d7a4b5b9b2c1370d4f592fec69d9 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/GPSRigidbodyFixer.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/CleanGPSMigrator.cs: 4342176f120e772be93202c200ccb239 -> 
  custom:scripting/monoscript/fileName/CleanMeshtasticAdapter.cs: 39e56bfbb5ca74b6dcc826a4a711b6e4 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/SimpleGPSAdapter.cs: 6f4694ab005088b9983c1ebe8931f56e -> 
  custom:scripting/monoscript/fileName/GPSMovementOptimizer.cs: 9a1ab7d0556c3c71dc784b699ac45d77 -> 
