using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Initialise tous les bateaux pour le suivi GPS permanent dès le lancement de l'application
/// Résout le problème où les bateaux ne bougent que pendant les courses
/// </summary>
public class BoatInitializer : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private Transform fleetContainer;
    [SerializeField] private bool initializeOnStart = true;
    [SerializeField] private KeyCode initializationKey = KeyCode.I;
    [SerializeField] private KeyCode teleportKey = KeyCode.T;
    [SerializeField] private float teleportIgnoreDuration = 2f;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    
    private List<GameObject> initializedBoats = new List<GameObject>();
    
    private void Start()
    {
        if (fleetContainer == null)
        {
            // Chercher automatiquement le conteneur Fleet
            GameObject fleet = GameObject.Find("Fleet");
            if (fleet != null)
            {
                fleetContainer = fleet.transform;
            }
        }
        
        if (initializeOnStart)
        {
            // <PERSON><PERSON><PERSON> pour laisser Unity se stabiliser
            Invoke(nameof(InitializeAllBoats), 1f);
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(initializationKey))
        {
            InitializeAllBoats();
        }
        
        if (Input.GetKeyDown(teleportKey))
        {
            TeleportAllBoats();
        }
    }
    
    /// <summary>
    /// Initialise tous les bateaux pour le mouvement GPS permanent
    /// </summary>
    public void InitializeAllBoats()
    {
        LogDebug("[BoatInitializer] === INITIALISATION GPS PERMANENTE ===");
        
        initializedBoats.Clear();
        
        // Trouver tous les bateaux avec UnifiedBoatGPS
        UnifiedBoatGPS[] allGPSComponents = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var gpsComponent in allGPSComponents)
        {
            InitializeBoat(gpsComponent.gameObject);
        }
        
        // Également dans le conteneur Fleet si défini
        if (fleetContainer != null)
        {
            foreach (Transform child in fleetContainer)
            {
                var gps = child.GetComponent<UnifiedBoatGPS>();
                if (gps != null && !initializedBoats.Contains(child.gameObject))
                {
                    InitializeBoat(child.gameObject);
                }
            }
        }
        
        LogDebug($"[BoatInitializer] {initializedBoats.Count} bateaux initialisés pour le GPS permanent");
    }
    
    /// <summary>
    /// Initialise un bateau spécifique pour le mouvement GPS fluide
    /// </summary>
    private void InitializeBoat(GameObject boat)
    {
        if (boat == null) return;
        
        // Vérifier que le bateau a UnifiedBoatGPS
        var gpsComponent = boat.GetComponent<UnifiedBoatGPS>();
        if (gpsComponent == null)
        {
            LogDebug($"[BoatInitializer] IGNORÉ: {boat.name} n'a pas de UnifiedBoatGPS");
            return;
        }
        
        // Configurer le Rigidbody pour un mouvement fluide (pas de physique)
        var rigidbody = boat.GetComponent<Rigidbody>();
        if (rigidbody != null)
        {
            rigidbody.isKinematic = true;
            rigidbody.detectCollisions = false;
            LogDebug($"[BoatInitializer] Rigidbody configuré pour mouvement GPS: {boat.name}");
        }
        else
        {
            LogDebug($"[BoatInitializer] ATTENTION: {boat.name} n'a pas de Rigidbody");
        }
        
        // Désactiver BoatWaypointMover si présent (éviter les conflits)
        var waypointMover = boat.GetComponent<BoatWaypointMover>();
        if (waypointMover != null)
        {
            waypointMover.enabled = false;
            LogDebug($"[BoatInitializer] BoatWaypointMover désactivé pour: {boat.name}");
        }
        
        initializedBoats.Add(boat);
        LogDebug($"[BoatInitializer] Bateau initialisé: {boat.name}");
    }
    
    /// <summary>
    /// Effectue la téléportation d'initialisation de tous les bateaux
    /// </summary>
    public void TeleportAllBoats()
    {
        LogDebug("[BoatInitializer] === TÉLÉPORTATION D'INITIALISATION ===");
        
        UnifiedBoatGPS[] allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allBoats.Length == 0)
        {
            LogDebug("[BoatInitializer] Aucun bateau avec UnifiedBoatGPS trouvé pour la téléportation");
            return;
        }
        
        foreach (var boat in allBoats)
        {
            if (boat != null)
            {
                // Utiliser la téléportation avec durée réduite pour l'initialisation
                boat.ManualTeleportToLatestGPS(teleportIgnoreDuration);
                LogDebug($"[BoatInitializer] Téléportation: {boat.gameObject.name}");
            }
        }
        
        LogDebug($"[BoatInitializer] {allBoats.Length} bateaux téléportés");
    }
    
    /// <summary>
    /// Vérifie si un bateau est initialisé
    /// </summary>
    public bool IsBoatInitialized(GameObject boat)
    {
        return initializedBoats.Contains(boat);
    }
    
    /// <summary>
    /// Obtient la liste des bateaux initialisés
    /// </summary>
    public List<GameObject> GetInitializedBoats()
    {
        return new List<GameObject>(initializedBoats);
    }
    
    private void LogDebug(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log(message);
        }
    }
    
    /// <summary>
    /// Appelé par le RaceController quand une course démarre
    /// S'assurer que les bateaux de course conservent leur configuration GPS
    /// </summary>
    public void OnRaceStarted(List<GameObject> raceBoats)
    {
        LogDebug($"[BoatInitializer] Course démarrée - préservation de la configuration GPS pour {raceBoats.Count} bateaux");
        
        foreach (var boat in raceBoats)
        {
            if (boat != null && !IsBoatInitialized(boat))
            {
                // Initialiser les bateaux qui ne l'étaient pas encore
                InitializeBoat(boat);
            }
        }
    }
    
    /// <summary>
    /// Force la réinitialisation de tous les bateaux
    /// </summary>
    [ContextMenu("Force Reinitialize All Boats")]
    public void ForceReinitializeAllBoats()
    {
        initializedBoats.Clear();
        InitializeAllBoats();
    }
}